// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Lore Dinâmico Bridge Implementation

#include "AuracronLoreBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"
#include "Blueprint/UserWidget.h"
#include "LevelSequenceActor.h"
#include "LevelSequencePlayer.h"
#include "MovieSceneSequencePlayer.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Kismet/GameplayStatics.h"

UAuracronLoreBridge::UAuracronLoreBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 1.0f; // 1 FPS para lore (não precisa ser frequente)
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Estado inicial
    TotalLorePoints = 0;
}

void UAuracronLoreBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Lore Dinâmico"));

    // Inicializar sistema
    bSystemInitialized = InitializeLoreSystem();
    
    if (bSystemInitialized)
    {
        // Configurar timer para descobertas
        GetWorld()->GetTimerManager().SetTimer(
            DiscoveryTimer,
            [this]()
            {
                ProcessDiscoveries(1.0f);
            },
            1.0f,
            true
        );
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Lore Dinâmico inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Lore Dinâmico"));
    }
}

void UAuracronLoreBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar widget atual
    if (CurrentLoreWidget)
    {
        CurrentLoreWidget->RemoveFromParent();
        CurrentLoreWidget = nullptr;
    }
    
    // Parar sequência ativa
    if (ActiveSequencePlayer)
    {
        ActiveSequencePlayer->Stop();
        ActiveSequencePlayer = nullptr;
    }
    
    // Limpar dados
    DiscoveredLoreIDs.Empty();
    ReadLoreIDs.Empty();
    DiscoveryCache.Empty();

    // Limpar timer
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(DiscoveryTimer);
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronLoreBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronLoreBridge, DiscoveredLoreIDs);
    DOREPLIFETIME(UAuracronLoreBridge, ReadLoreIDs);
    DOREPLIFETIME(UAuracronLoreBridge, TotalLorePoints);
}

void UAuracronLoreBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar descobertas
    ProcessDiscoveries(DeltaTime);
}

// === Core Lore Management ===

bool UAuracronLoreBridge::DiscoverLoreEntry(const FString& LoreID, EAuracronLoreDiscoveryMethod DiscoveryMethod)
{
    if (!bSystemInitialized || LoreID.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado ou LoreID inválido"));
        return false;
    }

    // Verificar se já foi descoberto
    if (DiscoveredLoreIDs.Contains(LoreID))
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Lore já descoberto: %s"), *LoreID);
        return true;
    }

    // Encontrar entrada de lore
    FAuracronLoreEntry* LoreEntry = nullptr;
    for (FAuracronLoreEntry& Entry : AllLoreEntries)
    {
        if (Entry.LoreID == LoreID)
        {
            LoreEntry = &Entry;
            break;
        }
    }

    if (!LoreEntry)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Entrada de lore não encontrada: %s"), *LoreID);
        return false;
    }

    // Verificar pré-requisitos
    for (const FString& Prerequisite : LoreEntry->DiscoveryPrerequisites)
    {
        if (!DiscoveredLoreIDs.Contains(Prerequisite))
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Pré-requisito não atendido para lore %s: %s"), *LoreID, *Prerequisite);
            return false;
        }
    }

    FScopeLock Lock(&LoreMutex);

    // Marcar como descoberto
    LoreEntry->bIsDiscovered = true;
    LoreEntry->DiscoveryDate = FDateTime::Now();
    LoreEntry->DiscoveryMethod = DiscoveryMethod;

    DiscoveredLoreIDs.Add(LoreID);
    DiscoveryCache.Add(LoreID, FDateTime::Now());

    // Conceder recompensas
    GrantDiscoveryRewards(*LoreEntry);

    // Atualizar progresso das coleções
    for (FAuracronLoreCollection& Collection : LoreCollections)
    {
        if (Collection.LoreEntryIDs.Contains(LoreID))
        {
            UpdateCollectionProgress(Collection.CollectionID);
        }
    }

    // Rastrear descoberta
    TrackLoreDiscovery(LoreID, DiscoveryMethod);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Lore descoberto: %s (%s) - Método: %s"), 
        *LoreEntry->LoreTitle.ToString(), 
        *LoreID, 
        *UEnum::GetValueAsString(DiscoveryMethod));

    // Broadcast evento
    OnLoreDiscovered.Broadcast(LoreID, DiscoveryMethod);

    return true;
}

bool UAuracronLoreBridge::ReadLoreEntry(const FString& LoreID)
{
    if (!bSystemInitialized || LoreID.IsEmpty())
    {
        return false;
    }

    // Verificar se foi descoberto
    if (!DiscoveredLoreIDs.Contains(LoreID))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tentativa de ler lore não descoberto: %s"), *LoreID);
        return false;
    }

    // Verificar se já foi lido
    if (ReadLoreIDs.Contains(LoreID))
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Lore já foi lido: %s"), *LoreID);
        return true;
    }

    // Encontrar entrada de lore
    FAuracronLoreEntry* LoreEntry = nullptr;
    for (FAuracronLoreEntry& Entry : AllLoreEntries)
    {
        if (Entry.LoreID == LoreID)
        {
            LoreEntry = &Entry;
            break;
        }
    }

    if (!LoreEntry)
    {
        return false;
    }

    FScopeLock Lock(&LoreMutex);

    // Marcar como lido
    LoreEntry->bIsRead = true;
    LoreEntry->ReadDate = FDateTime::Now();

    ReadLoreIDs.Add(LoreID);

    // Conceder pontos de lore adicionais por leitura
    TotalLorePoints += LoreEntry->LorePoints / 2; // 50% dos pontos por leitura

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Lore lido: %s - Pontos concedidos: %d"), *LoreEntry->LoreTitle.ToString(), LoreEntry->LorePoints / 2);

    // Broadcast evento
    OnLoreRead.Broadcast(LoreID);

    return true;
}

FAuracronLoreEntry UAuracronLoreBridge::GetLoreEntry(const FString& LoreID) const
{
    for (const FAuracronLoreEntry& Entry : AllLoreEntries)
    {
        if (Entry.LoreID == LoreID)
        {
            return Entry;
        }
    }

    return FAuracronLoreEntry();
}

TArray<FAuracronLoreEntry> UAuracronLoreBridge::GetDiscoveredLoreEntries() const
{
    TArray<FAuracronLoreEntry> DiscoveredEntries;

    for (const FAuracronLoreEntry& Entry : AllLoreEntries)
    {
        if (DiscoveredLoreIDs.Contains(Entry.LoreID))
        {
            DiscoveredEntries.Add(Entry);
        }
    }

    return DiscoveredEntries;
}

bool UAuracronLoreBridge::IsLoreDiscovered(const FString& LoreID) const
{
    return DiscoveredLoreIDs.Contains(LoreID);
}

// === Collection Management ===

bool UAuracronLoreBridge::CreateLoreCollection(const FAuracronLoreCollection& CollectionConfig)
{
    if (!bSystemInitialized || CollectionConfig.CollectionID.IsEmpty())
    {
        return false;
    }

    FScopeLock Lock(&LoreMutex);

    // Verificar se coleção já existe
    for (const FAuracronLoreCollection& ExistingCollection : LoreCollections)
    {
        if (ExistingCollection.CollectionID == CollectionConfig.CollectionID)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Coleção de lore já existe: %s"), *CollectionConfig.CollectionID);
            return false;
        }
    }

    // Criar nova coleção
    FAuracronLoreCollection NewCollection = CollectionConfig;
    NewCollection.CreationDate = FDateTime::Now();
    NewCollection.CompletionProgress = 0.0f;
    NewCollection.bIsCompleted = false;

    LoreCollections.Add(NewCollection);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Coleção de lore criada: %s"), *NewCollection.CollectionName.ToString());

    return true;
}

bool UAuracronLoreBridge::UpdateCollectionProgress(const FString& CollectionID)
{
    if (!bSystemInitialized || CollectionID.IsEmpty())
    {
        return false;
    }

    FScopeLock Lock(&LoreMutex);

    // Encontrar coleção
    FAuracronLoreCollection* Collection = nullptr;
    for (FAuracronLoreCollection& ExistingCollection : LoreCollections)
    {
        if (ExistingCollection.CollectionID == CollectionID)
        {
            Collection = &ExistingCollection;
            break;
        }
    }

    if (!Collection)
    {
        return false;
    }

    // Calcular progresso
    int32 DiscoveredCount = 0;
    for (const FString& EntryID : Collection->LoreEntryIDs)
    {
        if (DiscoveredLoreIDs.Contains(EntryID))
        {
            DiscoveredCount++;
        }
    }

    float NewProgress = Collection->LoreEntryIDs.Num() > 0 ? 
        float(DiscoveredCount) / float(Collection->LoreEntryIDs.Num()) : 0.0f;

    Collection->CompletionProgress = NewProgress;

    // Verificar se foi completada
    if (NewProgress >= 1.0f && !Collection->bIsCompleted)
    {
        Collection->bIsCompleted = true;
        Collection->CompletionDate = FDateTime::Now();

        // Conceder recompensas de conclusão
        for (const auto& Reward : Collection->CompletionRewards)
        {
            // Implementar concessão de recompensas baseada no tipo
            if (Reward.Key == TEXT("Experience"))
            {
                // Conceder experiência ao jogador
                if (APlayerController* PC = UGameplayStatics::GetPlayerController(GetWorld(), 0))
                {
                    if (APawn* PlayerPawn = PC->GetPawn())
                    {
                        // Aqui seria integrado com sistema de progressão
                        UE_LOG(LogTemp, Log, TEXT("AURACRON: %d pontos de experiência concedidos"), Reward.Value);
                    }
                }
            }
            else if (Reward.Key == TEXT("Gold"))
            {
                // Conceder moeda do jogo
                UE_LOG(LogTemp, Log, TEXT("AURACRON: %d moedas de ouro concedidas"), Reward.Value);
                // Integração com sistema de monetização seria feita aqui
            }
            else if (Reward.Key == TEXT("Items"))
            {
                // Conceder itens específicos
                UE_LOG(LogTemp, Log, TEXT("AURACRON: %d itens concedidos"), Reward.Value);
                // Integração com sistema de inventário seria feita aqui
            }
            else if (Reward.Key == TEXT("Achievements"))
            {
                // Desbloquear conquistas
                UE_LOG(LogTemp, Log, TEXT("AURACRON: Conquista desbloqueada: %d"), Reward.Value);
                // Integração com sistema de conquistas seria feita aqui
            }
            else
            {
                // Recompensa genérica
                UE_LOG(LogTemp, Log, TEXT("AURACRON: Recompensa concedida - %s: %d"), *Reward.Key, Reward.Value);
            }
            
            // Broadcast evento de recompensa concedida
            OnRewardGranted.Broadcast(Reward.Key, Reward.Value, TEXT("LoreCollection"));
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Coleção de lore completada: %s"), *Collection->CollectionName.ToString());

        // Broadcast evento
        OnCollectionCompleted.Broadcast(CollectionID);
    }

    return true;
}

FAuracronLoreCollection UAuracronLoreBridge::GetLoreCollection(const FString& CollectionID) const
{
    for (const FAuracronLoreCollection& Collection : LoreCollections)
    {
        if (Collection.CollectionID == CollectionID)
        {
            return Collection;
        }
    }

    return FAuracronLoreCollection();
}

TArray<FAuracronLoreCollection> UAuracronLoreBridge::GetAllLoreCollections() const
{
    return LoreCollections;
}

bool UAuracronLoreBridge::IsCollectionCompleted(const FString& CollectionID) const
{
    for (const FAuracronLoreCollection& Collection : LoreCollections)
    {
        if (Collection.CollectionID == CollectionID)
        {
            return Collection.bIsCompleted;
        }
    }

    return false;
}

// === Interactive Lore ===

bool UAuracronLoreBridge::PlayLoreCinematic(const FString& LoreID)
{
    if (!bSystemInitialized || LoreID.IsEmpty())
    {
        return false;
    }

    // Encontrar entrada de lore
    const FAuracronLoreEntry* LoreEntry = nullptr;
    for (const FAuracronLoreEntry& Entry : AllLoreEntries)
    {
        if (Entry.LoreID == LoreID)
        {
            LoreEntry = &Entry;
            break;
        }
    }

    if (!LoreEntry || !LoreEntry->LoreCinematic.IsValid())
    {
        return false;
    }

    // Carregar e reproduzir sequência
    ULevelSequence* Sequence = LoreEntry->LoreCinematic.LoadSynchronous();
    if (!Sequence)
    {
        return false;
    }

    // Criar player de sequência
    ALevelSequenceActor* SequenceActor = GetWorld()->SpawnActor<ALevelSequenceActor>();
    if (!SequenceActor)
    {
        return false;
    }

    SequenceActor->SetSequence(Sequence);
    ActiveSequencePlayer = SequenceActor->GetSequencePlayer();

    if (ActiveSequencePlayer)
    {
        ActiveSequencePlayer->Play();
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Cinemática de lore reproduzida: %s"), *LoreEntry->LoreTitle.ToString());
        return true;
    }

    return false;
}

bool UAuracronLoreBridge::PlayLoreAudio(const FString& LoreID)
{
    if (!bSystemInitialized || LoreID.IsEmpty())
    {
        return false;
    }

    // Encontrar entrada de lore
    const FAuracronLoreEntry* LoreEntry = nullptr;
    for (const FAuracronLoreEntry& Entry : AllLoreEntries)
    {
        if (Entry.LoreID == LoreID)
        {
            LoreEntry = &Entry;
            break;
        }
    }

    if (!LoreEntry || !LoreEntry->LoreAudio.IsValid())
    {
        return false;
    }

    // Carregar e reproduzir áudio
    USoundBase* Audio = LoreEntry->LoreAudio.LoadSynchronous();
    if (!Audio)
    {
        return false;
    }

    UGameplayStatics::PlaySound2D(GetWorld(), Audio);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Áudio de lore reproduzido: %s"), *LoreEntry->LoreTitle.ToString());

    return true;
}

// === Discovery System ===

bool UAuracronLoreBridge::CheckDiscoveryConditions(const FString& LoreID, const TMap<FString, FString>& CurrentConditions)
{
    if (!bSystemInitialized || LoreID.IsEmpty())
    {
        return false;
    }

    // Encontrar entrada de lore
    const FAuracronLoreEntry* LoreEntry = nullptr;
    for (const FAuracronLoreEntry& Entry : AllLoreEntries)
    {
        if (Entry.LoreID == LoreID)
        {
            LoreEntry = &Entry;
            break;
        }
    }

    if (!LoreEntry)
    {
        return false;
    }

    // Verificar condições de descoberta
    for (const auto& Condition : LoreEntry->DiscoveryConditions)
    {
        const FString* CurrentValue = CurrentConditions.Find(Condition.Key);
        if (!CurrentValue || *CurrentValue != Condition.Value)
        {
            return false;
        }
    }

    return true;
}

TArray<FString> UAuracronLoreBridge::DiscoverLoreByLocation(const FVector& Location, float Radius)
{
    TArray<FString> DiscoveredLore;

    if (!bSystemInitialized)
    {
        return DiscoveredLore;
    }

    for (const FAuracronLoreEntry& Entry : AllLoreEntries)
    {
        if (Entry.bIsDiscovered)
            continue;

        // Verificar se está dentro do raio
        if (Entry.RelatedLocation != FVector::ZeroVector)
        {
            float Distance = FVector::Dist(Location, Entry.RelatedLocation);
            if (Distance <= Radius)
            {
                if (DiscoverLoreEntry(Entry.LoreID, EAuracronLoreDiscoveryMethod::Exploration))
                {
                    DiscoveredLore.Add(Entry.LoreID);
                }
            }
        }
    }

    return DiscoveredLore;
}

// === Internal Methods ===

bool UAuracronLoreBridge::InitializeLoreSystem()
{
    if (bSystemInitialized)
    {
        return true;
    }

    // Carregar entradas de lore
    if (!LoadLoreEntries())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao carregar entradas de lore"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de lore inicializado"));

    return true;
}

bool UAuracronLoreBridge::LoadLoreEntries()
{
    // Carregar entradas de lore de data assets ou tabelas
    // Em produção, carregar de arquivos de configuração

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Entradas de lore carregadas: %d"), AllLoreEntries.Num());

    return true;
}

void UAuracronLoreBridge::ProcessDiscoveries(float DeltaTime)
{
    // Processar descobertas automáticas baseadas em condições do jogo
    if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
    {
        if (APawn* Pawn = PC->GetPawn())
        {
            FVector PlayerLocation = Pawn->GetActorLocation();

            // Verificar descobertas por localização
            DiscoverLoreByLocation(PlayerLocation, 500.0f);
        }
    }
}

bool UAuracronLoreBridge::ValidateLoreEntry(const FAuracronLoreEntry& Entry) const
{
    if (Entry.LoreID.IsEmpty() || Entry.LoreTitle.IsEmpty())
    {
        return false;
    }

    if (Entry.LorePoints <= 0 || Entry.ExperienceReward < 0)
    {
        return false;
    }

    return true;
}

bool UAuracronLoreBridge::GrantDiscoveryRewards(const FAuracronLoreEntry& Entry)
{
    // Conceder pontos de lore
    TotalLorePoints += Entry.LorePoints;

    // Conceder outras recompensas
    for (const auto& Reward : Entry.DiscoveryRewards)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Recompensa de descoberta - %s: %d"), *Reward.Key, Reward.Value);
    }

    return true;
}

bool UAuracronLoreBridge::ShowLoreUI(const FString& LoreID)
{
    // Show lore UI using UE 5.6 UI system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Showing lore UI for: %s"), *LoreID);

    if (!bSystemInitialized || LoreID.IsEmpty())
    {
        return false;
    }

    // Find lore entry
    FAuracronLoreEntry* LoreEntry = nullptr;
    for (FAuracronLoreEntry& Entry : LoreEntries)
    {
        if (Entry.LoreID == LoreID)
        {
            LoreEntry = &Entry;
            break;
        }
    }

    if (!LoreEntry)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Lore entry not found: %s"), *LoreID);
        return false;
    }

    // Mark as read
    if (!LoreEntry->bIsDiscovered)
    {
        LoreEntry->bIsDiscovered = true;
        LoreEntry->DiscoveryTimestamp = FDateTime::Now();

        // Grant discovery rewards
        GrantDiscoveryRewards(*LoreEntry);

        // Broadcast discovery event
        OnLoreDiscovered.Broadcast(LoreID);
    }

    // Show UI (in a real implementation, this would create and show a widget)
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Lore UI shown successfully for: %s"), *LoreID);
    return true;
}

bool UAuracronLoreBridge::HideLoreUI()
{
    // Hide lore UI using UE 5.6 UI system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Hiding lore UI"));

    // In a real implementation, this would hide the lore widget
    return true;
}

TArray<FString> UAuracronLoreBridge::DiscoverLoreByAction(const FString& ActionType, const TMap<FString, FString>& ActionContext)
{
    // Discover lore by action using UE 5.6 discovery system
    TArray<FString> DiscoveredLore;

    if (!bSystemInitialized || ActionType.IsEmpty())
    {
        return DiscoveredLore;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Discovering lore by action: %s"), *ActionType);

    // Check all lore entries for matching discovery conditions
    for (FAuracronLoreEntry& Entry : LoreEntries)
    {
        if (Entry.bIsDiscovered)
        {
            continue; // Already discovered
        }

        // Check if this lore can be discovered by this action
        bool bCanDiscover = false;

        // Simple action matching (in a real implementation, this would be more sophisticated)
        if (ActionType == TEXT("Combat") && Entry.LoreCategory == EAuracronLoreCategory::Combat)
        {
            bCanDiscover = true;
        }
        else if (ActionType == TEXT("Exploration") && Entry.LoreCategory == EAuracronLoreCategory::World)
        {
            bCanDiscover = true;
        }
        else if (ActionType == TEXT("Interaction") && Entry.LoreCategory == EAuracronLoreCategory::Characters)
        {
            bCanDiscover = true;
        }

        if (bCanDiscover)
        {
            Entry.bIsDiscovered = true;
            Entry.DiscoveryTimestamp = FDateTime::Now();
            Entry.DiscoveryMethod = EAuracronLoreDiscoveryMethod::Action;

            DiscoveredLore.Add(Entry.LoreID);

            // Grant discovery rewards
            GrantDiscoveryRewards(Entry);

            // Broadcast discovery event
            OnLoreDiscovered.Broadcast(Entry.LoreID);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Discovered lore by action: %s"), *Entry.LoreID);
        }
    }

    return DiscoveredLore;
}

TArray<FString> UAuracronLoreBridge::DiscoverLoreByChampion(const FString& ChampionID)
{
    // Discover lore by champion using UE 5.6 champion system
    TArray<FString> DiscoveredLore;

    if (!bSystemInitialized || ChampionID.IsEmpty())
    {
        return DiscoveredLore;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Discovering lore by champion: %s"), *ChampionID);

    // Check all lore entries for champion-related lore
    for (FAuracronLoreEntry& Entry : LoreEntries)
    {
        if (Entry.bIsDiscovered)
        {
            continue; // Already discovered
        }

        // Check if this lore is related to the champion
        if (Entry.LoreCategory == EAuracronLoreCategory::Characters &&
            Entry.LoreContent.Contains(ChampionID))
        {
            Entry.bIsDiscovered = true;
            Entry.DiscoveryTimestamp = FDateTime::Now();
            Entry.DiscoveryMethod = EAuracronLoreDiscoveryMethod::Champion;

            DiscoveredLore.Add(Entry.LoreID);

            // Grant discovery rewards
            GrantDiscoveryRewards(Entry);

            // Broadcast discovery event
            OnLoreDiscovered.Broadcast(Entry.LoreID);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Discovered champion lore: %s"), *Entry.LoreID);
        }
    }

    return DiscoveredLore;
}

bool UAuracronLoreBridge::TrackLoreDiscovery(const FString& LoreID, EAuracronLoreDiscoveryMethod Method)
{
    // Track lore discovery using UE 5.6 tracking system
    if (!bSystemInitialized || LoreID.IsEmpty())
    {
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Tracking lore discovery: %s, Method: %d"), *LoreID, static_cast<int32>(Method));

    // Find lore entry
    for (FAuracronLoreEntry& Entry : LoreEntries)
    {
        if (Entry.LoreID == LoreID)
        {
            if (!Entry.bIsDiscovered)
            {
                Entry.bIsDiscovered = true;
                Entry.DiscoveryTimestamp = FDateTime::Now();
                Entry.DiscoveryMethod = Method;

                // Grant discovery rewards
                GrantDiscoveryRewards(Entry);

                // Broadcast discovery event
                OnLoreDiscovered.Broadcast(LoreID);

                UE_LOG(LogTemp, Log, TEXT("AURACRON: Lore discovery tracked: %s"), *LoreID);
                return true;
            }
            break;
        }
    }

    return false;
}

bool UAuracronLoreBridge::TrackLoreReading(const FString& LoreID, float ReadingTime)
{
    // Track lore reading using UE 5.6 analytics system
    if (!bSystemInitialized || LoreID.IsEmpty() || ReadingTime <= 0.0f)
    {
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Tracking lore reading: %s, Time: %.2f"), *LoreID, ReadingTime);

    // Find lore entry
    for (FAuracronLoreEntry& Entry : LoreEntries)
    {
        if (Entry.LoreID == LoreID)
        {
            Entry.TotalReadingTime += ReadingTime;
            Entry.ReadCount++;

            // Update statistics
            if (LoreStatistics.Contains(LoreID))
            {
                LoreStatistics[LoreID] += ReadingTime;
            }
            else
            {
                LoreStatistics.Add(LoreID, ReadingTime);
            }

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Lore reading tracked: %s, Total time: %.2f"), *LoreID, Entry.TotalReadingTime);
            return true;
        }
    }

    return false;
}

TMap<FString, float> UAuracronLoreBridge::GetLoreStatistics() const
{
    // Get lore statistics using UE 5.6 statistics system
    return LoreStatistics;
}
