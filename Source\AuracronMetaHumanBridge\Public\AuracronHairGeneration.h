#pragma once

#include "CoreMinimal.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Array.h"
#include "Math/Vector.h"
#include "Math/Transform.h"
#include "Math/Color.h"
#include "Engine/SkeletalMesh.h"
#include "Components/SkeletalMeshComponent.h"
#include "GroomAsset.h"
#include "GroomBindingAsset.h"
#include "GroomComponent.h"
#include "HairStrandsDatas.h"
#include "HairStrandsCore.h"
#include "HairStrandsInterface.h"
#include "HairCardsBuilder.h"
#include "HairCardsDatas.h"
#include "GroomCreateBindingOptions.h"
#include "GroomBindingBuilder.h"
#include "NiagaraSystem.h"
#include "NiagaraComponent.h"

#include "AuracronHairGeneration.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronHairGeneration, Log, All);

// Enums for hair generation
UENUM(BlueprintType)
enum class EHairType : uint8
{
    Straight        UMETA(DisplayName = "Straight"),
    Wavy            UMETA(DisplayName = "Wavy"),
    Curly           UMETA(DisplayName = "Curly"),
    Coily           UMETA(DisplayName = "Coily"),
    Kinky           UMETA(DisplayName = "Kinky")
};

UENUM(BlueprintType)
enum class EHairLength : uint8
{
    VeryShort       UMETA(DisplayName = "Very Short"),
    Short           UMETA(DisplayName = "Short"),
    Medium          UMETA(DisplayName = "Medium"),
    Long            UMETA(DisplayName = "Long"),
    VeryLong        UMETA(DisplayName = "Very Long")
};

UENUM(BlueprintType)
enum class EHairDensity : uint8
{
    Sparse          UMETA(DisplayName = "Sparse"),
    Low             UMETA(DisplayName = "Low"),
    Medium          UMETA(DisplayName = "Medium"),
    High            UMETA(DisplayName = "High"),
    VeryHigh        UMETA(DisplayName = "Very High")
};

UENUM(BlueprintType)
enum class EHairRenderingMode : uint8
{
    Strands         UMETA(DisplayName = "Hair Strands"),
    Cards           UMETA(DisplayName = "Hair Cards"),
    Meshes          UMETA(DisplayName = "Hair Meshes"),
    Hybrid          UMETA(DisplayName = "Hybrid")
};

UENUM(BlueprintType)
enum class EHairStrandType : uint8
{
    Strands         UMETA(DisplayName = "Hair Strands"),
    Cards           UMETA(DisplayName = "Hair Cards"),
    Hybrid          UMETA(DisplayName = "Hybrid")
};

UENUM(BlueprintType)
enum class EAuracronHairCardQuality : uint8
{
    Low             UMETA(DisplayName = "Low Quality"),
    Medium          UMETA(DisplayName = "Medium Quality"),
    High            UMETA(DisplayName = "High Quality"),
    Ultra           UMETA(DisplayName = "Ultra Quality")
};

UENUM(BlueprintType)
enum class EHairPhysicsType : uint8
{
    None            UMETA(DisplayName = "None"),
    Simple          UMETA(DisplayName = "Simple"),
    Advanced        UMETA(DisplayName = "Advanced"),
    Niagara         UMETA(DisplayName = "Niagara Physics")
};

// Structures for hair generation
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FHairStrandData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Strand")
    int32 StrandCount = 10000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Strand")
    int32 PointsPerStrand = 16;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Strand")
    float StrandLength = 10.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Strand")
    float StrandWidth = 0.02f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Strand")
    float RootRadius = 0.02f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Strand")
    float TipRadius = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Strand")
    float RootWidth = 0.03f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Strand")
    float TipWidth = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Strand")
    EHairType HairType = EHairType::Straight;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Strand")
    EHairLength HairLength = EHairLength::Medium;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Strand")
    EHairDensity HairDensity = EHairDensity::Medium;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FHairStylingData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Styling")
    float CurlIntensity = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Styling")
    float CurlFrequency = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Styling")
    float WaveIntensity = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Styling")
    float WaveFrequency = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Styling")
    FVector GravityDirection = FVector(0.0f, 0.0f, -1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Styling")
    float GravityStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Styling")
    float WindStrength = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Styling")
    FVector WindDirection = FVector(1.0f, 0.0f, 0.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Styling")
    float NoiseIntensity = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Styling")
    bool bUseProceduralStyling = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Styling")
    float NoiseFrequency = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Styling")
    float LengthVariation = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Styling")
    FVector DirectionVector = FVector::ZeroVector;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FHairColorData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Color")
    FLinearColor RootColor = FLinearColor(0.1f, 0.05f, 0.02f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Color")
    FLinearColor TipColor = FLinearColor(0.2f, 0.1f, 0.05f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Color")
    float ColorVariation = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Color")
    float Roughness = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Color")
    float Metallic = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Color")
    float Specular = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Color")
    float Transmission = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Color")
    float Scattering = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Color")
    FLinearColor BaseColor = FLinearColor(0.1f, 0.05f, 0.02f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Color")
    float SpecularIntensity = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Color")
    float VariationIntensity = 0.1f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FHairPhysicsData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Physics")
    EHairPhysicsType PhysicsType = EHairPhysicsType::Simple;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Physics")
    float Stiffness = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Physics")
    float Damping = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Physics")
    float Friction = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Physics")
    float CollisionRadius = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Physics")
    int32 SolverIterations = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Physics")
    float SubSteps = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Physics")
    bool bEnableCollision = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Physics")
    bool bEnableSelfCollision = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Physics")
    FVector Gravity = FVector(0.0f, 0.0f, -980.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Physics")
    float AirResistance = 0.1f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FHairCardData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Cards")
    int32 CardCount = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Cards")
    FVector2D CardSize = FVector2D(1.0f, 10.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Cards")
    int32 SegmentsPerCard = 8;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Cards")
    float CardWidth = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Cards")
    bool bGenerateAtlas = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Cards")
    FIntPoint AtlasResolution = FIntPoint(1024, 1024);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Cards")
    float CardLength = 10.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Cards")
    FIntPoint TextureResolution = FIntPoint(512, 512);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Cards")
    int32 Quality = 1;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FHairLODData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair LOD")
    int32 NumLODs = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair LOD")
    TArray<float> LODDistances = {500.0f, 1000.0f, 2000.0f, 4000.0f};

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair LOD")
    TArray<float> StrandReductions = {1.0f, 0.5f, 0.25f, 0.1f};

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair LOD")
    TArray<EHairRenderingMode> RenderingModes = {EHairRenderingMode::Strands, EHairRenderingMode::Strands, EHairRenderingMode::Cards, EHairRenderingMode::Cards};

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair LOD")
    bool bEnableScreenSizeBasedLOD = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair LOD")
    float ScreenSizeThreshold = 0.1f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FHairNoiseParameters
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Noise")
    float NoiseScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Noise")
    float NoiseIntensity = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Noise")
    int32 NoiseOctaves = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Noise")
    float NoiseLacunarity = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Noise")
    float NoisePersistence = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Noise")
    int32 NoiseSeed = 0;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FHairGenerationParameters
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    FHairStrandData StrandData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    FHairStylingData StylingData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    FHairColorData ColorData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    FHairPhysicsData PhysicsData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    FHairCardData CardData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    FHairLODData LODData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    FHairNoiseParameters NoiseParams;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    EHairRenderingMode RenderingMode = EHairRenderingMode::Strands;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    bool bGenerateBindingAsset = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    int32 Seed = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    bool bGenerateHairCards = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    EHairStrandType StrandType = EHairStrandType::Strands;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    bool bGeneratePhysics = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    bool bGenerateLODs = false;
};

/**
 * Hair generation system for MetaHuman Bridge
 * Provides advanced hair generation capabilities with UE5.6 Groom and Niagara APIs
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronHairGeneration
{
public:
    FAuracronHairGeneration();
    ~FAuracronHairGeneration();

    // Core hair generation
    UGroomAsset* GenerateProceduralHair(const FHairGenerationParameters& Parameters);
    bool GenerateHairStrands(UGroomAsset* GroomAsset, const FHairStrandData& StrandData, const FHairStylingData& StylingData);
    bool GenerateHairCardsFromStrands(UGroomAsset* GroomAsset, const FHairCardData& CardData);

    // Hair styling and physics
    bool ApplyHairStyling(UGroomAsset* GroomAsset, const FHairStylingData& StylingData);
    bool SetupHairPhysics(UGroomAsset* GroomAsset, const FHairPhysicsData& PhysicsData);
    bool SetupNiagaraHairPhysics(UGroomAsset* GroomAsset, const FHairPhysicsData& PhysicsData);

    // Hair appearance
    bool GenerateHairColorVariation(UGroomAsset* GroomAsset, const FHairColorData& ColorData);
    bool ApplyProceduralStyling(UGroomAsset* GroomAsset, const FHairStylingData& StylingData);

    // Hair cards and meshes
    bool CreateHairCards(UGroomAsset* GroomAsset, const FHairCardData& CardData);
    bool CreateHairLODLevels(UGroomAsset* GroomAsset, const FHairLODData& LODData);

    // Hair optimization
    bool OptimizeHairLOD(UGroomAsset* GroomAsset, const FHairLODData& LODData);
    bool OptimizeHairPerformance(UGroomAsset* GroomAsset);

    // Hair binding
    bool CreateHairBindingData(UGroomBindingAsset* BindingAsset, UGroomAsset* GroomAsset, USkeletalMesh* SkeletalMesh);

    // Hair validation and export
    bool ValidateGroomAsset(UGroomAsset* GroomAsset, FString& OutErrorMessage);
    bool ValidateHairGenerationParameters(const FHairGenerationParameters& Parameters, FString& OutErrorMessage);
    FString GetHairGenerationStats(UGroomAsset* GroomAsset);
    bool ExportHairToAlembic(UGroomAsset* GroomAsset, const FString& ExportFilePath);

    // Hair presets
    FHairGenerationParameters CreateHairPreset(UGroomAsset* GroomAsset, const FString& PresetName);
    bool ApplyHairPreset(UGroomAsset* GroomAsset, const FHairGenerationParameters& PresetParameters);

    // Thread safety
    mutable FCriticalSection HairGenerationMutex;

private:
    // Hair generation cache
    mutable TMap<FString, TWeakObjectPtr<UGroomAsset>> HairAssetCache;
    mutable TMap<FString, FString> HairGenerationStats;
    mutable TMap<FString, TWeakObjectPtr<UMaterialInterface>> HairMaterialCache;

    // Internal hair generation methods
    bool CreateHairStrandGeometry(UGroomAsset* GroomAsset, const FHairStrandData& StrandData);
    FVector ApplyHairNoise(const FVector& BasePosition, const FHairNoiseParameters& NoiseParams, int32 StrandIndex);
    FTransform CalculateHairCurlTransform(float CurlIntensity, float CurlFrequency, float SegmentRatio);
    bool GenerateStrandPointsForUE56(const FHairStrandData& StrandData, TArray<FHairStrandsPoints>& OutStrandPoints);

    // Hair transformation methods for UE 5.6
    void ApplyCurlTransformation(FHairStrandsDatas& HairData, const FHairStylingData& StylingData);
    void ApplyWaveTransformation(FHairStrandsDatas& HairData, const FHairStylingData& StylingData);
    void ApplyLengthVariation(FHairStrandsDatas& HairData, const FHairStylingData& StylingData);
    void ApplyDirectionalStyling(FHairStrandsDatas& HairData, const FHairStylingData& StylingData);

    // Texture generation methods for UE 5.6
    UTexture2D* GenerateHairTextureAtlas(UGroomAsset* GroomAsset, const FIntPoint& TextureResolution, EAuracronHairCardQuality Quality);
    UTexture2D* GenerateHairColorTexture(const FHairColorData& ColorData, const FIntPoint& TextureSize);
    UMaterialInstanceDynamic* CreateHairMaterialInstance(UGroomAsset* GroomAsset, const FHairColorData& ColorData, UMaterialInterface* BaseMaterial = nullptr);
    UMaterialInterface* GetDefaultHairMaterial();

    // Hair generation utilities
    FString CalculateHairGenerationHash(const FHairGenerationParameters& Parameters);
    void UpdateHairGenerationStats(const FString& OperationName, double ExecutionTime, bool bSuccess);
    void CleanupHairAssetCache();

    // Prevent copying
    FAuracronHairGeneration(const FAuracronHairGeneration&) = delete;
    FAuracronHairGeneration& operator=(const FAuracronHairGeneration&) = delete;
};

// Hash function for FHairColorData to enable use in TMap and other hash-based containers
FORCEINLINE uint32 GetTypeHash(const FHairColorData& ColorData)
{
    uint32 Hash = 0;
    Hash = HashCombine(Hash, GetTypeHash(ColorData.RootColor));
    Hash = HashCombine(Hash, GetTypeHash(ColorData.TipColor));
    Hash = HashCombine(Hash, GetTypeHash(ColorData.ColorVariation));
    Hash = HashCombine(Hash, GetTypeHash(ColorData.Roughness));
    Hash = HashCombine(Hash, GetTypeHash(ColorData.Metallic));
    Hash = HashCombine(Hash, GetTypeHash(ColorData.Specular));
    Hash = HashCombine(Hash, GetTypeHash(ColorData.Transmission));
    Hash = HashCombine(Hash, GetTypeHash(ColorData.Scattering));
    Hash = HashCombine(Hash, GetTypeHash(ColorData.BaseColor));
    Hash = HashCombine(Hash, GetTypeHash(ColorData.SpecularIntensity));
    Hash = HashCombine(Hash, GetTypeHash(ColorData.VariationIntensity));
    return Hash;
}
