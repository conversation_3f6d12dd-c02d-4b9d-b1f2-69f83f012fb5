// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de FÃ­sica Chaos Bridge Build Configuration
using UnrealBuildTool;
public class AuracronPhysicsBridge : ModuleRules
{
    public AuracronPhysicsBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "PhysicsCore",
                "ChaosCore",
                "ChaosSolverEngine",
                "ChaosVehicles",
                "ChaosCloth",



                "GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "ModularGameplay",
                "NetCore",
                "ReplicationGraph",
                "EnhancedInput",
                "InputCore",
                "UMG",
                "Slate",
                "SlateCore","DeveloperSettings",
                "EngineSettings"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "RenderCore",
                "RHI",
                "Json",
                "AudioMixer",
                "SignalProcessing",

                "ClothingSystemRuntimeInterface",
                "ClothingSystemRuntimeCommon",
                "ClothingSystemRuntimeNv",
                "Landscape",
                "Foliage",
                "Water",
                "NiagaraCore",
                "NiagaraShader",
                "Chaos",
                "ChaosVehiclesCore",
                "GeometryCollectionEngine",
                "FieldSystemEngine",

                "Analytics",
                "AnalyticsET",
                "HTTP",
                "Sockets",
                "Networking",

                "TraceLog",
                "ApplicationCore"
            }
        );

        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "ToolMenus",
                    "EditorStyle",
                    "EditorWidgets",
                    "UnrealEd",
                    "PropertyEditor",
                    "KismetCompiler",
                    "BlueprintGraph",
                    "Kismet"
                }
            );
        }
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {}
        );
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_CHAOS=1");
        PublicDefinitions.Add("WITH_CHAOS_PHYSICS=1");
        PublicDefinitions.Add("WITH_FIELD_SYSTEM=1");
        PublicDefinitions.Add("WITH_GEOMETRY_COLLECTION=1");
        PublicDefinitions.Add("WITH_DESTRUCTION=1");
        PublicDefinitions.Add("WITH_CLOTH_SIMULATION=1");
        PublicDefinitions.Add("WITH_FLUID_SIMULATION=1");
        PublicDefinitions.Add("WITH_SOFT_BODY=1");
        PublicDefinitions.Add("WITH_RIGID_BODY=1");
        PublicDefinitions.Add("WITH_ADVANCED_CONSTRAINTS=1");
        PublicDefinitions.Add("WITH_VEHICLE_PHYSICS=1");
        PublicDefinitions.Add("WITH_NETWORK_PHYSICS=1");
        PublicDefinitions.Add("WITH_MATERIAL_PHYSICS=1");
        PublicDefinitions.Add("WITH_PHYSICS_ANALYTICS=1");
        PublicDefinitions.Add("WITH_MACHINE_LEARNING_CLOTH=1");
        // Physics features
        PublicDefinitions.Add("AURACRON_CHAOS_DESTRUCTION=1");
        PublicDefinitions.Add("AURACRON_FIELD_SYSTEM=1");
        PublicDefinitions.Add("AURACRON_GEOMETRY_COLLECTION=1");
        PublicDefinitions.Add("AURACRON_DYNAMIC_PHYSICS=1");
        PublicDefinitions.Add("AURACRON_REALM_PHYSICS=1");
        PublicDefinitions.Add("AURACRON_ABILITY_PHYSICS=1");
        PublicDefinitions.Add("AURACRON_ENVIRONMENTAL_PHYSICS=1");
        PublicDefinitions.Add("AURACRON_INTERACTIVE_DESTRUCTION=1");
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_PHYSICS=1");
            PublicDefinitions.Add("AURACRON_SIMPLIFIED_PHYSICS=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_PHYSICS=0");
            PublicDefinitions.Add("AURACRON_SIMPLIFIED_PHYSICS=0");
        }
        // Development vs Shipping
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_PHYSICS_DEBUG=1");
            PublicDefinitions.Add("AURACRON_PHYSICS_PROFILING=1");
            PublicDefinitions.Add("AURACRON_CHAOS_DEBUG=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_PHYSICS_DEBUG=0");
            PublicDefinitions.Add("AURACRON_PHYSICS_PROFILING=0");
            PublicDefinitions.Add("AURACRON_CHAOS_DEBUG=0");
        }
        // Performance optimizations
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("AURACRON_OPTIMIZE_PHYSICS=1");
            PublicDefinitions.Add("AURACRON_PHYSICS_CULLING=1");
            PublicDefinitions.Add("AURACRON_ASYNC_PHYSICS=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_OPTIMIZE_PHYSICS=0");
            PublicDefinitions.Add("AURACRON_PHYSICS_CULLING=0");
            PublicDefinitions.Add("AURACRON_ASYNC_PHYSICS=0");
        }
    }
}

