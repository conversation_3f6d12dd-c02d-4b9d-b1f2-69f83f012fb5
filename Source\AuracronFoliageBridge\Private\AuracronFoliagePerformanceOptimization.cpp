// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Performance Optimization Implementation
// Bridge 4.12: Foliage - Performance Optimization

#include "AuracronFoliagePerformanceOptimization.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageInstanced.h"
#include "AuracronFoliageLOD.h"
#include "AuracronFoliageStreaming.h"
#include "AuracronFoliageBridge.h"

// UE5.6 Performance includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Engine/World.h"
#include "RenderingThread.h"
#include "RHI.h"
#include "RHICommandList.h"

// Culling includes
#include "Engine/Engine.h"
#include "SceneView.h"
#include "ConvexVolume.h"
#include "SceneManagement.h"

// GPU includes
#include "GlobalShader.h"
#include "ShaderParameterStruct.h"
#include "RenderGraphBuilder.h"
#include "RenderGraphUtils.h"

// Performance monitoring
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "DrawDebugHelpers.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"

// =============================================================================
// FOLIAGE PERFORMANCE OPTIMIZATION MANAGER IMPLEMENTATION
// =============================================================================

UAuracronFoliagePerformanceOptimizationManager* UAuracronFoliagePerformanceOptimizationManager::Instance = nullptr;

UAuracronFoliagePerformanceOptimizationManager* UAuracronFoliagePerformanceOptimizationManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronFoliagePerformanceOptimizationManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronFoliagePerformanceOptimizationManager::Initialize(const FAuracronFoliagePerformanceOptimizationConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Performance Optimization Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize performance data
    PerformanceData = FAuracronOverallPerformanceData();
    CullingData = FAuracronCullingPerformanceData();
    BatchingData = FAuracronBatchingPerformanceData();
    GPUData = FAuracronGPUPerformanceData();

    // Initialize timers
    LastCullingUpdate = 0.0f;
    LastBatchingUpdate = 0.0f;
    LastPerformanceUpdate = 0.0f;
    LastAdaptiveUpdate = 0.0f;

    // Initialize frame tracking
    FrameTimeHistory.Empty();
    FrameTimeHistory.Reserve(60); // Store 1 second of frame times at 60 FPS
    AverageFPS = Configuration.TargetFrameRate;
    FrameCounter = 0;

    // Get world reference
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        ManagedWorld = World;
    }

    bIsInitialized = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Performance Optimization Manager initialized with tier: %s, target FPS: %.1f"), 
                              *UEnum::GetValueAsString(Configuration.PerformanceTier),
                              Configuration.TargetFrameRate);
}

void UAuracronFoliagePerformanceOptimizationManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Clear frame history
    FrameTimeHistory.Empty();

    // Reset references
    ManagedWorld.Reset();
    LODManager.Reset();
    StreamingManager.Reset();
    InstancedManager.Reset();

    bIsInitialized = false;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Performance Optimization Manager shutdown completed"));
}

bool UAuracronFoliagePerformanceOptimizationManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronFoliagePerformanceOptimizationManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        return;
    }

    TRACE_CPUPROFILER_EVENT_SCOPE(AuracronFoliagePerformanceOptimization_Tick);

    // Update frame time tracking
    FrameTimeHistory.Add(DeltaTime * 1000.0f); // Convert to milliseconds
    if (FrameTimeHistory.Num() > 60)
    {
        FrameTimeHistory.RemoveAt(0);
    }
    FrameCounter++;

    // Update culling
    if (Configuration.bEnableAdvancedCulling)
    {
        LastCullingUpdate += DeltaTime;
        if (LastCullingUpdate >= Configuration.CullingUpdateInterval)
        {
            UpdateCullingInternal(DeltaTime);
            LastCullingUpdate = 0.0f;
        }
    }

    // Update batching
    if (Configuration.bEnableBatchingOptimization)
    {
        LastBatchingUpdate += DeltaTime;
        if (LastBatchingUpdate >= Configuration.BatchingUpdateInterval)
        {
            UpdateBatchingInternal(DeltaTime);
            LastBatchingUpdate = 0.0f;
        }
    }

    // Update GPU performance
    if (Configuration.bEnableGPUInstancing)
    {
        UpdateGPUPerformanceInternal(DeltaTime);
    }

    // Update adaptive performance
    if (Configuration.bEnableAdaptivePerformance)
    {
        LastAdaptiveUpdate += DeltaTime;
        if (LastAdaptiveUpdate >= 1.0f) // Update every second
        {
            float CurrentFPS = CalculateFrameTime() > 0.0f ? 1000.0f / CalculateFrameTime() : 0.0f;
            UpdateAdaptivePerformanceInternal(CurrentFPS);
            LastAdaptiveUpdate = 0.0f;
        }
    }

    // Update performance monitoring
    LastPerformanceUpdate += DeltaTime;
    if (LastPerformanceUpdate >= Configuration.PerformanceMonitoringInterval)
    {
        UpdatePerformanceMetrics();
        LastPerformanceUpdate = 0.0f;
    }
}

void UAuracronFoliagePerformanceOptimizationManager::SetConfiguration(const FAuracronFoliagePerformanceOptimizationConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Performance Optimization configuration updated"));
}

FAuracronFoliagePerformanceOptimizationConfiguration UAuracronFoliagePerformanceOptimizationManager::GetConfiguration() const
{
    return Configuration;
}

void UAuracronFoliagePerformanceOptimizationManager::EnableFrustumCulling(bool bEnabled)
{
    Configuration.bEnableFrustumCulling = bEnabled;
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Frustum culling %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliagePerformanceOptimizationManager::UpdateFrustumCulling(const FVector& CameraLocation, const FVector& CameraDirection, float FOV)
{
    if (!Configuration.bEnableFrustumCulling)
    {
        return;
    }

    TRACE_CPUPROFILER_EVENT_SCOPE(AuracronFoliagePerformanceOptimization_FrustumCulling);

    double StartTime = FPlatformTime::Seconds();
    
    bool bCullingPerformed = PerformFrustumCulling(CameraLocation, CameraDirection, FOV);
    
    double EndTime = FPlatformTime::Seconds();
    CullingData.FrustumCullingTimeMs = (EndTime - StartTime) * 1000.0f;

    if (bCullingPerformed)
    {
        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Frustum culling completed in %.2f ms"), CullingData.FrustumCullingTimeMs);
    }
}

void UAuracronFoliagePerformanceOptimizationManager::EnableOcclusionCulling(bool bEnabled)
{
    Configuration.bEnableOcclusionCulling = bEnabled;
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Occlusion culling %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliagePerformanceOptimizationManager::UpdateOcclusionCulling(const FVector& CameraLocation)
{
    if (!Configuration.bEnableOcclusionCulling)
    {
        return;
    }

    TRACE_CPUPROFILER_EVENT_SCOPE(AuracronFoliagePerformanceOptimization_OcclusionCulling);

    double StartTime = FPlatformTime::Seconds();
    
    bool bCullingPerformed = PerformOcclusionCulling(CameraLocation);
    
    double EndTime = FPlatformTime::Seconds();
    CullingData.OcclusionCullingTimeMs = (EndTime - StartTime) * 1000.0f;

    if (bCullingPerformed)
    {
        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Occlusion culling completed in %.2f ms"), CullingData.OcclusionCullingTimeMs);
    }
}

void UAuracronFoliagePerformanceOptimizationManager::EnableBatchingOptimization(bool bEnabled)
{
    Configuration.bEnableBatchingOptimization = bEnabled;
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Batching optimization %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliagePerformanceOptimizationManager::OptimizeBatches()
{
    if (!Configuration.bEnableBatchingOptimization)
    {
        return;
    }

    TRACE_CPUPROFILER_EVENT_SCOPE(AuracronFoliagePerformanceOptimization_OptimizeBatches);

    double StartTime = FPlatformTime::Seconds();
    
    OptimizeBatchesInternal();
    
    double EndTime = FPlatformTime::Seconds();
    BatchingData.BatchOptimizationTimeMs = (EndTime - StartTime) * 1000.0f;

    OnBatchOptimized.Broadcast(BatchingData.OptimizedBatches, BatchingData.BatchOptimizationTimeMs);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Batch optimization completed: %d batches optimized in %.2f ms"), 
                              BatchingData.OptimizedBatches, BatchingData.BatchOptimizationTimeMs);
}

void UAuracronFoliagePerformanceOptimizationManager::EnableGPUInstancing(bool bEnabled)
{
    Configuration.bEnableGPUInstancing = bEnabled;
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("GPU instancing %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliagePerformanceOptimizationManager::SetGPUInstancingMode(EAuracronGPUInstancingMode Mode)
{
    Configuration.GPUInstancingMode = Mode;
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("GPU instancing mode set to: %s"), *UEnum::GetValueAsString(Mode));
}

EAuracronGPUInstancingMode UAuracronFoliagePerformanceOptimizationManager::GetGPUInstancingMode() const
{
    return Configuration.GPUInstancingMode;
}

void UAuracronFoliagePerformanceOptimizationManager::ValidateConfiguration()
{
    // Validate culling settings
    Configuration.FrustumCullingMargin = FMath::Max(0.0f, Configuration.FrustumCullingMargin);
    Configuration.OcclusionCullingAccuracy = FMath::Clamp(Configuration.OcclusionCullingAccuracy, 0.1f, 1.0f);
    Configuration.OcclusionQueryBudget = FMath::Max(100, Configuration.OcclusionQueryBudget);

    // Validate batching settings
    Configuration.MaxInstancesPerBatch = FMath::Max(100, Configuration.MaxInstancesPerBatch);
    Configuration.BatchingRadius = FMath::Max(100.0f, Configuration.BatchingRadius);

    // Validate GPU settings
    Configuration.MaxGPUInstances = FMath::Max(1000, Configuration.MaxGPUInstances);

    // Validate memory settings
    Configuration.MemoryBudgetMB = FMath::Max(256.0f, Configuration.MemoryBudgetMB);

    // Validate threading settings
    Configuration.WorkerThreadCount = FMath::Clamp(Configuration.WorkerThreadCount, 1, 8);

    // Validate adaptive performance settings
    Configuration.TargetFrameRate = FMath::Max(30.0f, Configuration.TargetFrameRate);
    Configuration.PerformanceThreshold = FMath::Clamp(Configuration.PerformanceThreshold, 0.5f, 0.95f);

    // Validate update intervals
    Configuration.CullingUpdateInterval = FMath::Max(0.016f, Configuration.CullingUpdateInterval); // Min 60 FPS
    Configuration.BatchingUpdateInterval = FMath::Max(0.05f, Configuration.BatchingUpdateInterval);
    Configuration.PerformanceMonitoringInterval = FMath::Max(0.5f, Configuration.PerformanceMonitoringInterval);
}
