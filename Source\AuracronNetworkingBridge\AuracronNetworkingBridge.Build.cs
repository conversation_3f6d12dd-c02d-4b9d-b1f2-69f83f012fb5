// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema Multiplayer 5v5 Bridge Build Configuration
using UnrealBuildTool;
public class AuracronNetworkingBridge : ModuleRules
{
    public AuracronNetworkingBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicIncludePaths.AddRange(
            new string[] {
                // ... add public include paths required here ...
            }
        );
        PrivateIncludePaths.AddRange(
            new string[] {
                // ... add other private include paths required here ...
            }
        );
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "NetCore",
                "ReplicationGraph",
                "PacketHandler",
                "ReliabilityHandlerComponent",
                "OnlineSubsystem",
                "OnlineSubsystemUtils",
                "OnlineSubsystemEOS",
                "EOSShared",
                "Sockets",
                "Networking",
                "GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "ModularGameplay",
                "EnhancedInput",
                "InputCore",
                "UMG",
                "Slate",
                "SlateCore",
                "DeveloperSettings",
                "EngineSettings",

                "NetworkPrediction"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "RenderCore",
                "RHI",
                "Json",
                "AudioMixer",
                "SignalProcessing",
                "ChaosCore",
                "PhysicsCore",
                "NetworkReplayStreaming",
                "SessionServices",
                "GameplayDebugger",
                "NetworkPrediction",


                "NetCore",
                "OnlineSubsystem",
                "OnlineSubsystemEOS",
                "Voice",
                "VoiceChat",
                "HTTP",
                "HTTPServer",
                "WebSockets",
                "SSL",
                "Chaos",
                "ChaosVehicles",
                "GeometryCollectionEngine"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "ToolMenus",
                    "EditorStyle",
                    "EditorWidgets",
                    "UnrealEd",
                    "PropertyEditor",
                    "KismetCompiler",
                    "BlueprintGraph",
                    "Kismet",
                    "ToolMenus"
                }
            );
        }
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {}
        );
        // Enable optimization for shipping builds
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            OptimizeCode = CodeOptimization.InShippingBuildsOnly;
            bUseUnity = true;
        }
        // Enable additional features for development builds
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_NETWORKING_DEBUG=1");
            PublicDefinitions.Add("AURACRON_NETWORKING_PROFILING=1");
            PublicDefinitions.Add("AURACRON_CHEAT_DETECTION=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_NETWORKING_DEBUG=0");
            PublicDefinitions.Add("AURACRON_NETWORKING_PROFILING=0");
            PublicDefinitions.Add("AURACRON_CHEAT_DETECTION=1");
        }
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_PLATFORM=1");
            // Mobile-specific optimizations
            bUseUnity = true;
            MinFilesUsingPrecompiledHeaderOverride = 1;
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_PLATFORM=0");
        }
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_EOS=1");
        PublicDefinitions.Add("WITH_ONLINE_SUBSYSTEM_EOS=1");
        PublicDefinitions.Add("WITH_REPLICATION_GRAPH=1");
        PublicDefinitions.Add("WITH_IRIS_NETWORKING=1");
        PublicDefinitions.Add("WITH_NETWORK_PREDICTION=1");
        PublicDefinitions.Add("WITH_GAMEPLAY_ABILITY_SYSTEM=1");
        PublicDefinitions.Add("WITH_ENHANCED_INPUT=1");
        PublicDefinitions.Add("WITH_VOICE_CHAT=1");
        PublicDefinitions.Add("WITH_REPLAY_SYSTEM=1");
        PublicDefinitions.Add("WITH_ANTI_CHEAT=1");
        // Security and anti-cheat
        PublicDefinitions.Add("AURACRON_SERVER_AUTHORITATIVE=1");
        PublicDefinitions.Add("AURACRON_CLIENT_PREDICTION=1");
        PublicDefinitions.Add("AURACRON_CHEAT_DETECTION=1");
        PublicDefinitions.Add("AURACRON_SECURE_NETWORKING=1");
        // Performance optimizations
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("AURACRON_OPTIMIZE_NETWORKING=1");
            PublicDefinitions.Add("AURACRON_COMPRESS_PACKETS=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_OPTIMIZE_NETWORKING=0");
            PublicDefinitions.Add("AURACRON_COMPRESS_PACKETS=0");
        }
        // Cross-platform support
        PublicDefinitions.Add("AURACRON_CROSS_PLATFORM=1");
        PublicDefinitions.Add("AURACRON_CROSS_PROGRESSION=1");
        PublicDefinitions.Add("AURACRON_CROSS_PLAY=1");
    }
}


