// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de PartÃ­culas e Efeitos Visuais Bridge
// IntegraÃ§Ã£o C++ para VFX usando Niagara e APIs modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "Components/GameFrameworkComponent.h"
#include "NiagaraSystem.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "NiagaraDataInterface.h"
#include "NiagaraParameterCollection.h"
// NiagaraParameterCollectionInstance.h não existe no UE 5.6 - removido
#include "Particles/ParticleSystem.h"
#include "Particles/ParticleSystemComponent.h"
#include "Materials/Material.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "GameplayTagContainer.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "Net/Core/PushModel/PushModel.h"
#include "TimerManager.h"
#include "Async/Async.h"
#include "Engine/AssetManager.h"
#include "Engine/StreamableManager.h"
#include "HAL/ThreadSafeBool.h"
#include "AuracronVFXBridge.generated.h"

/**
 * EnumeraÃ§Ã£o para tipos de efeitos visuais
 */
UENUM(BlueprintType)
enum class EAuracronVFXType : uint8
{
    None                UMETA(DisplayName = "None"),
    Ability             UMETA(DisplayName = "Ability Effect"),
    Impact              UMETA(DisplayName = "Impact Effect"),
    Projectile          UMETA(DisplayName = "Projectile Effect"),
    Aura                UMETA(DisplayName = "Aura Effect"),
    Buff                UMETA(DisplayName = "Buff Effect"),
    Debuff              UMETA(DisplayName = "Debuff Effect"),
    Healing             UMETA(DisplayName = "Healing Effect"),
    Death               UMETA(DisplayName = "Death Effect"),
    Respawn             UMETA(DisplayName = "Respawn Effect"),
    Teleport            UMETA(DisplayName = "Teleport Effect"),
    Environmental       UMETA(DisplayName = "Environmental Effect"),
    Weather             UMETA(DisplayName = "Weather Effect"),
    Destruction         UMETA(DisplayName = "Destruction Effect"),
    UI                  UMETA(DisplayName = "UI Effect"),
    Transition          UMETA(DisplayName = "Transition Effect")
};

/**
 * EnumeraÃ§Ã£o para qualidade de VFX
 */
UENUM(BlueprintType)
enum class EAuracronVFXQuality : uint8
{
    Low                 UMETA(DisplayName = "Low Quality"),
    Medium              UMETA(DisplayName = "Medium Quality"),
    High                UMETA(DisplayName = "High Quality"),
    Ultra               UMETA(DisplayName = "Ultra Quality"),
    Cinematic           UMETA(DisplayName = "Cinematic Quality")
};

/**
 * Estrutura para configuraÃ§Ã£o de efeito visual
 */
USTRUCT(BlueprintType)
struct AURACRONVFXBRIDGE_API FAuracronVFXConfiguration
{
    GENERATED_BODY()

    /** Sistema Niagara do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration")
    TSoftObjectPtr<UNiagaraSystem> NiagaraSystem;

    /** Sistema de partÃ­culas legacy (fallback) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration")
    TSoftObjectPtr<UParticleSystem> LegacyParticleSystem;

    /** Tipo do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration")
    EAuracronVFXType VFXType = EAuracronVFXType::None;

    /** DuraÃ§Ã£o do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration", meta = (ClampMin = "0.1", ClampMax = "60.0"))
    float Duration = 2.0f;

    /** Escala do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    FVector Scale = FVector::OneVector;

    /** Cor do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration")
    FLinearColor Color = FLinearColor::White;

    /** Intensidade do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration", meta = (ClampMin = "0.0", ClampMax = "5.0"))
    float Intensity = 1.0f;

    /** Velocidade do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float Speed = 1.0f;

    /** Usar pooling de componentes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration")
    bool bUseComponentPooling = true;

    /** Seguir ator */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration")
    bool bFollowActor = false;

    /** Ator a seguir */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration")
    TObjectPtr<AActor> ActorToFollow;

    /** Socket para anexar */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration")
    FName AttachSocket = NAME_None;

    /** Offset de posiÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration")
    FVector PositionOffset = FVector::ZeroVector;

    /** Offset de rotaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration")
    FRotator RotationOffset = FRotator::ZeroRotator;

    /** ParÃ¢metros customizados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration")
    TMap<FString, float> CustomParameters;

    /** ParÃ¢metros de vetor */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration")
    TMap<FString, FVector> VectorParameters;

    /** ParÃ¢metros de cor */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration")
    TMap<FString, FLinearColor> ColorParameters;

    /** Usar LOD baseado em distÃ¢ncia */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration")
    bool bUseDistanceLOD = true;

    /** DistÃ¢ncia mÃ¡xima de renderizaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration", meta = (ClampMin = "100.0", ClampMax = "10000.0"))
    float MaxRenderDistance = 5000.0f;

    /** Qualidade do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration")
    EAuracronVFXQuality Quality = EAuracronVFXQuality::High;

    /** Prioridade de renderizaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration", meta = (ClampMin = "0", ClampMax = "10"))
    int32 RenderPriority = 5;

    /** Tags do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX Configuration")
    FGameplayTagContainer VFXTags;
};

/**
 * Estrutura para configuraÃ§Ã£o de efeitos de realm
 */
USTRUCT(BlueprintType)
struct AURACRONVFXBRIDGE_API FAuracronRealmVFXConfiguration
{
    GENERATED_BODY()

    /** Efeitos ambientes da PlanÃ­cie */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm VFX")
    TArray<TSoftObjectPtr<UNiagaraSystem>> SurfaceAmbientEffects;

    /** Efeitos ambientes do Firmamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm VFX")
    TArray<TSoftObjectPtr<UNiagaraSystem>> SkyAmbientEffects;

    /** Efeitos ambientes do Abismo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm VFX")
    TArray<TSoftObjectPtr<UNiagaraSystem>> UndergroundAmbientEffects;

    /** Efeitos de transiÃ§Ã£o entre realms */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm VFX")
    TArray<TSoftObjectPtr<UNiagaraSystem>> RealmTransitionEffects;

    /** Efeitos de portais */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm VFX")
    TSoftObjectPtr<UNiagaraSystem> PortalEffect;

    /** Efeitos de conectores verticais */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm VFX")
    TSoftObjectPtr<UNiagaraSystem> VerticalConnectorEffect;

    /** Densidade de efeitos ambientes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm VFX", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float AmbientEffectDensity = 1.0f;

    /** Usar efeitos de clima */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm VFX")
    bool bUseWeatherEffects = true;

    /** Intensidade dos efeitos de clima */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm VFX", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float WeatherIntensity = 1.0f;

    /** Usar fog volumÃ©trico */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm VFX")
    bool bUseVolumetricFog = true;

    /** Densidade do fog */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm VFX", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float FogDensity = 0.3f;

    /** Cor do fog por realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm VFX")
    TArray<FLinearColor> RealmFogColors;
};

/**
 * Classe principal do Bridge para Sistema de PartÃ­culas e Efeitos Visuais
 * ResponsÃ¡vel pelo gerenciamento completo de VFX com Niagara
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|VFX", meta = (DisplayName = "AURACRON VFX Bridge", BlueprintSpawnableComponent))
class AURACRONVFXBRIDGE_API UAuracronVFXBridge : public UGameFrameworkComponent
{
    GENERATED_BODY()

public:
    UAuracronVFXBridge(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core VFX Management ===

    /**
     * Spawnar efeito Niagara
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Niagara", CallInEditor)
    UNiagaraComponent* SpawnNiagaraEffect(const FAuracronVFXConfiguration& VFXConfig, const FVector& Location, const FRotator& Rotation = FRotator::ZeroRotator);

    /**
     * Spawnar efeito anexado
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Niagara", CallInEditor)
    UNiagaraComponent* SpawnAttachedNiagaraEffect(const FAuracronVFXConfiguration& VFXConfig, AActor* AttachActor, const FName& SocketName = NAME_None);

    /**
     * Parar efeito
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Control", CallInEditor)
    bool StopVFXEffect(UNiagaraComponent* EffectComponent);

    /**
     * Pausar todos os efeitos
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Control", CallInEditor)
    bool PauseAllVFXEffects();

    /**
     * Retomar todos os efeitos
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Control", CallInEditor)
    bool ResumeAllVFXEffects();

    // === Ability VFX ===

    /**
     * Reproduzir efeito de habilidade
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Abilities", CallInEditor)
    bool PlayAbilityVFX(const FString& ChampionID, const FString& AbilitySlot, const FVector& Location, const FVector& TargetLocation = FVector::ZeroVector);

    /**
     * Reproduzir efeito de impacto
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Combat", CallInEditor)
    bool PlayImpactVFX(const FVector& Location, const FString& ImpactType, float Intensity = 1.0f);

    /**
     * Reproduzir efeito de cura
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Healing", CallInEditor)
    bool PlayHealingVFX(AActor* TargetActor, float HealAmount);

    /**
     * Reproduzir efeito de buff/debuff
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|StatusEffects", CallInEditor)
    bool PlayStatusEffectVFX(AActor* TargetActor, const FString& EffectType, float Duration);

    // === Realm VFX ===

    /**
     * Ativar efeitos de realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Realm", CallInEditor)
    bool ActivateRealmVFX(int32 RealmIndex);

    /**
     * Reproduzir transiÃ§Ã£o de realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Realm", CallInEditor)
    bool PlayRealmTransitionVFX(int32 FromRealm, int32 ToRealm, const FVector& Location);

    /**
     * Atualizar efeitos ambientes
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Realm", CallInEditor)
    bool UpdateAmbientVFX(int32 RealmIndex, float Intensity = 1.0f);

    /**
     * Spawnar portal visual
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Realm", CallInEditor)
    UNiagaraComponent* SpawnPortalVFX(const FVector& Location, int32 DestinationRealm);

    // === Dynamic Materials ===

    /**
     * Criar material dinÃ¢mico
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Materials", CallInEditor)
    UMaterialInstanceDynamic* CreateDynamicMaterial(UMaterialInterface* BaseMaterial, const TMap<FString, float>& Parameters);

    /**
     * Atualizar parÃ¢metros de material
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Materials", CallInEditor)
    bool UpdateMaterialParameters(UMaterialInstanceDynamic* Material, const TMap<FString, float>& Parameters);

    /**
     * Aplicar efeito de dissoluÃ§Ã£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Materials", CallInEditor)
    bool ApplyDissolveEffect(AActor* TargetActor, float DissolveTime = 2.0f);

    // === Performance Management ===

    /**
     * Definir qualidade de VFX
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Performance", CallInEditor)
    bool SetVFXQuality(EAuracronVFXQuality Quality);

    /**
     * Otimizar efeitos por distÃ¢ncia
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Performance", CallInEditor)
    bool OptimizeVFXByDistance(const FVector& ViewerLocation);

    /**
     * Limpar efeitos inativos
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON VFX|Performance", CallInEditor)
    bool CleanupInactiveVFX();

protected:
    // === Internal Methods ===
    
    /** Inicializar sistema de VFX */
    bool InitializeVFXSystem();
    
    /** Configurar Niagara */
    bool SetupNiagara();
    
    /** Configurar pooling de componentes */
    bool SetupComponentPooling();
    
    /** Processar efeitos ativos */
    void ProcessActiveVFX(float DeltaTime);
    
    /** Validar configuraÃ§Ã£o de VFX */
    bool ValidateVFXConfiguration(const FAuracronVFXConfiguration& VFXConfig) const;
    
    /** Obter componente do pool */
    UNiagaraComponent* GetPooledNiagaraComponent();
    
    /** Retornar componente ao pool */
    bool ReturnComponentToPool(UNiagaraComponent* Component);
    
    /** Obter componente do pool para otimização */
    UNiagaraComponent* GetComponentFromPool();
    
    /** Obter cor do realm */
    FVector GetRealmColor(int32 RealmIndex);

public:
    // === Configuration Properties ===

    /** ConfiguraÃ§Ã£o de efeitos de realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronRealmVFXConfiguration RealmVFXConfiguration;

    /** Qualidade atual de VFX */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", ReplicatedUsing = OnRep_VFXQuality)
    EAuracronVFXQuality CurrentVFXQuality = EAuracronVFXQuality::High;

    /** Efeitos ativos */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    TArray<TObjectPtr<UNiagaraComponent>> ActiveVFXComponents;

    /** Pool de componentes Niagara */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    TArray<TObjectPtr<UNiagaraComponent>> NiagaraComponentPool;
    
    /** Pool de componentes para otimização */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    TArray<TObjectPtr<UNiagaraComponent>> ComponentPool;

    /** Materiais dinÃ¢micos ativos */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    TArray<TObjectPtr<UMaterialInstanceDynamic>> ActiveDynamicMaterials;

private:
    // === Internal State ===
    
    /** Sistema inicializado */
    bool bSystemInitialized = false;
    
    /** Mapa de materiais dinâmicos por nome */
    TMap<FString, TObjectPtr<UMaterialInstanceDynamic>> DynamicMaterials;
    
    /** Timer para limpeza */
    FTimerHandle CleanupTimer;
    
    /** Timer para otimizaÃ§Ã£o */
    FTimerHandle OptimizationTimer;
    
    /** Mutex para thread safety */
    mutable FCriticalSection VFXMutex;

    // === Internal Methods ===

    /** Método para limpeza de VFX inativos (usado pelo timer) */
    void PerformCleanup();

    // === Replication Callbacks ===

    UFUNCTION()
    void OnRep_VFXQuality();

public:
    // === Delegates ===
    
    /** Delegate chamado quando efeito Ã© spawnado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnVFXSpawned, UNiagaraComponent*, EffectComponent, FAuracronVFXConfiguration, VFXConfig);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON VFX|Events")
    FOnVFXSpawned OnVFXSpawned;
    
    /** Delegate chamado quando qualidade de VFX muda */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnVFXQualityChanged, EAuracronVFXQuality, OldQuality, EAuracronVFXQuality, NewQuality);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON VFX|Events")
    FOnVFXQualityChanged OnVFXQualityChanged;
};

