#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "Engine/World.h"
#include "GameFramework/Actor.h"
#include "Components/ActorComponent.h"
#include "AuracronDynamicRealmBridge.h"
#include "AuracronSigilosBridge/Public/AuracronSigilosBridge.h"
#include "PCGComponent.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "GameplayEffectTypes.h"
#include "GameplayAbilitySpec.h"
#include "AbilitySystemComponent.h"
#include "GameplayTagContainer.h"
#include "NiagaraComponent.h"
#include "Components/AudioComponent.h"
#include "Engine/TimerHandle.h"
#include "Misc/Guid.h"
#include "AuracronDynamicRealmSubsystem.generated.h"

// Sigil Type Enums
UENUM(BlueprintType)
enum class ESigilType : uint8
{
    None        UMETA(DisplayName = "None"),
    Aegis       UMETA(DisplayName = "Aegis"),
    Tempest     UMETA(DisplayName = "Tempest"),
    Inferno     UMETA(DisplayName = "Inferno"),
    Glacial     UMETA(DisplayName = "Glacial"),
    Umbral      UMETA(DisplayName = "Umbral"),
    Prismatic   UMETA(DisplayName = "Prismatic"),
    Quantum     UMETA(DisplayName = "Quantum"),
    Celestial   UMETA(DisplayName = "Celestial")
};

UENUM(BlueprintType)
enum class EAuracronSigilType : uint8
{
    None        UMETA(DisplayName = "None"),
    Aegis       UMETA(DisplayName = "Aegis"),
    Tempest     UMETA(DisplayName = "Tempest"),
    Inferno     UMETA(DisplayName = "Inferno"),
    Glacial     UMETA(DisplayName = "Glacial"),
    Umbral      UMETA(DisplayName = "Umbral"),
    Prismatic   UMETA(DisplayName = "Prismatic"),
    Quantum     UMETA(DisplayName = "Quantum"),
    Celestial   UMETA(DisplayName = "Celestial"),
    Fusion20    UMETA(DisplayName = "Fusion 20")
};

// Forward declarations
class AAuracronRealmManager;
class AAuracronPrismalFlow;
class AAuracronDynamicRail;
class UAuracronLayerComponent;
class UAuracronRealmTransitionComponent;
class UPCGComponent;

/**
 * Data structure for realm layer configuration
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronRealmLayerData
{
    GENERATED_BODY()

    /** Layer identifier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Data")
    EAuracronRealmLayer LayerType;

    /** World height for this layer */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Data")
    float LayerHeight;

    /** Layer bounds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Data")
    FBox LayerBounds;

    /** Is layer currently active */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Data")
    bool bIsActive;

    /** Current evolution phase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Data")
    ERealmEvolutionPhase CurrentPhase;

    /** Layer-specific PCG components */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Data")
    TArray<TObjectPtr<UPCGComponent>> PCGComponents;

    /** Actors belonging to this layer */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Data")
    TArray<TObjectPtr<AActor>> LayerActors;

    /** Layer performance metrics */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Data")
    float LastUpdateTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Data")
    int32 ActiveActorCount;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Data")
    float MemoryUsageMB;

    /** Layer stability level */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Data")
    float StabilityLevel;

    /** Layer energy level */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Data")
    float EnergyLevel;

    /** Twilight intensity for Vesper effects */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Data")
    float TwilightIntensity;

    FAuracronRealmLayerData()
    {
        LayerType = EAuracronRealmLayer::None;
        LayerHeight = 0.0f;
        LayerBounds = FBox(EForceInit::ForceInit);
        bIsActive = false;
        CurrentPhase = ERealmEvolutionPhase::Despertar;
        LastUpdateTime = 0.0f;
        ActiveActorCount = 0;
        MemoryUsageMB = 0.0f;
        StabilityLevel = 1.0f;
        EnergyLevel = 1.0f;
        TwilightIntensity = 1.0f;
    }
};

/**
 * Data structure for Prismal Flow island
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FPrismalIslandData
{
    GENERATED_BODY()

    /** Island type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island Data")
    EPrismalIslandType IslandType;

    /** Island location in world space */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island Data")
    FVector Location;

    /** Island radius */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island Data")
    float Radius;

    /** Is island currently active */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island Data")
    bool bIsActive;

    /** Players currently on island */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island Data")
    TArray<TObjectPtr<APawn>> PlayersOnIsland;

    /** Island-specific effects */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island Data")
    TArray<TObjectPtr<AActor>> IslandEffects;

    /** Last activation time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island Data")
    float LastActivationTime;

    FPrismalIslandData()
    {
        IslandType = EPrismalIslandType::Nexus;
        Location = FVector::ZeroVector;
        Radius = 500.0f;
        bIsActive = false;
        LastActivationTime = 0.0f;
    }
};

/**
 * Estrutura para dados de evolução de camada
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronRealmLayerEvolutionData
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Evolution")
    EAuracronRealmLayer Layer = EAuracronRealmLayer::None;

    UPROPERTY(BlueprintReadOnly, Category = "Evolution")
    EAuracronLayerEvolutionStage EvolutionStage = EAuracronLayerEvolutionStage::Dormant;

    UPROPERTY(BlueprintReadOnly, Category = "Evolution")
    float EvolutionProgress = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Evolution")
    float StabilityLevel = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Evolution")
    float EnergyAccumulation = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Evolution")
    float LastEvolutionTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Evolution")
    float EvolutionRate = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "AI")
    float CreatureSpawnRateMultiplier = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Environment")
    float EnvironmentalHazardIntensity = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Resources")
    float ResourceDensityMultiplier = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Environment")
    float EnvironmentalFactors = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Evolution")
    float PlayerInfluence = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Evolution")
    bool bCanEvolve = true;

    UPROPERTY(BlueprintReadOnly, Category = "Evolution")
    bool bAutoEvolution = false;

    UPROPERTY(BlueprintReadOnly, Category = "Evolution")
    float RequiredPlayerTime = 300.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Evolution")
    float EvolutionSpeed = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Evolution")
    float StageStartTime = 0.0f;

    FAuracronRealmLayerEvolutionData()
    {
        Layer = EAuracronRealmLayer::None;
        EvolutionStage = EAuracronLayerEvolutionStage::Dormant;
        EvolutionProgress = 0.0f;
        StabilityLevel = 1.0f;
        EnergyAccumulation = 0.0f;
        LastEvolutionTime = 0.0f;
        EvolutionRate = 1.0f;
        CreatureSpawnRateMultiplier = 1.0f;
        EnvironmentalHazardIntensity = 1.0f;
        ResourceDensityMultiplier = 1.0f;
        EnvironmentalFactors = 1.0f;
    }
};

/**
 * Estrutura para transição avançada entre camadas
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronAdvancedRealmTransition
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    TWeakObjectPtr<AActor> Actor;

    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    EAuracronRealmLayer SourceLayer = EAuracronRealmLayer::None;

    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    EAuracronRealmLayer TargetLayer = EAuracronRealmLayer::None;

    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    float TransitionDuration = 2.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    float Progress = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    bool bUseCustomEffect = false;

    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    float StartTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    FGuid TransitionID;

    // Transition properties
    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    FVector OriginalLocation = FVector::ZeroVector;

    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    FRotator OriginalRotation = FRotator::ZeroRotator;

    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    FVector OriginalScale = FVector::OneVector;

    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    FVector TargetLocation = FVector::ZeroVector;

    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    FRotator TargetRotation = FRotator::ZeroRotator;

    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    FVector TargetScale = FVector::OneVector;

    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    float TargetLightIntensity = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    float TargetGravityScale = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Transition")
    float TargetTimeScale = 1.0f;

    FAuracronAdvancedRealmTransition()
    {
        TransitionID = FGuid::NewGuid();
    }
};

/**
 * Auracron Dynamic Realm Subsystem
 * 
 * Central management system for the three-layer dynamic realm:
 * - Manages layer transitions and evolution
 * - Controls Prismal Flow and islands
 * - Handles dynamic rail system
 * - Optimizes performance across layers
 * - Integrates with PCG for procedural content
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONDYNAMICREALMBRIDGE_API UAuracronDynamicRealmSubsystem : public UTickableWorldSubsystem
{
    GENERATED_BODY()

public:
    UAuracronDynamicRealmSubsystem();

    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    virtual bool ShouldCreateSubsystem(UObject* Outer) const override;

    // UWorldSubsystem interface
    virtual void OnWorldBeginPlay(UWorld& InWorld) override;
    virtual void Tick(float DeltaTime) override;
    virtual bool DoesSupportWorldType(const EWorldType::Type WorldType) const override;

    // Core realm management
    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    void InitializeRealmLayers();

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    void UpdateRealmEvolution(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    void TransitionToPhase(ERealmEvolutionPhase NewPhase);

    // Layer management
    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    bool ActivateLayer(EAuracronRealmLayer Layer);

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    bool DeactivateLayer(EAuracronRealmLayer Layer);

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    bool IsLayerActive(EAuracronRealmLayer Layer) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Dynamic Realm")
    FAuracronRealmLayerData GetLayerData(EAuracronRealmLayer Layer) const;

    // Actor layer management
    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    bool RegisterActorToLayer(AActor* Actor, EAuracronRealmLayer Layer);

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    bool UnregisterActorFromLayer(AActor* Actor, EAuracronRealmLayer Layer);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Dynamic Realm")
    EAuracronRealmLayer GetActorLayer(AActor* Actor) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Dynamic Realm")
    EAuracronRealmLayer GetLocationLayer(const FVector& Location) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Dynamic Realm")
    FVector GetLayerCenter(EAuracronRealmLayer Layer) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Dynamic Realm")
    TArray<AActor*> GetActorsInLayer(EAuracronRealmLayer Layer) const;

    // Transition system
    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    bool RequestLayerTransition(AActor* Actor, EAuracronRealmLayer TargetLayer, ERealmTransitionType TransitionType = ERealmTransitionType::Gradual);

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    bool IsActorInTransition(AActor* Actor) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Dynamic Realm")
    float GetTransitionProgress(AActor* Actor) const;

    // Prismal Flow system
    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    void InitializePrismalFlow();

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    void UpdatePrismalFlow(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    bool ActivatePrismalIsland(EPrismalIslandType IslandType, const FVector& Location);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Dynamic Realm")
    TArray<FPrismalIslandData> GetActivePrismalIslands() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Dynamic Realm")
    FPrismalIslandData GetNearestPrismalIsland(const FVector& Location, EPrismalIslandType IslandType = EPrismalIslandType::Nexus) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Dynamic Realm")
    AAuracronPrismalFlow* GetPrismalFlowActor() const;

    // Dynamic rail system
    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    void InitializeDynamicRails();

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    bool ActivateRail(EAuracronRailType RailType, const FVector& StartLocation, const FVector& EndLocation);

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    bool UseRail(APawn* Pawn, EAuracronRailType RailType);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Dynamic Realm")
    TArray<AAuracronDynamicRail*> GetActiveRails() const;

    // Performance and optimization
    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    void OptimizeLayerPerformance(EAuracronRealmLayer Layer);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Dynamic Realm")
    float GetLayerPerformanceMetric(EAuracronRealmLayer Layer) const;

    // Evolution phase getter
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Dynamic Realm")
    ERealmEvolutionPhase GetCurrentEvolutionPhase() const { return CurrentEvolutionPhase; }

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm")
    void SetLayerLOD(EAuracronRealmLayer Layer, int32 LODLevel);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Dynamic Realm")
    int32 GetLayerLODLevel(EAuracronRealmLayer Layer) const;

    // Debug and development
    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm", CallInEditor)
    void DebugShowLayerInfo();

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm", CallInEditor)
    void DebugToggleLayerVisibility(EAuracronRealmLayer Layer);

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm", CallInEditor)
    void DebugGenerateLayerContent(EAuracronRealmLayer Layer);

    // Advanced layer evolution system
    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm|Evolution")
    void InitializeLayerEvolutionSystem();

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm|Evolution")
    void InitializeEvolutionSystem();

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm|Evolution")
    FAuracronRealmLayerEvolutionData GetLayerEvolutionData(EAuracronRealmLayer Layer) const;

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm|Evolution")
    void SetLayerEvolutionRate(EAuracronRealmLayer Layer, float EvolutionRate);

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm|Evolution")
    void ForceLayerEvolution(EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage TargetStage);

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm|Evolution")
    void SetLayerEvolutionSpeed(EAuracronRealmLayer Layer, float Speed);

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm|Evolution")
    void EnableLayerAutoEvolution(EAuracronRealmLayer Layer, bool bEnable);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Dynamic Realm|Evolution")
    FAuracronGlobalEvolutionData GetGlobalEvolutionData() const;

    // === Adaptive AI Control ===

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm|AI")
    void SetCreatureSpawnRateMultiplier(float Multiplier);

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm|Environment")
    void SetEnvironmentalHazardIntensity(float Intensity);

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm|Resources")
    void SetResourceDensityMultiplier(float Multiplier);

    // Advanced transition system
    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm|Transition")
    void InitiateAdvancedLayerTransition(AActor* Actor, EAuracronRealmLayer TargetLayer, float TransitionDuration = 2.0f, bool bUseCustomEffect = false);

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm|Transition")
    TArray<FAuracronAdvancedRealmTransition> GetActiveAdvancedTransitions() const { return ActiveAdvancedTransitions; }

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm|Transition")
    void CancelAdvancedTransition(const FGuid& TransitionID);

    // Integration with other systems
    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm|Integration")
    void IntegrateWithSigilSystem();

    UFUNCTION(BlueprintCallable, Category = "Dynamic Realm|Integration")
    float GetLayerResonanceValue(EAuracronRealmLayer Layer) const;

    // Sigil integration callbacks
    UFUNCTION()
    void OnSigilActivatedInRealm(EAuracronSigiloType SigilType, float PowerLevel);

    UFUNCTION()
    void OnFusion20ActivatedInRealm(const FAuracronSigilArchetype& SigilArchetype, float PowerLevel);

    // Events
    UPROPERTY(BlueprintAssignable, Category = "Dynamic Realm Events")
    FOnRealmLayerChanged OnRealmLayerChanged;

    UPROPERTY(BlueprintAssignable, Category = "Dynamic Realm Events")
    FOnLayerEvolutionStageChanged OnLayerEvolutionStageChanged;

    UPROPERTY(BlueprintAssignable, Category = "Dynamic Realm Events")
    FOnAdvancedRealmTransitionComplete OnAdvancedRealmTransitionComplete;

    UPROPERTY(BlueprintAssignable, Category = "Dynamic Realm Events")
    FOnGlobalEvolutionEvent OnGlobalEvolutionEvent;

    UPROPERTY(BlueprintAssignable, Category = "Dynamic Realm Events")
    FOnTranscendentContentUnlocked OnTranscendentContentUnlocked;

    UPROPERTY(BlueprintAssignable, Category = "Dynamic Realm Events")
    FOnRealmEvolutionPhaseChanged OnRealmEvolutionPhaseChanged;

    UPROPERTY(BlueprintAssignable, Category = "Dynamic Realm Events")
    FOnRealmTransitionStarted OnRealmTransitionStarted;

    UPROPERTY(BlueprintAssignable, Category = "Dynamic Realm Events")
    FOnRealmTransitionCompleted OnRealmTransitionCompleted;

    UPROPERTY(BlueprintAssignable, Category = "Dynamic Realm Events")
    FOnPrismalIslandActivated OnPrismalIslandActivated;

    // === Advanced Dynamic Rail System API ===

    /** Initialize advanced rail management system */
    UFUNCTION(BlueprintCallable, Category = "Advanced Rail System")
    void InitializeAdvancedRailSystem();

    /** Get rails in specific layer */
    UFUNCTION(BlueprintCallable, Category = "Advanced Rail System")
    TArray<AAuracronDynamicRail*> GetRailsInLayer(EAuracronRealmLayer Layer) const;

    /** Find nearest rail of specific type */
    UFUNCTION(BlueprintCallable, Category = "Advanced Rail System")
    AAuracronDynamicRail* FindNearestRail(const FVector& Location, EAuracronRailType RailType) const;

    /** Activate all rails in layer */
    UFUNCTION(BlueprintCallable, Category = "Advanced Rail System")
    void ActivateAllRailsInLayer(EAuracronRealmLayer Layer);

    /** Deactivate all rails in layer */
    UFUNCTION(BlueprintCallable, Category = "Advanced Rail System")
    void DeactivateAllRailsInLayer(EAuracronRealmLayer Layer);

    /** Get player rail experience */
    UFUNCTION(BlueprintCallable, Category = "Advanced Rail System")
    FPlayerRailExperience GetPlayerRailExperience(APawn* Player) const;

    /** Set rail generation configuration */
    UFUNCTION(BlueprintCallable, Category = "Advanced Rail System")
    void SetRailGenerationConfig(const FRailGenerationConfig& NewConfig);

    /** Get rail generation configuration */
    UFUNCTION(BlueprintCallable, Category = "Advanced Rail System")
    FRailGenerationConfig GetRailGenerationConfig() const;

    // === Sigil Integration API ===

    /** Finalize system initialization */
    UFUNCTION(BlueprintCallable, Category = "System Management")
    void FinalizeSystemInitialization();

    /** Validate system integrity */
    UFUNCTION(BlueprintCallable, Category = "System Management")
    bool ValidateSystemIntegrity() const;

    // === Sigil Integration Events ===

    UPROPERTY(BlueprintAssignable, Category = "Sigil Integration Events")
    FOnSystemFullyInitialized OnSystemFullyInitialized;

protected:
    // Core realm data
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Realm Configuration")
    TMap<EAuracronRealmLayer, FAuracronRealmLayerData> RealmLayers;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Realm Configuration")
    ERealmEvolutionPhase CurrentEvolutionPhase;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Realm Configuration")
    float PhaseStartTime;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Realm Configuration")
    float TotalGameTime;

    // Prismal Flow data
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Prismal Flow")
    TObjectPtr<AAuracronPrismalFlow> PrismalFlowActor;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Prismal Flow")
    TArray<FPrismalIslandData> PrismalIslands;

    // Dynamic rails
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Dynamic Rails")
    TArray<TObjectPtr<AAuracronDynamicRail>> ActiveRails;

    // Transition management
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Transitions")
    TMap<TObjectPtr<AActor>, TObjectPtr<UAuracronRealmTransitionComponent>> ActiveTransitions;

    // Performance tracking
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Performance")
    float LastPerformanceUpdate;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Performance")
    TMap<EAuracronRealmLayer, float> LayerPerformanceMetrics;

    // Test methods - only available in development builds
#if WITH_AUTOMATION_TESTS
public:
    void TestMonitorAdvancedPerformance() { MonitorAdvancedPerformance(); }
    void TestOptimizeEvolutionPerformance() { OptimizeEvolutionPerformance(); }
    void TestUpdateLayerEvolution() { UpdateLayerEvolution(); }
#endif

private:
    // Internal management functions
    void InitializeLayerData();
    void UpdateLayerEvolution(EAuracronRealmLayer Layer, float DeltaTime);
    void ProcessActiveTransitions(float DeltaTime);
    void UpdatePrismalIslands(float DeltaTime);
    void UpdateDynamicRails(float DeltaTime);
    void OptimizePerformance(float DeltaTime);
    
    // Layer-specific initialization
    void InitializeTerrestrialLayer();
    void InitializeCelestialLayer();
    void InitializeAbyssalLayer();
    
    // Phase transition handlers
    void OnPhaseTransition(ERealmEvolutionPhase OldPhase, ERealmEvolutionPhase NewPhase);
    void HandleDespertarPhase();
    void HandleConvergenciaPhase();
    void HandleIntensificacaoPhase();
    void HandleResolucaoPhase();
    
    // Prismal Flow management
    void SpawnPrismalIslands();
    void UpdateIslandStates(float DeltaTime);
    void HandleIslandInteractions();
    
    // Performance optimization
    void UpdateLayerLOD();
    void CullInactiveContent();
    void ManageMemoryUsage();

    // Advanced evolution system methods
    void UpdateLayerEvolution();
    void CheckEvolutionStageAdvancement(EAuracronRealmLayer Layer, FAuracronRealmLayerEvolutionData& EvolutionData);
    void ApplyEvolutionEffectsToLayer(EAuracronRealmLayer Layer, const FAuracronRealmLayerEvolutionData& EvolutionData);
    void ApplyEvolutionStageEffects(EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage);
    void ApplyGlobalLayerEvolutionEffects(EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage);

    // Content generation methods
    void GenerateTerrestrialContent();
    void GenerateCelestialContent();
    void GenerateAbyssalContent();

    // Advanced transition methods
    void CalculateTargetTransitionProperties(FAuracronAdvancedRealmTransition& Transition);
    void ApplyPreTransitionEffects(const FAuracronAdvancedRealmTransition& Transition);
    void StartTransitionVisualEffects(const FAuracronAdvancedRealmTransition& Transition);
    void UpdateAdvancedTransition(const FGuid& TransitionID);
    void ApplyTransitionLayerEffects(const FAuracronAdvancedRealmTransition& Transition, float Alpha);
    void CompleteAdvancedTransition(const FGuid& TransitionID);
    void CleanupAdvancedTransition(const FGuid& TransitionID);

    // Evolution calculation methods
    float CalculateLayerActivityFactor(EAuracronRealmLayer Layer) const;
    float CalculatePlayerPresenceFactor(EAuracronRealmLayer Layer) const;
    float CalculateLayerEnergyFactor(EAuracronRealmLayer Layer) const;
    float CalculateEnvironmentalActivity(EAuracronRealmLayer Layer) const;
    float CalculateBaseEvolutionRate(EAuracronRealmLayer Layer) const;

    // Evolution stage methods
    EAuracronLayerEvolutionStage GetNextEvolutionStage(EAuracronLayerEvolutionStage CurrentStage) const;
    float GetRequiredProgressForStage(EAuracronLayerEvolutionStage Stage) const;
    float GetStageIntensityMultiplier(EAuracronLayerEvolutionStage Stage) const;
    float GetLayerBonusMultiplier(EAuracronRealmLayer Layer) const;

    // Effect application methods
    void ApplyStageVisualEffects(AActor* Actor, EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage);
    void ApplyStageGameplayEffects(AActor* Actor, EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage);
    void ApplyStageEnvironmentalChanges(AActor* Actor, EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage);
    void ApplyLayerAtmosphericEffects(AActor* Actor, EAuracronRealmLayer Layer, float Intensity);
    void ApplyGlobalAtmosphericChanges(AAuracronRealmManager* RealmManager, EAuracronLayerEvolutionStage Stage, float Intensity);
    void ApplyGlobalLightingChanges(AAuracronRealmManager* RealmManager, EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage, float Intensity);
    void ApplyGlobalAudioChanges(AAuracronRealmManager* RealmManager, EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage, float Intensity);

    // Global evolution event methods
    void CheckGlobalEvolutionEvents();
    void TriggerGlobalEvolutionEvent(EAuracronGlobalEvolutionEvent EventType);
    void SpawnGlobalEvolutionVFX(const FString& VFXPath);
    void PlayGlobalEvolutionAudio(const FString& AudioPath);
    void ApplyGlobalEvolutionBonuses(float BonusMultiplier, const FString& BonusTag);
    void UnlockTranscendentContent();
    void UnlockTranscendentAbilities();

    // Utility methods
    float GetLayerDepthValue(EAuracronRealmLayer Layer) const;
    int32 CountLayerSpecificObjects(EAuracronRealmLayer Layer, const FString& ObjectType) const;
    float GetActorLayerEntryTime(AActor* Actor) const;
    void UpdateLayerStability(EAuracronRealmLayer Layer, FAuracronRealmLayerEvolutionData& EvolutionData);
    void ApplyInstabilityEffects(EAuracronRealmLayer Layer, float InstabilityLevel);
    void ApplyStabilityBonuses(EAuracronRealmLayer Layer, float StabilityBonus);
    void MonitorAdvancedPerformance();
    void OptimizeEvolutionPerformance();

    // Integration methods
    void RegisterRealmSigilBonuses(class UAuracronSigilosBridge* SigilBridge);
    void SetupLayerSigilEffects(class UAuracronSigilosBridge* SigilBridge);
    void CreateSigilRealmSynergies(class UAuracronSigilosBridge* SigilBridge);
    void SetupRealmSigilCombinationBonuses(class UAuracronSigilosBridge* SigilBridge);
    void SetupTranscendentSigilUnlocks(class UAuracronSigilosBridge* SigilBridge);
    void TriggerFusionRealmEffects(AActor* User, EAuracronRealmLayer Layer, const TArray<EAuracronSigiloType>& FusedSigils, float PowerLevel);
    void TriggerRealmWideFusionEffects(EAuracronRealmLayer Layer, float PowerLevel);

    // Configuration
    
    // Utility functions
    bool ValidateLayerConfiguration() const;
    void LogRealmStatus() const;
    void BroadcastLayerEvents();

    // Advanced evolution system variables
    UPROPERTY()
    bool bLayerEvolutionActive = false;

    UPROPERTY()
    float LayerEvolutionStartTime = 0.0f;

    UPROPERTY()
    float LastEvolutionUpdateTime = 0.0f;

    UPROPERTY()
    TMap<EAuracronRealmLayer, FAuracronRealmLayerEvolutionData> LayerEvolutionData;

    UPROPERTY()
    TArray<FAuracronAdvancedRealmTransition> ActiveAdvancedTransitions;

    UPROPERTY()
    TMap<FGuid, FTimerHandle> ActiveTransitionTimers;

    UPROPERTY()
    TMap<FGuid, TObjectPtr<UNiagaraComponent>> TransitionVFXComponents;

    UPROPERTY()
    TMap<FGuid, TObjectPtr<UAudioComponent>> TransitionAudioComponents;

    UPROPERTY()
    TMap<FGuid, FActiveGameplayEffectHandle> TransitionEffectHandles;

    UPROPERTY()
    TMap<TObjectPtr<AActor>, FActiveGameplayEffectHandle> ActorLayerEffects;

    UPROPERTY()
    TMap<TObjectPtr<AActor>, FActiveGameplayEffectHandle> ActorEvolutionEffects;

    UPROPERTY()
    TMap<TObjectPtr<AActor>, float> ActorLayerEntryTimes;

    // === Advanced Rail System Variables ===

    UPROPERTY()
    bool bAdvancedRailSystemActive = false;

    UPROPERTY()
    float RailSystemStartTime = 0.0f;

    UPROPERTY()
    float LastRailSystemUpdate = 0.0f;

    UPROPERTY()
    FRailGenerationConfig RailGenerationConfig;

    // Layer rail mapping - not exposed to Blueprint due to TArray in TMap limitation
    TMap<EAuracronRealmLayer, TArray<TObjectPtr<AAuracronDynamicRail>>> LayerRailMap;

    UPROPERTY()
    TMap<TObjectPtr<AAuracronDynamicRail>, FRailSystemMetrics> RailMetricsMap;

    UPROPERTY()
    TMap<TObjectPtr<APawn>, FPlayerRailExperience> PlayerRailExperienceMap;

    UPROPERTY()
    TMap<TObjectPtr<APawn>, FActiveGameplayEffectHandle> PlayerRailBonuses;

    UPROPERTY()
    TMap<TObjectPtr<APawn>, FActiveGameplayEffectHandle> PlayerRailMasteryEffects;

    // Rail system timers
    FTimerHandle RailSystemUpdateTimer;
    FTimerHandle RailOptimizationTimer;

    // Sigil integration variables
    UPROPERTY()
    TMap<TObjectPtr<APawn>, FActiveGameplayEffectHandle> PlayerSigilRealmEffects;

    UPROPERTY()
    TMap<TObjectPtr<APawn>, FGameplayAbilitySpecHandle> PlayerMasterRailAbilities;

private:
    // === Advanced Rail System Private Methods ===

    /** Generate rail network for specific layer */
    void GenerateLayerRailNetwork(EAuracronRealmLayer Layer);

    /** Get appropriate rail types for layer */
    TArray<EAuracronRailType> GetRailTypesForLayer(EAuracronRealmLayer Layer) const;

    /** Calculate number of rails to generate for type */
    int32 CalculateRailCountForType(EAuracronRealmLayer Layer, EAuracronRailType RailType) const;

    /** Generate single rail instance */
    void GenerateSingleRail(EAuracronRealmLayer Layer, EAuracronRailType RailType, int32 RailIndex);

    /** Calculate rail spawn location */
    FVector CalculateRailSpawnLocation(EAuracronRealmLayer Layer, EAuracronRailType RailType, int32 RailIndex) const;

    /** Generate rail path points */
    TArray<FVector> GenerateRailPath(EAuracronRealmLayer Layer, EAuracronRailType RailType, const FVector& StartLocation, int32 RailIndex) const;

    /** Generate solar rail path */
    void GenerateSolarRailPath(TArray<FVector>& PathPoints, const FVector& StartLocation, float PathLength, int32 Segments) const;

    /** Generate axis rail path */
    void GenerateAxisRailPath(TArray<FVector>& PathPoints, const FVector& StartLocation, float PathLength, int32 Segments, EAuracronRealmLayer Layer) const;

    /** Generate lunar rail path */
    void GenerateLunarRailPath(TArray<FVector>& PathPoints, const FVector& StartLocation, float PathLength, int32 Segments) const;

    /** Calculate path segments for rail type */
    int32 CalculatePathSegments(EAuracronRailType RailType) const;

    /** Calculate path length for layer and type */
    float CalculatePathLength(EAuracronRealmLayer Layer, EAuracronRailType RailType) const;

    /** Get target layer location for connections */
    FVector GetTargetLayerLocation(EAuracronRealmLayer SourceLayer) const;

    /** Update advanced rail system */
    void UpdateAdvancedRailSystem();

    /** Optimize rail system performance */
    void OptimizeRailSystem();

    /** Check for adaptive rail generation needs */
    void CheckAdaptiveRailGeneration();

    /** Determine optimal rail type for conditions */
    EAuracronRailType DetermineOptimalRailType(EAuracronRealmLayer Layer) const;

    /** Update rail network connectivity */
    void UpdateRailNetworkConnectivity();

    /** Check connectivity between two rails */
    void CheckRailConnectivity(AAuracronDynamicRail* Rail1, AAuracronDynamicRail* Rail2);

    /** Create connection between rails */
    void CreateRailConnection(AAuracronDynamicRail* Rail1, AAuracronDynamicRail* Rail2);

    /** Monitor rail system performance */
    void MonitorRailSystemPerformance();

    /** Get active rails in layer */
    TArray<AAuracronDynamicRail*> GetActiveRailsInLayer(EAuracronRealmLayer Layer) const;

    /** Get active players in layer */
    int32 GetActivePlayersInLayer(EAuracronRealmLayer Layer) const;

    /** Check if currently night time */
    bool IsCurrentlyNightTime() const;

    /** Get rail type bonus multiplier */
    float GetRailTypeBonusMultiplier(EAuracronRailType RailType) const;

    /** Update rail system metrics */
    void UpdateRailSystemMetrics(AAuracronDynamicRail* Rail, float DeltaTime);

    /** Update rail adaptive behavior */
    void UpdateRailAdaptiveBehavior(AAuracronDynamicRail* Rail, float DeltaTime);

    /** Enhance rail performance */
    void EnhanceRailPerformance(AAuracronDynamicRail* Rail, float EnhancementLevel);

    /** Optimize unused rail */
    void OptimizeUnusedRail(AAuracronDynamicRail* Rail);

    /** Adapt rail to time of day */
    void AdaptRailToTimeOfDay(AAuracronDynamicRail* Rail, EAuracronRailType RailType);

    /** Update rail player interactions */
    void UpdateRailPlayerInteractions(AAuracronDynamicRail* Rail);

    /** Apply rail bonuses to player */
    void ApplyRailBonusesToPlayer(APawn* Player, AAuracronDynamicRail* Rail);

    /** Update player rail experience */
    void UpdatePlayerRailExperience(APawn* Player, AAuracronDynamicRail* Rail);

    /** Calculate rail experience gain */
    float CalculateRailExperienceGain(EAuracronRailType RailType, float DeltaTime) const;

    /** Apply rail experience level bonus */
    void ApplyRailExperienceLevelBonus(APawn* Player, int32 NewLevel);

    // === Sigil Integration Private Methods ===

    /** Handle sigil activation */
    UFUNCTION()
    void OnSigilActivated(EAuracronSigiloType SigilType, APawn* Player);

    /** Handle sigil deactivation */
    UFUNCTION()
    void OnSigilDeactivated(EAuracronSigiloType SigilType, APawn* Player);

    /** Handle sigil evolution */
    UFUNCTION()
    void OnSigilEvolved(EAuracronSigiloType SigilType, int32 NewLevel, APawn* Player);

    /** Apply Aegis sigil realm effects */
    void ApplyAegisSigilRealmEffects(APawn* Player);

    /** Apply Ruin sigil realm effects */
    void ApplyRuinSigilRealmEffects(APawn* Player);

    /** Apply Vesper sigil realm effects */
    void ApplyVesperSigilRealmEffects(APawn* Player);

    /** Apply Fusion sigil realm effects */
    void ApplyFusionSigilRealmEffects(APawn* Player);

    /** Apply sigil bonuses to realms */
    void ApplySigilBonusesToRealms();

    /** Apply sigil-specific realm bonus */
    void ApplySigilSpecificRealmBonus(APawn* Player, EAuracronSigiloType SigilType);

    /** Update rail accessibility for player */
    void UpdateRailAccessibilityForPlayer(APawn* Player, EAuracronSigiloType SigilType, bool bGrantAccess);

    /** Remove sigil realm effects */
    void RemoveSigilRealmEffects(APawn* Player, EAuracronSigiloType SigilType);

    /** Restore default rail properties */
    void RestoreDefaultRailProperties(APawn* Player, EAuracronSigiloType SigilType);

    /** Apply enhanced sigil realm effects */
    void ApplyEnhancedSigilRealmEffects(APawn* Player, EAuracronSigiloType SigilType, int32 SigilLevel);

    /** Unlock advanced rail features */
    void UnlockAdvancedRailFeatures(APawn* Player, EAuracronSigiloType SigilType, int32 SigilLevel);

    /** Grant rail speed boost */
    void GrantRailSpeedBoost(APawn* Player, EAuracronSigiloType SigilType);

    /** Grant rail energy efficiency */
    void GrantRailEnergyEfficiency(APawn* Player, EAuracronSigiloType SigilType);

    /** Grant cross-layer rail access */
    void GrantCrossLayerRailAccess(APawn* Player, EAuracronSigiloType SigilType);

    /** Grant master rail abilities */
    void GrantMasterRailAbilities(APawn* Player, EAuracronSigiloType SigilType);

    /** Apply sigil rail enhancement */
    void ApplySigilRailEnhancement(AAuracronDynamicRail* Rail, APawn* Player, EAuracronSigiloType SigilType);

    // Evolution system private methods
    void InitializeLayerEvolutionData(EAuracronRealmLayer Layer);
    void SetupEvolutionTriggers();
    void InitializeGlobalEvolutionTracking();
    void MonitorEvolutionProgress();
    void UpdateEvolutionFactors(EAuracronRealmLayer Layer, FAuracronRealmLayerEvolutionData& EvolutionData);
    float CalculateEnvironmentalFactors(EAuracronRealmLayer Layer) const;
    float CalculateTriggerInfluence(EAuracronRealmLayer Layer) const;
    float CalculateGlobalEvolutionProgress() const;
    bool IsTriggerActiveForLayer(EAuracronEvolutionTrigger Trigger, EAuracronRealmLayer Layer) const;
    void CheckStageProgression(EAuracronRealmLayer Layer, FAuracronRealmLayerEvolutionData& EvolutionData);
    void ProgressToNextStage(EAuracronRealmLayer Layer, FAuracronRealmLayerEvolutionData& EvolutionData, EAuracronLayerEvolutionStage NextStage);
    void ApplyEvolutionEffects(EAuracronRealmLayer Layer, const FAuracronRealmLayerEvolutionData& EvolutionData);
    void PlayLayerEvolutionEffects(EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage);
    void UpdateGlobalEvolutionTracking(EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage);
    void ApplyStageEffectsToActor(AActor* Actor, EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage, float Progress);
    void RemoveActorEvolutionEffects(AActor* Actor);

    // Evolution utility methods
    bool CheckCombatActivityInLayer(EAuracronRealmLayer Layer) const;
    bool CheckSigilActivityInLayer(EAuracronRealmLayer Layer) const;
    bool CheckRailUsageInLayer(EAuracronRealmLayer Layer) const;
    bool CheckIslandActivityInLayer(EAuracronRealmLayer Layer) const;
    bool CheckWeatherEventsInLayer(EAuracronRealmLayer Layer) const;
    bool CheckSeasonalChangeInLayer(EAuracronRealmLayer Layer) const;
    FLinearColor GetLayerEvolutionColor(EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage) const;

    UPROPERTY()
    TMap<EAuracronRealmLayer, TObjectPtr<UNiagaraComponent>> EvolutionVFXComponents;

    UPROPERTY()
    TMap<TObjectPtr<APawn>, FActiveGameplayEffectHandle> GlobalEvolutionBonusHandles;

    UPROPERTY()
    TMap<TObjectPtr<APawn>, FGameplayAbilitySpecHandle> TranscendentAbilityHandles;

    // Global evolution event flags
    UPROPERTY()
    bool bGlobalEvolutionEventTriggered = false;

    UPROPERTY()
    bool bTranscendentEventTriggered = false;

    UPROPERTY()
    bool bMaxEvolutionEventTriggered = false;

    // Evolution system data - using existing definitions above
    UPROPERTY()
    TMap<EAuracronEvolutionTrigger, float> EvolutionTriggers;

    UPROPERTY()
    FAuracronGlobalEvolutionData GlobalEvolutionData;

    // Evolution timers
    FTimerHandle EvolutionMonitoringTimer;

    UPROPERTY()
    FTimerHandle LayerEvolutionTimer;

    // Configuration
    bool bIsInitialized;
    bool bEnableDebugLogging;
    float PerformanceUpdateInterval;
    int32 MaxConcurrentTransitions;
    
    // Timers
    FTimerHandle EvolutionUpdateTimer;
    FTimerHandle PerformanceOptimizationTimer;
    FTimerHandle PrismalFlowUpdateTimer;

public:
    // === Delegates ===

    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCreatureSpawnRateChanged, float, NewMultiplier);
    UPROPERTY(BlueprintAssignable, Category = "Dynamic Realm|Events")
    FOnCreatureSpawnRateChanged OnCreatureSpawnRateChanged;

    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnEnvironmentalHazardIntensityChanged, float, NewIntensity);
    UPROPERTY(BlueprintAssignable, Category = "Dynamic Realm|Events")
    FOnEnvironmentalHazardIntensityChanged OnEnvironmentalHazardIntensityChanged;

    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnResourceDensityChanged, float, NewMultiplier);
    UPROPERTY(BlueprintAssignable, Category = "Dynamic Realm|Events")
    FOnResourceDensityChanged OnResourceDensityChanged;
};
