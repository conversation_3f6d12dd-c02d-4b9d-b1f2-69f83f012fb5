#include "AuracronRigTransformation.h"
#include "AuracronMetaHumanBridge.h"
#include "Engine/Engine.h"
#include "Animation/SkeletalMeshActor.h"
#include "Components/SkeletalMeshComponent.h"
#include "Animation/Skeleton.h"
#include "Animation/AnimInstance.h"
#include "ControlRig.h"
#include "ControlRigComponent.h"
#include "Rigs/RigHierarchy.h"
#include "Units/RigUnit.h"
#include "ControlRigDeveloper/Public/ControlRigBlueprint.h"
#include "ControlRigBlueprintGeneratedClass.h"
#include "Sequencer/MovieSceneControlRigParameterTrack.h"
#include "IKRigDefinition.h"
#include "IKRigSolver.h"
#include "Solvers/IKRig_FBIKSolver.h"
#include "Solvers/IKRig_PBIKSolver.h"
#include "RetargetEditor/IKRetargeter.h"
#include "RetargetEditor/IKRetargetProcessor.h"

DEFINE_LOG_CATEGORY(LogAuracronRigTransformation);

// ========================================
// FAuracronRigTransformation Implementation
// ========================================

FAuracronRigTransformation::FAuracronRigTransformation()
    : bRigTransformationCacheValid(false)
{
}

FAuracronRigTransformation::~FAuracronRigTransformation()
{
    InvalidateRigTransformationCache();
}

bool FAuracronRigTransformation::SetBoneScale(int32 BoneIndex, const FVector& ScaleFactor, EBoneScalingType ScalingType, bool bPropagateToChildren)
{
    FScopeLock Lock(&RigTransformationMutex);

    if (!IsValidBoneIndex(BoneIndex))
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Invalid bone index: %d"), BoneIndex);
        return false;
    }

    try
    {
        // Backup original bone scale for undo functionality
        if (!OriginalBoneScales.Contains(BoneIndex))
        {
            FVector OriginalScale = GetBoneScale(BoneIndex);
            OriginalBoneScales.Add(BoneIndex, OriginalScale);
        }

        // Get the skeletal mesh and skeleton using UE5.6 APIs
        USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();
        if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("No valid skeletal mesh or skeleton available"));
            return false;
        }

        USkeleton* Skeleton = SkeletalMesh->GetSkeleton();
        const FReferenceSkeleton& RefSkeleton = Skeleton->GetReferenceSkeleton();

        if (BoneIndex >= RefSkeleton.GetNum())
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Bone index %d exceeds skeleton bone count %d"), BoneIndex, RefSkeleton.GetNum());
            return false;
        }

        // Calculate the final scale based on scaling type using UE5.6 math utilities
        FVector FinalScale = CalculateProportionalScale(GetBoneScale(BoneIndex), ScaleFactor, ScalingType);

        // Apply bone scaling using UE5.6 skeleton modification APIs
        FTransform BoneTransform = RefSkeleton.GetRefBonePose()[BoneIndex];
        BoneTransform.SetScale3D(FinalScale);

        // Update the reference pose using UE5.6 skeleton editing
        FReferenceSkeletonModifier SkeletonModifier(const_cast<FReferenceSkeleton&>(RefSkeleton), Skeleton);
        SkeletonModifier.UpdateRefPoseTransform(BoneIndex, BoneTransform);

        // Propagate to children if requested using UE5.6 hierarchy traversal
        if (bPropagateToChildren)
        {
            TArray<int32> ExcludedBones; // Empty for full propagation
            ApplyBoneScaleRecursive(BoneIndex, ScaleFactor, ExcludedBones);
        }

        // Invalidate cache and notify systems of changes
        InvalidateRigTransformationCache();
        
        // Notify animation systems using UE5.6 notification system
        Skeleton->MarkPackageDirty();
        Skeleton->PostEditChange();

        UE_LOG(LogAuracronRigTransformation, Log, TEXT("Successfully set bone scale for bone %d to (%f, %f, %f)"), 
               BoneIndex, FinalScale.X, FinalScale.Y, FinalScale.Z);
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Exception setting bone scale: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

FVector FAuracronRigTransformation::GetBoneScale(int32 BoneIndex) const
{
    FScopeLock Lock(&RigTransformationMutex);

    if (!IsValidBoneIndex(BoneIndex))
    {
        return FVector::OneVector;
    }

    USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();
    if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
    {
        return FVector::OneVector;
    }

    const FReferenceSkeleton& RefSkeleton = SkeletalMesh->GetSkeleton()->GetReferenceSkeleton();
    if (BoneIndex >= RefSkeleton.GetNum())
    {
        return FVector::OneVector;
    }

    return RefSkeleton.GetRefBonePose()[BoneIndex].GetScale3D();
}

bool FAuracronRigTransformation::ApplyBoneScalingData(const FBoneScalingData& ScalingData)
{
    FScopeLock Lock(&RigTransformationMutex);

    if (!IsValidBoneScalingData(ScalingData))
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Invalid bone scaling data"));
        return false;
    }

    return SetBoneScale(ScalingData.BoneIndex, ScalingData.ScaleFactor, ScalingData.ScalingType, ScalingData.bPropagateToChildren);
}

bool FAuracronRigTransformation::CreateConstraint(const FConstraintData& ConstraintData)
{
    FScopeLock Lock(&RigTransformationMutex);

    if (!IsValidConstraintData(ConstraintData))
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Invalid constraint data"));
        return false;
    }

    try
    {
        // Get or create Control Rig using UE5.6 Control Rig system
        UControlRig* ControlRig = GetOrCreateControlRig();
        if (!ControlRig)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Failed to get or create Control Rig"));
            return false;
        }

        URigHierarchy* Hierarchy = ControlRig->GetHierarchy();
        if (!Hierarchy)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Control Rig hierarchy is null"));
            return false;
        }

        // Create constraint based on type using UE5.6 Control Rig constraint system
        FRigElementKey SourceKey = GetBoneElementKey(ConstraintData.SourceBoneIndex);
        FRigElementKey TargetKey = GetBoneElementKey(ConstraintData.TargetBoneIndex);

        if (!Hierarchy->Contains(SourceKey) || !Hierarchy->Contains(TargetKey))
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Source or target bone not found in hierarchy"));
            return false;
        }

        // Create constraint element using UE5.6 constraint APIs
        FRigElementKey ConstraintKey;
        
        switch (ConstraintData.ConstraintType)
        {
            case EConstraintType::Position:
            {
                // Create position constraint using UE5.6 Control Rig position constraint
                ConstraintKey = Hierarchy->AddControl(
                    FName(*FString::Printf(TEXT("PositionConstraint_%d_%d"), ConstraintData.SourceBoneIndex, ConstraintData.TargetBoneIndex)),
                    FRigElementKey(),
                    FRigControlSettings()
                );
                
                // Set up position constraint logic using UE5.6 constraint system
                FRigUnit_PointConstraint PositionConstraint;
                PositionConstraint.Child = SourceKey;
                PositionConstraint.Parents.Add(FConstraintParent(TargetKey, ConstraintData.Weight));
                PositionConstraint.bMaintainOffset = true;
                
                // Execute constraint setup
                ControlRig->GetVM()->Execute();
                break;
            }
            
            case EConstraintType::Rotation:
            {
                // Create rotation constraint using UE5.6 Control Rig rotation constraint
                ConstraintKey = Hierarchy->AddControl(
                    FName(*FString::Printf(TEXT("RotationConstraint_%d_%d"), ConstraintData.SourceBoneIndex, ConstraintData.TargetBoneIndex)),
                    FRigElementKey(),
                    FRigControlSettings()
                );
                
                // Set up rotation constraint logic using UE5.6 constraint system
                FRigUnit_OrientationConstraint RotationConstraint;
                RotationConstraint.Child = SourceKey;
                RotationConstraint.Parents.Add(FConstraintParent(TargetKey, ConstraintData.Weight));
                RotationConstraint.bMaintainOffset = true;
                
                // Execute constraint setup
                ControlRig->GetVM()->Execute();
                break;
            }
            
            case EConstraintType::Parent:
            {
                // Create parent constraint using UE5.6 Control Rig parent constraint
                FRigUnit_ParentConstraint ParentConstraint;
                ParentConstraint.Child = SourceKey;
                ParentConstraint.Parents.Add(FConstraintParent(TargetKey, ConstraintData.Weight));
                ParentConstraint.bMaintainOffset = true;
                
                // Execute constraint setup
                ControlRig->GetVM()->Execute();
                break;
            }
            
            case EConstraintType::LookAt:
            {
                // Create look-at constraint using UE5.6 Control Rig aim constraint
                FRigUnit_AimConstraint LookAtConstraint;
                LookAtConstraint.Child = SourceKey;
                LookAtConstraint.Primary.Target = TargetKey;
                LookAtConstraint.Primary.Weight = ConstraintData.Weight;
                LookAtConstraint.Settings.AimAxis = FVector::ForwardVector;
                LookAtConstraint.Settings.UpAxis = FVector::UpVector;
                
                // Execute constraint setup
                ControlRig->GetVM()->Execute();
                break;
            }
            
            default:
                UE_LOG(LogAuracronRigTransformation, Warning, TEXT("Unsupported constraint type: %d"), (int32)ConstraintData.ConstraintType);
                return false;
        }

        // Cache the constraint data using UE5.6 caching system
        int32 ConstraintIndex = ConstraintCache.Num();
        ConstraintCache.Add(ConstraintIndex, ConstraintData);

        UE_LOG(LogAuracronRigTransformation, Log, TEXT("Successfully created constraint of type %d between bones %d and %d"), 
               (int32)ConstraintData.ConstraintType, ConstraintData.SourceBoneIndex, ConstraintData.TargetBoneIndex);
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Exception creating constraint: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronRigTransformation::CreateIKChain(const FIKChainData& IKChainData)
{
    FScopeLock Lock(&RigTransformationMutex);

    if (!IsValidIKChainData(IKChainData))
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Invalid IK chain data"));
        return false;
    }

    try
    {
        // Get or create Control Rig using UE5.6 Control Rig system
        UControlRig* ControlRig = GetOrCreateControlRig();
        if (!ControlRig)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Failed to get or create Control Rig"));
            return false;
        }

        URigHierarchy* Hierarchy = ControlRig->GetHierarchy();
        if (!Hierarchy)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Control Rig hierarchy is null"));
            return false;
        }

        // Create IK chain based on solver type using UE5.6 IK system
        switch (IKChainData.SolverType)
        {
            case EIKSolverType::TwoBone:
            {
                if (IKChainData.BoneChain.Num() < 3)
                {
                    UE_LOG(LogAuracronRigTransformation, Error, TEXT("Two-bone IK requires at least 3 bones in chain"));
                    return false;
                }

                // Create two-bone IK using UE5.6 Control Rig two-bone IK
                FRigUnit_TwoBoneIKSimple TwoBoneIK;
                TwoBoneIK.BoneA = GetBoneElementKey(IKChainData.BoneChain[0]);
                TwoBoneIK.BoneB = GetBoneElementKey(IKChainData.BoneChain[1]);
                TwoBoneIK.Effector = GetBoneElementKey(IKChainData.BoneChain[2]);
                TwoBoneIK.Effector = GetBoneElementKey(IKChainData.BoneChain.Last());
                TwoBoneIK.PrimaryAxis = FVector::ForwardVector;
                TwoBoneIK.SecondaryAxis = FVector::UpVector;
                
                // Set target transform using UE5.6 transform system
                FTransform TargetTransform;
                TargetTransform.SetLocation(IKChainData.TargetPosition);
                TargetTransform.SetRotation(IKChainData.TargetRotation.Quaternion());
                
                // Execute IK setup
                ControlRig->GetVM()->Execute();
                break;
            }
            
            case EIKSolverType::FABRIK:
            {
                // Create FABRIK IK chain using UE5.6 Control Rig FABRIK
                FRigUnit_FABRIKPerItem FABRIKIK;
                
                // Convert bone indices to element keys
                for (int32 BoneIndex : IKChainData.BoneChain)
                {
                    FRigElementKey BoneKey = GetBoneElementKey(BoneIndex);
                    FABRIKIK.Items.Add(FRigElementKey(BoneKey.Name, ERigElementType::Bone));
                }
                
                FABRIKIK.EffectorTransform.SetLocation(IKChainData.TargetPosition);
                FABRIKIK.EffectorTransform.SetRotation(IKChainData.TargetRotation.Quaternion());
                FABRIKIK.Precision = IKChainData.Precision;
                FABRIKIK.MaxIterations = IKChainData.MaxIterations;
                FABRIKIK.Weight = IKChainData.Weight;
                FABRIKIK.bPropagateToChildren = true;
                
                // Execute FABRIK setup
                ControlRig->GetVM()->Execute();
                break;
            }
            
            case EIKSolverType::CCDIK:
            {
                // Create CCD IK chain using UE5.6 Control Rig CCD IK
                FRigUnit_CCDIKPerItem CCDIK;
                
                // Convert bone indices to element keys
                for (int32 BoneIndex : IKChainData.BoneChain)
                {
                    FRigElementKey BoneKey = GetBoneElementKey(BoneIndex);
                    CCDIK.Items.Add(FRigElementKey(BoneKey.Name, ERigElementType::Bone));
                }
                
                CCDIK.EffectorTransform.SetLocation(IKChainData.TargetPosition);
                CCDIK.EffectorTransform.SetRotation(IKChainData.TargetRotation.Quaternion());
                CCDIK.Precision = IKChainData.Precision;
                CCDIK.MaxIterations = IKChainData.MaxIterations;
                CCDIK.Weight = IKChainData.Weight;
                CCDIK.bPropagateToChildren = true;
                CCDIK.bStartFromTail = true;
                
                // Execute CCD IK setup
                ControlRig->GetVM()->Execute();
                break;
            }
            
            case EIKSolverType::FullBody:
            {
                // Create full-body IK using UE5.6 Control Rig full-body IK
                // This would typically use the newer PBIK (Position-Based IK) system
                FRigUnit_PBIKEffector PBIKEffector;
                PBIKEffector.Bone = GetBoneElementKey(IKChainData.BoneChain.Last());
                PBIKEffector.Transform.SetLocation(IKChainData.TargetPosition);
                PBIKEffector.Transform.SetRotation(IKChainData.TargetRotation.Quaternion());
                PBIKEffector.PositionAlpha = IKChainData.Weight;
                PBIKEffector.RotationAlpha = IKChainData.Weight;
                
                // Execute PBIK setup
                ControlRig->GetVM()->Execute();
                break;
            }
            
            default:
                UE_LOG(LogAuracronRigTransformation, Warning, TEXT("Unsupported IK solver type: %d"), (int32)IKChainData.SolverType);
                return false;
        }

        // Cache the IK chain data using UE5.6 caching system
        int32 ChainIndex = IKChainCache.Num();
        IKChainCache.Add(ChainIndex, IKChainData);

        UE_LOG(LogAuracronRigTransformation, Log, TEXT("Successfully created IK chain '%s' with %d bones using solver type %d"), 
               *IKChainData.ChainName, IKChainData.BoneChain.Num(), (int32)IKChainData.SolverType);
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Exception creating IK chain: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronRigTransformation::SetupRetargeting(const FRetargetingData& RetargetingData)
{
    FScopeLock Lock(&RigTransformationMutex);

    if (!IsValidRetargetingData(RetargetingData))
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Invalid retargeting data"));
        return false;
    }

    try
    {
        // Create IK Retargeter using UE5.6 IK Retargeting system
        UIKRetargeter* IKRetargeter = NewObject<UIKRetargeter>();
        if (!IKRetargeter)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Failed to create IK Retargeter"));
            return false;
        }

        // Get source and target skeletal meshes
        USkeletalMesh* SourceMesh = GetSkeletalMeshByIndex(RetargetingData.SourceSkeletonIndex);
        USkeletalMesh* TargetMesh = GetSkeletalMeshByIndex(RetargetingData.TargetSkeletonIndex);

        if (!SourceMesh || !TargetMesh)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Source or target skeletal mesh not found"));
            return false;
        }

        // Set up IK Rig definitions using UE5.6 IK Rig system
        UIKRigDefinition* SourceIKRig = CreateIKRigFromSkeleton(SourceMesh->GetSkeleton());
        UIKRigDefinition* TargetIKRig = CreateIKRigFromSkeleton(TargetMesh->GetSkeleton());

        if (!SourceIKRig || !TargetIKRig)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Failed to create IK Rig definitions"));
            return false;
        }

        // Configure retargeter using UE5.6 retargeting APIs
        IKRetargeter->SetSourceIKRig(SourceIKRig);
        IKRetargeter->SetTargetIKRig(TargetIKRig);

        // Set up bone mapping using UE5.6 bone mapping system
        for (const auto& BoneMapping : RetargetingData.BoneMapping)
        {
            FName SourceBoneName(*BoneMapping.Key);
            FName TargetBoneName(*BoneMapping.Value);

            // Add bone mapping using UE5.6 retargeting chain system
            IKRetargeter->AddRetargetChain(SourceBoneName, TargetBoneName);
        }

        // Configure retargeting settings using UE5.6 retargeting configuration
        FRetargetGlobalSettings& GlobalSettings = IKRetargeter->GetGlobalSettingsUObject()->Settings;
        GlobalSettings.bEnableRoot = RetargetingData.bRetargetTranslation;
        GlobalSettings.bEnableFK = RetargetingData.bRetargetRotation;
        GlobalSettings.bEnableIK = true;

        // Set up retargeting poses using UE5.6 pose management
        if (RetargetingData.bRetargetScale)
        {
            GlobalSettings.bEnableRoot = true;
            // Configure scale retargeting
            IKRetargeter->SetGlobalScale(RetargetingData.ScaleFactor);
        }

        // Cache the retargeting configuration
        int32 RetargetingIndex = RetargetingCache.Num();
        RetargetingCache.Add(RetargetingIndex, RetargetingData);

        UE_LOG(LogAuracronRigTransformation, Log, TEXT("Successfully set up retargeting between skeletons %d and %d with %d bone mappings"),
               RetargetingData.SourceSkeletonIndex, RetargetingData.TargetSkeletonIndex, RetargetingData.BoneMapping.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Exception setting up retargeting: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronRigTransformation::ApplyRetargeting(int32 RetargetingIndex, const TArray<FTransform>& SourceTransforms, TArray<FTransform>& OutTargetTransforms)
{
    FScopeLock Lock(&RigTransformationMutex);

    if (!RetargetingCache.Contains(RetargetingIndex))
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Retargeting configuration %d not found"), RetargetingIndex);
        return false;
    }

    const FRetargetingData& RetargetingData = RetargetingCache[RetargetingIndex];

    try
    {
        // Get the IK Retargeter processor using UE5.6 retargeting system
        UIKRetargetProcessor* RetargetProcessor = NewObject<UIKRetargetProcessor>();
        if (!RetargetProcessor)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Failed to create retarget processor"));
            return false;
        }

        // Get source and target skeletal meshes
        USkeletalMesh* SourceMesh = GetSkeletalMeshByIndex(RetargetingData.SourceSkeletonIndex);
        USkeletalMesh* TargetMesh = GetSkeletalMeshByIndex(RetargetingData.TargetSkeletonIndex);

        if (!SourceMesh || !TargetMesh)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Source or target skeletal mesh not found"));
            return false;
        }

        // Initialize retargeting processor using UE5.6 initialization
        UIKRetargeter* IKRetargeter = GetRetargeterForIndex(RetargetingIndex);
        if (!IKRetargeter)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("IK Retargeter not found for index %d"), RetargetingIndex);
            return false;
        }

        RetargetProcessor->Initialize(
            SourceMesh->GetSkeleton(),
            TargetMesh->GetSkeleton(),
            IKRetargeter
        );

        // Prepare source pose data using UE5.6 pose management
        FRetargetSourcePoseData SourcePoseData;
        SourcePoseData.GlobalTransforms = SourceTransforms;

        // Apply retargeting using UE5.6 retargeting processor
        FRetargetResult RetargetResult;
        RetargetProcessor->ProcessRetargeting(SourcePoseData, RetargetResult);

        // Extract target transforms from result using UE5.6 result processing
        OutTargetTransforms = RetargetResult.TargetGlobalTransforms;

        // Apply scale factor if enabled
        if (RetargetingData.bRetargetScale && RetargetingData.ScaleFactor != 1.0f)
        {
            for (FTransform& Transform : OutTargetTransforms)
            {
                FVector Location = Transform.GetLocation() * RetargetingData.ScaleFactor;
                Transform.SetLocation(Location);
            }
        }

        UE_LOG(LogAuracronRigTransformation, Log, TEXT("Successfully applied retargeting for %d transforms"), SourceTransforms.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Exception applying retargeting: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

FRigValidationResult FAuracronRigTransformation::ValidateRigIntegrity(ERigValidationType ValidationType)
{
    FScopeLock Lock(&RigTransformationMutex);

    FRigValidationResult Result;
    Result.bIsValid = true;
    Result.PerformanceScore = 100.0f;
    Result.CompatibilityScore = 100.0f;

    try
    {
        USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();
        if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
        {
            Result.bIsValid = false;
            Result.Errors.Add(TEXT("No valid skeletal mesh or skeleton available"));
            return Result;
        }

        USkeleton* Skeleton = SkeletalMesh->GetSkeleton();
        const FReferenceSkeleton& RefSkeleton = Skeleton->GetReferenceSkeleton();

        Result.BoneCount = RefSkeleton.GetNum();
        Result.ConstraintCount = ConstraintCache.Num();
        Result.IKChainCount = IKChainCache.Num();

        // Perform validation based on type using UE5.6 validation systems
        switch (ValidationType)
        {
            case ERigValidationType::Basic:
            {
                // Basic validation using UE5.6 skeleton validation
                if (Result.BoneCount == 0)
                {
                    Result.bIsValid = false;
                    Result.Errors.Add(TEXT("Skeleton has no bones"));
                }

                // Check for valid root bone
                if (RefSkeleton.GetNum() > 0)
                {
                    int32 RootBoneIndex = 0;
                    int32 ParentIndex = RefSkeleton.GetParentIndex(RootBoneIndex);
                    if (ParentIndex != INDEX_NONE)
                    {
                        Result.Warnings.Add(TEXT("Root bone has a parent - unusual hierarchy"));
                    }
                }
                break;
            }

            case ERigValidationType::Hierarchy:
            {
                // Comprehensive hierarchy validation using UE5.6 hierarchy analysis
                TSet<int32> VisitedBones;
                TArray<int32> BoneStack;

                // Check for circular dependencies using depth-first search
                for (int32 BoneIndex = 0; BoneIndex < RefSkeleton.GetNum(); ++BoneIndex)
                {
                    if (!VisitedBones.Contains(BoneIndex))
                    {
                        BoneStack.Empty();
                        if (HasCircularDependency(BoneIndex, RefSkeleton, VisitedBones, BoneStack))
                        {
                            Result.bIsValid = false;
                            Result.Errors.Add(FString::Printf(TEXT("Circular dependency detected in bone hierarchy starting at bone %d"), BoneIndex));
                        }
                    }
                }

                // Validate bone transforms using UE5.6 transform validation
                for (int32 BoneIndex = 0; BoneIndex < RefSkeleton.GetNum(); ++BoneIndex)
                {
                    const FTransform& BoneTransform = RefSkeleton.GetRefBonePose()[BoneIndex];

                    if (!BoneTransform.IsValid())
                    {
                        Result.bIsValid = false;
                        Result.Errors.Add(FString::Printf(TEXT("Invalid transform for bone %d"), BoneIndex));
                    }

                    if (BoneTransform.ContainsNaN())
                    {
                        Result.bIsValid = false;
                        Result.Errors.Add(FString::Printf(TEXT("NaN values in transform for bone %d"), BoneIndex));
                    }
                }
                break;
            }

            case ERigValidationType::Constraints:
            {
                // Validate all constraints using UE5.6 constraint validation
                for (const auto& ConstraintPair : ConstraintCache)
                {
                    const FConstraintData& Constraint = ConstraintPair.Value;

                    if (!IsValidBoneIndex(Constraint.SourceBoneIndex))
                    {
                        Result.bIsValid = false;
                        Result.Errors.Add(FString::Printf(TEXT("Invalid source bone index %d in constraint"), Constraint.SourceBoneIndex));
                    }

                    if (!IsValidBoneIndex(Constraint.TargetBoneIndex))
                    {
                        Result.bIsValid = false;
                        Result.Errors.Add(FString::Printf(TEXT("Invalid target bone index %d in constraint"), Constraint.TargetBoneIndex));
                    }

                    if (Constraint.Weight < 0.0f || Constraint.Weight > 1.0f)
                    {
                        Result.Warnings.Add(FString::Printf(TEXT("Constraint weight %f is outside normal range [0,1]"), Constraint.Weight));
                    }
                }
                break;
            }

            case ERigValidationType::IKChains:
            {
                // Validate all IK chains using UE5.6 IK validation
                for (const auto& IKChainPair : IKChainCache)
                {
                    const FIKChainData& IKChain = IKChainPair.Value;

                    if (IKChain.BoneChain.Num() < 2)
                    {
                        Result.bIsValid = false;
                        Result.Errors.Add(FString::Printf(TEXT("IK chain '%s' has insufficient bones"), *IKChain.ChainName));
                    }

                    for (int32 BoneIndex : IKChain.BoneChain)
                    {
                        if (!IsValidBoneIndex(BoneIndex))
                        {
                            Result.bIsValid = false;
                            Result.Errors.Add(FString::Printf(TEXT("Invalid bone index %d in IK chain '%s'"), BoneIndex, *IKChain.ChainName));
                        }
                    }

                    // Validate IK chain connectivity
                    if (!ValidateIKChainConnectivity(IKChain, RefSkeleton))
                    {
                        Result.Warnings.Add(FString::Printf(TEXT("IK chain '%s' bones are not properly connected"), *IKChain.ChainName));
                    }
                }
                break;
            }

            case ERigValidationType::Comprehensive:
            {
                // Perform all validation types
                FRigValidationResult BasicResult = ValidateRigIntegrity(ERigValidationType::Basic);
                FRigValidationResult HierarchyResult = ValidateRigIntegrity(ERigValidationType::Hierarchy);
                FRigValidationResult ConstraintResult = ValidateRigIntegrity(ERigValidationType::Constraints);
                FRigValidationResult IKResult = ValidateRigIntegrity(ERigValidationType::IKChains);

                // Combine results
                Result.bIsValid = BasicResult.bIsValid && HierarchyResult.bIsValid && ConstraintResult.bIsValid && IKResult.bIsValid;
                Result.Errors.Append(BasicResult.Errors);
                Result.Errors.Append(HierarchyResult.Errors);
                Result.Errors.Append(ConstraintResult.Errors);
                Result.Errors.Append(IKResult.Errors);
                Result.Warnings.Append(BasicResult.Warnings);
                Result.Warnings.Append(HierarchyResult.Warnings);
                Result.Warnings.Append(ConstraintResult.Warnings);
                Result.Warnings.Append(IKResult.Warnings);
                break;
            }
        }

        // Calculate performance and compatibility scores using UE5.6 metrics
        Result.PerformanceScore = CalculateRigPerformanceScore();
        Result.CompatibilityScore = CalculateRigCompatibilityScore();

        UE_LOG(LogAuracronRigTransformation, Log, TEXT("Rig validation completed: %s (Performance: %.1f%%, Compatibility: %.1f%%)"),
               Result.bIsValid ? TEXT("PASSED") : TEXT("FAILED"), Result.PerformanceScore, Result.CompatibilityScore);
    }
    catch (const std::exception& e)
    {
        Result.bIsValid = false;
        Result.Errors.Add(FString::Printf(TEXT("Exception during rig validation: %s"), UTF8_TO_TCHAR(e.what())));
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Exception validating rig integrity: %s"), UTF8_TO_TCHAR(e.what()));
    }

    return Result;
}

// ========================================
// Helper Methods Implementation
// ========================================

bool FAuracronRigTransformation::IsValidBoneIndex(int32 BoneIndex) const
{
    USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();
    if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
    {
        return false;
    }

    const FReferenceSkeleton& RefSkeleton = SkeletalMesh->GetSkeleton()->GetReferenceSkeleton();
    return BoneIndex >= 0 && BoneIndex < RefSkeleton.GetNum();
}

bool FAuracronRigTransformation::IsValidBoneScalingData(const FBoneScalingData& ScalingData) const
{
    if (!IsValidBoneIndex(ScalingData.BoneIndex))
    {
        return false;
    }

    if (ScalingData.ScaleFactor.ContainsNaN() || !ScalingData.ScaleFactor.IsFinite())
    {
        return false;
    }

    // Check for zero or negative scale values
    if (ScalingData.ScaleFactor.X <= 0.0f || ScalingData.ScaleFactor.Y <= 0.0f || ScalingData.ScaleFactor.Z <= 0.0f)
    {
        return false;
    }

    return true;
}

bool FAuracronRigTransformation::IsValidConstraintData(const FConstraintData& ConstraintData) const
{
    if (!IsValidBoneIndex(ConstraintData.SourceBoneIndex) || !IsValidBoneIndex(ConstraintData.TargetBoneIndex))
    {
        return false;
    }

    if (ConstraintData.SourceBoneIndex == ConstraintData.TargetBoneIndex)
    {
        return false; // Cannot constrain bone to itself
    }

    if (ConstraintData.Weight < 0.0f || ConstraintData.Weight > 1.0f)
    {
        return false;
    }

    return true;
}

bool FAuracronRigTransformation::IsValidIKChainData(const FIKChainData& IKChainData) const
{
    if (IKChainData.BoneChain.Num() < 2)
    {
        return false;
    }

    for (int32 BoneIndex : IKChainData.BoneChain)
    {
        if (!IsValidBoneIndex(BoneIndex))
        {
            return false;
        }
    }

    if (IKChainData.Weight < 0.0f || IKChainData.Weight > 1.0f)
    {
        return false;
    }

    if (IKChainData.MaxIterations <= 0 || IKChainData.MaxIterations > 100)
    {
        return false;
    }

    if (IKChainData.Precision <= 0.0f)
    {
        return false;
    }

    return true;
}

bool FAuracronRigTransformation::IsValidRetargetingData(const FRetargetingData& RetargetingData) const
{
    if (RetargetingData.SourceSkeletonIndex < 0 || RetargetingData.TargetSkeletonIndex < 0)
    {
        return false;
    }

    if (RetargetingData.SourceSkeletonIndex == RetargetingData.TargetSkeletonIndex)
    {
        return false; // Cannot retarget to same skeleton
    }

    if (RetargetingData.BoneMapping.Num() == 0)
    {
        return false; // Need at least one bone mapping
    }

    if (RetargetingData.ScaleFactor <= 0.0f)
    {
        return false;
    }

    return true;
}

USkeletalMesh* FAuracronRigTransformation::GetCurrentSkeletalMesh() const
{
    // In a real implementation, this would get the current skeletal mesh
    // from the MetaHuman system using UE5.6 asset management APIs
    // This is a framework method that would be implemented based on
    // the specific skeletal mesh storage and access system
    return nullptr;
}

USkeletalMesh* FAuracronRigTransformation::GetSkeletalMeshByIndex(int32 SkeletonIndex) const
{
    // In a real implementation, this would retrieve a skeletal mesh by index
    // from the MetaHuman system using UE5.6 asset management APIs
    return nullptr;
}

UControlRig* FAuracronRigTransformation::GetOrCreateControlRig() const
{
    // In a real implementation, this would get or create a Control Rig
    // using UE5.6 Control Rig creation APIs
    USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();
    if (!SkeletalMesh)
    {
        return nullptr;
    }

    // Create Control Rig Blueprint using UE5.6 Control Rig system
    UControlRigBlueprint* ControlRigBP = NewObject<UControlRigBlueprint>();
    if (!ControlRigBP)
    {
        return nullptr;
    }

    // Set up Control Rig with skeleton using UE5.6 initialization
    ControlRigBP->SetObjectBeingDebugged(SkeletalMesh);

    // Get the Control Rig instance using UE5.6 Control Rig instantiation
    UControlRig* ControlRig = ControlRigBP->GetControlRigClass()->GetDefaultObject<UControlRig>();

    return ControlRig;
}

FRigElementKey FAuracronRigTransformation::GetBoneElementKey(int32 BoneIndex) const
{
    USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();
    if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
    {
        return FRigElementKey();
    }

    const FReferenceSkeleton& RefSkeleton = SkeletalMesh->GetSkeleton()->GetReferenceSkeleton();
    if (BoneIndex < 0 || BoneIndex >= RefSkeleton.GetNum())
    {
        return FRigElementKey();
    }

    FName BoneName = RefSkeleton.GetBoneName(BoneIndex);
    return FRigElementKey(BoneName, ERigElementType::Bone);
}

UIKRigDefinition* FAuracronRigTransformation::CreateIKRigFromSkeleton(USkeleton* Skeleton) const
{
    if (!Skeleton)
    {
        return nullptr;
    }

    // Create IK Rig Definition using UE5.6 IK Rig system
    UIKRigDefinition* IKRigDefinition = NewObject<UIKRigDefinition>();
    if (!IKRigDefinition)
    {
        return nullptr;
    }

    // Set up IK Rig with skeleton using UE5.6 IK Rig initialization
    IKRigDefinition->SetPreviewMesh(Skeleton->GetPreviewMesh());

    // Add bones to IK Rig using UE5.6 bone management
    const FReferenceSkeleton& RefSkeleton = Skeleton->GetReferenceSkeleton();
    for (int32 BoneIndex = 0; BoneIndex < RefSkeleton.GetNum(); ++BoneIndex)
    {
        FName BoneName = RefSkeleton.GetBoneName(BoneIndex);
        IKRigDefinition->GetSkeleton()->AddBone(BoneName, BoneIndex, RefSkeleton.GetParentIndex(BoneIndex));
    }

    return IKRigDefinition;
}

UIKRetargeter* FAuracronRigTransformation::GetRetargeterForIndex(int32 RetargetingIndex) const
{
    // In a real implementation, this would retrieve the cached retargeter
    // using UE5.6 retargeting management APIs
    return nullptr;
}

FVector FAuracronRigTransformation::CalculateProportionalScale(const FVector& OriginalScale, const FVector& TargetScale, EBoneScalingType ScalingType) const
{
    switch (ScalingType)
    {
        case EBoneScalingType::Uniform:
        {
            // Use the average of target scale components for uniform scaling
            float UniformScale = (TargetScale.X + TargetScale.Y + TargetScale.Z) / 3.0f;
            return FVector(UniformScale);
        }

        case EBoneScalingType::NonUniform:
        {
            // Use target scale directly for non-uniform scaling
            return TargetScale;
        }

        case EBoneScalingType::Proportional:
        {
            // Scale proportionally based on original scale ratios
            FVector ScaleRatio = TargetScale / OriginalScale;
            float AverageRatio = (ScaleRatio.X + ScaleRatio.Y + ScaleRatio.Z) / 3.0f;
            return OriginalScale * AverageRatio;
        }

        case EBoneScalingType::Hierarchical:
        {
            // Apply hierarchical scaling that considers parent bone scales
            // This would typically involve traversing the bone hierarchy
            // and applying cumulative scaling effects
            return TargetScale * 0.9f; // Slight reduction for hierarchical effect
        }

        default:
            return TargetScale;
    }
}

bool FAuracronRigTransformation::ApplyBoneScaleRecursive(int32 BoneIndex, const FVector& ScaleFactor, const TArray<int32>& ExcludedBones) const
{
    if (ExcludedBones.Contains(BoneIndex))
    {
        return true; // Skip excluded bones
    }

    USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();
    if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
    {
        return false;
    }

    const FReferenceSkeleton& RefSkeleton = SkeletalMesh->GetSkeleton()->GetReferenceSkeleton();

    // Apply scale to current bone
    if (BoneIndex >= 0 && BoneIndex < RefSkeleton.GetNum())
    {
        // Get current bone transform
        FTransform BoneTransform = RefSkeleton.GetRefBonePose()[BoneIndex];

        // Apply scale factor
        FVector CurrentScale = BoneTransform.GetScale3D();
        FVector NewScale = CurrentScale * ScaleFactor;
        BoneTransform.SetScale3D(NewScale);

        // Update the reference pose
        FReferenceSkeletonModifier SkeletonModifier(const_cast<FReferenceSkeleton&>(RefSkeleton), SkeletalMesh->GetSkeleton());
        SkeletonModifier.UpdateRefPoseTransform(BoneIndex, BoneTransform);
    }

    // Recursively apply to children using UE5.6 hierarchy traversal
    for (int32 ChildIndex = 0; ChildIndex < RefSkeleton.GetNum(); ++ChildIndex)
    {
        if (RefSkeleton.GetParentIndex(ChildIndex) == BoneIndex)
        {
            ApplyBoneScaleRecursive(ChildIndex, ScaleFactor, ExcludedBones);
        }
    }

    return true;
}

float FAuracronRigTransformation::CalculateRigPerformanceScore() const
{
    USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();
    if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
    {
        return 0.0f;
    }

    float PerformanceScore = 100.0f;
    const FReferenceSkeleton& RefSkeleton = SkeletalMesh->GetSkeleton()->GetReferenceSkeleton();

    // Analyze bone count impact on performance using UE5.6 performance metrics
    int32 BoneCount = RefSkeleton.GetNum();
    if (BoneCount > 300)
    {
        PerformanceScore -= 30.0f; // High bone count significantly impacts performance
    }
    else if (BoneCount > 150)
    {
        PerformanceScore -= 15.0f; // Moderate bone count impact
    }
    else if (BoneCount > 75)
    {
        PerformanceScore -= 5.0f; // Minor bone count impact
    }

    // Analyze constraint complexity using UE5.6 constraint analysis
    int32 ConstraintCount = ConstraintCache.Num();
    if (ConstraintCount > 50)
    {
        PerformanceScore -= 20.0f; // Many constraints impact performance
    }
    else if (ConstraintCount > 20)
    {
        PerformanceScore -= 10.0f; // Moderate constraint impact
    }

    // Analyze IK chain complexity using UE5.6 IK performance analysis
    int32 IKChainCount = IKChainCache.Num();
    int32 TotalIKBones = 0;

    for (const auto& IKChainPair : IKChainCache)
    {
        const FIKChainData& IKChain = IKChainPair.Value;
        TotalIKBones += IKChain.BoneChain.Num();

        // Complex IK solvers have higher performance cost
        switch (IKChain.SolverType)
        {
            case EIKSolverType::FullBody:
                PerformanceScore -= 10.0f; // Full-body IK is expensive
                break;
            case EIKSolverType::FABRIK:
                PerformanceScore -= 5.0f; // FABRIK is moderately expensive
                break;
            case EIKSolverType::CCDIK:
                PerformanceScore -= 3.0f; // CCD IK is less expensive
                break;
            case EIKSolverType::TwoBone:
                PerformanceScore -= 1.0f; // Two-bone IK is least expensive
                break;
        }
    }

    // Factor in total IK bones
    if (TotalIKBones > 100)
    {
        PerformanceScore -= 15.0f;
    }
    else if (TotalIKBones > 50)
    {
        PerformanceScore -= 8.0f;
    }

    // Analyze bone hierarchy depth using UE5.6 hierarchy analysis
    int32 MaxDepth = CalculateMaxBoneHierarchyDepth(RefSkeleton);
    if (MaxDepth > 20)
    {
        PerformanceScore -= 10.0f; // Deep hierarchies can impact performance
    }
    else if (MaxDepth > 15)
    {
        PerformanceScore -= 5.0f;
    }

    return FMath::Max(0.0f, PerformanceScore);
}

float FAuracronRigTransformation::CalculateRigCompatibilityScore() const
{
    USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();
    if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
    {
        return 0.0f;
    }

    float CompatibilityScore = 100.0f;
    const FReferenceSkeleton& RefSkeleton = SkeletalMesh->GetSkeleton()->GetReferenceSkeleton();

    // Check for standard bone naming conventions using UE5.6 naming validation
    TArray<FString> StandardBoneNames = {
        TEXT("Root"), TEXT("Pelvis"), TEXT("Spine"), TEXT("Chest"), TEXT("Neck"), TEXT("Head"),
        TEXT("LeftShoulder"), TEXT("LeftArm"), TEXT("LeftForeArm"), TEXT("LeftHand"),
        TEXT("RightShoulder"), TEXT("RightArm"), TEXT("RightForeArm"), TEXT("RightHand"),
        TEXT("LeftUpLeg"), TEXT("LeftLeg"), TEXT("LeftFoot"), TEXT("LeftToe"),
        TEXT("RightUpLeg"), TEXT("RightLeg"), TEXT("RightFoot"), TEXT("RightToe")
    };

    int32 StandardBonesFound = 0;
    for (const FString& StandardBoneName : StandardBoneNames)
    {
        for (int32 BoneIndex = 0; BoneIndex < RefSkeleton.GetNum(); ++BoneIndex)
        {
            FString BoneName = RefSkeleton.GetBoneName(BoneIndex).ToString();
            if (BoneName.Contains(StandardBoneName))
            {
                StandardBonesFound++;
                break;
            }
        }
    }

    // Calculate compatibility based on standard bone presence
    float StandardBoneRatio = static_cast<float>(StandardBonesFound) / static_cast<float>(StandardBoneNames.Num());
    CompatibilityScore *= StandardBoneRatio;

    // Check for common animation system compatibility using UE5.6 animation validation
    bool bHasRootMotion = CheckForRootMotionCompatibility(RefSkeleton);
    if (!bHasRootMotion)
    {
        CompatibilityScore -= 10.0f; // Reduced compatibility without root motion support
    }

    // Check for retargeting compatibility using UE5.6 retargeting validation
    bool bRetargetingCompatible = CheckRetargetingCompatibility(RefSkeleton);
    if (!bRetargetingCompatible)
    {
        CompatibilityScore -= 15.0f; // Reduced compatibility for retargeting
    }

    // Check for Control Rig compatibility using UE5.6 Control Rig validation
    bool bControlRigCompatible = CheckControlRigCompatibility(RefSkeleton);
    if (!bControlRigCompatible)
    {
        CompatibilityScore -= 10.0f; // Reduced compatibility for Control Rig
    }

    return FMath::Max(0.0f, CompatibilityScore);
}

bool FAuracronRigTransformation::HasCircularDependency(int32 BoneIndex, const FReferenceSkeleton& RefSkeleton, TSet<int32>& VisitedBones, TArray<int32>& BoneStack) const
{
    if (BoneStack.Contains(BoneIndex))
    {
        return true; // Circular dependency found
    }

    if (VisitedBones.Contains(BoneIndex))
    {
        return false; // Already processed this bone
    }

    VisitedBones.Add(BoneIndex);
    BoneStack.Add(BoneIndex);

    // Check parent bone using UE5.6 hierarchy traversal
    int32 ParentIndex = RefSkeleton.GetParentIndex(BoneIndex);
    if (ParentIndex != INDEX_NONE)
    {
        if (HasCircularDependency(ParentIndex, RefSkeleton, VisitedBones, BoneStack))
        {
            return true;
        }
    }

    BoneStack.Remove(BoneIndex);
    return false;
}

bool FAuracronRigTransformation::ValidateIKChainConnectivity(const FIKChainData& IKChain, const FReferenceSkeleton& RefSkeleton) const
{
    if (IKChain.BoneChain.Num() < 2)
    {
        return false;
    }

    // Check if bones in the chain are connected using UE5.6 hierarchy validation
    for (int32 i = 1; i < IKChain.BoneChain.Num(); ++i)
    {
        int32 CurrentBone = IKChain.BoneChain[i];
        int32 PreviousBone = IKChain.BoneChain[i - 1];

        // Check if current bone is a child of previous bone or vice versa
        bool bConnected = false;

        // Check direct parent-child relationship
        if (RefSkeleton.GetParentIndex(CurrentBone) == PreviousBone ||
            RefSkeleton.GetParentIndex(PreviousBone) == CurrentBone)
        {
            bConnected = true;
        }
        else
        {
            // Check if bones are connected through intermediate bones
            bConnected = AreBonesCo nnected(PreviousBone, CurrentBone, RefSkeleton);
        }

        if (!bConnected)
        {
            return false;
        }
    }

    return true;
}

bool FAuracronRigTransformation::AreBonesConnected(int32 Bone1, int32 Bone2, const FReferenceSkeleton& RefSkeleton) const
{
    // Traverse up from Bone1 to see if we reach Bone2
    int32 CurrentBone = Bone1;
    while (CurrentBone != INDEX_NONE)
    {
        if (CurrentBone == Bone2)
        {
            return true;
        }
        CurrentBone = RefSkeleton.GetParentIndex(CurrentBone);
    }

    // Traverse up from Bone2 to see if we reach Bone1
    CurrentBone = Bone2;
    while (CurrentBone != INDEX_NONE)
    {
        if (CurrentBone == Bone1)
        {
            return true;
        }
        CurrentBone = RefSkeleton.GetParentIndex(CurrentBone);
    }

    return false;
}

int32 FAuracronRigTransformation::CalculateMaxBoneHierarchyDepth(const FReferenceSkeleton& RefSkeleton) const
{
    int32 MaxDepth = 0;

    for (int32 BoneIndex = 0; BoneIndex < RefSkeleton.GetNum(); ++BoneIndex)
    {
        int32 Depth = CalculateBoneDepth(BoneIndex, RefSkeleton);
        MaxDepth = FMath::Max(MaxDepth, Depth);
    }

    return MaxDepth;
}

int32 FAuracronRigTransformation::CalculateBoneDepth(int32 BoneIndex, const FReferenceSkeleton& RefSkeleton) const
{
    int32 Depth = 0;
    int32 CurrentBone = BoneIndex;

    while (CurrentBone != INDEX_NONE)
    {
        CurrentBone = RefSkeleton.GetParentIndex(CurrentBone);
        if (CurrentBone != INDEX_NONE)
        {
            Depth++;
        }
    }

    return Depth;
}

bool FAuracronRigTransformation::CheckForRootMotionCompatibility(const FReferenceSkeleton& RefSkeleton) const
{
    // Check if skeleton has a proper root bone for root motion using UE5.6 root motion validation
    if (RefSkeleton.GetNum() == 0)
    {
        return false;
    }

    // Root bone should have no parent
    int32 RootBoneIndex = 0;
    return RefSkeleton.GetParentIndex(RootBoneIndex) == INDEX_NONE;
}

bool FAuracronRigTransformation::CheckRetargetingCompatibility(const FReferenceSkeleton& RefSkeleton) const
{
    // Check if skeleton has minimum required bones for retargeting using UE5.6 retargeting validation
    TArray<FString> RequiredBones = {
        TEXT("Pelvis"), TEXT("Spine"), TEXT("Head"),
        TEXT("LeftArm"), TEXT("LeftForeArm"), TEXT("LeftHand"),
        TEXT("RightArm"), TEXT("RightForeArm"), TEXT("RightHand"),
        TEXT("LeftUpLeg"), TEXT("LeftLeg"), TEXT("LeftFoot"),
        TEXT("RightUpLeg"), TEXT("RightLeg"), TEXT("RightFoot")
    };

    int32 RequiredBonesFound = 0;
    for (const FString& RequiredBone : RequiredBones)
    {
        for (int32 BoneIndex = 0; BoneIndex < RefSkeleton.GetNum(); ++BoneIndex)
        {
            FString BoneName = RefSkeleton.GetBoneName(BoneIndex).ToString();
            if (BoneName.Contains(RequiredBone))
            {
                RequiredBonesFound++;
                break;
            }
        }
    }

    // Need at least 80% of required bones for good retargeting compatibility
    return RequiredBonesFound >= (RequiredBones.Num() * 0.8f);
}

bool FAuracronRigTransformation::CheckControlRigCompatibility(const FReferenceSkeleton& RefSkeleton) const
{
    // Check if skeleton is compatible with Control Rig system using UE5.6 Control Rig validation
    if (RefSkeleton.GetNum() == 0)
    {
        return false;
    }

    // Check for valid bone transforms
    for (int32 BoneIndex = 0; BoneIndex < RefSkeleton.GetNum(); ++BoneIndex)
    {
        const FTransform& BoneTransform = RefSkeleton.GetRefBonePose()[BoneIndex];
        if (!BoneTransform.IsValid() || BoneTransform.ContainsNaN())
        {
            return false;
        }
    }

    return true;
}

void FAuracronRigTransformation::InvalidateRigTransformationCache() const
{
    FScopeLock Lock(&RigTransformationMutex);
    bRigTransformationCacheValid = false;
}
