using UnrealBuildTool;
public class AuracronAdaptiveCreaturesBridge : ModuleRules
{
    public AuracronAdaptiveCreaturesBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "GameplayTasks",
                "NavigationSystem",
                "MassEntity",
                "MassEntityTestSuite",
                "MassEntity",
                "MassMovement",
                "MassNavigation",
                "MassReplication",
                "MassSimulation",
                "MassSpawner",
                "StateTreeModule",

                "GameplayStateTreeModule",
                "StructUtils",
                "SmartObjectsModule",
                "ZoneGraph",
                "ZoneGraphAnnotations",
                "UMG",
                "Slate",
                "SlateCore",
                "Json",

                "MassEntity"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "RenderCore",
                "RH<PERSON>",
                "Chaos<PERSON><PERSON>",
                "Physics<PERSON>ore",
                "NiagaraCore",
                "NiagaraShader",
                "CinematicCamera",
                "MovieScene",
                "MovieSceneTracks",
                "AnimGraphRuntime",
                "AnimationCore"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "ToolMenus",
                    "EditorStyle",
                    "EditorWidgets",
                    "UnrealEd",
                    "LevelEditor",
                    "PropertyEditor",
                    "DetailCustomizations",
                    "ComponentVisualizers",
                    "StateTreeEditorModule"
                }
            );
        }
        // Enable RTTI for this module
        bUseRTTI = true;
        // Enable exceptions for this module
        bEnableExceptions = true;
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        // Include paths
        PublicIncludePaths.AddRange(new string[] 
        {
            "AuracronAdaptiveCreaturesBridge/Public"
        });
        PrivateIncludePaths.AddRange(new string[]
        {
            "AuracronAdaptiveCreaturesBridge/Private"
        });

        // C++ standard - Using Cpp20 for UE 5.6 compatibility
        CppStandard = CppStandardVersion.Cpp20;
    }
}


