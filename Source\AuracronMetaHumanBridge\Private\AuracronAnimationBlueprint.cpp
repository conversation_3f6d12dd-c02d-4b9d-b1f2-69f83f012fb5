#include "AuracronAnimationBlueprint.h"
#include "AuracronMetaHumanBridge.h"
#include "Engine/Engine.h"
#include "Animation/AnimBlueprint.h"
#include "Animation/AnimBlueprintGeneratedClass.h"
#include "Animation/AnimInstance.h"
#include "Animation/AnimSequence.h"
#include "Animation/AnimMontage.h"
#include "Animation/BlendSpace.h"
#include "Animation/BlendSpace1D.h"
#include "Animation/BlendSpace2D.h"
#include "Animation/AnimStateMachine.h"
#include "Animation/AnimStateNode.h"
#include "Animation/AnimStateTransitionNode.h"
#include "BlueprintGraph/Classes/K2Node_Event.h"
#include "BlueprintGraph/Classes/K2Node_CallFunction.h"
#include "BlueprintGraph/Classes/K2Node_VariableGet.h"
#include "BlueprintGraph/Classes/K2Node_VariableSet.h"
#include "Engine/Blueprint.h"
#include "EdGraph/EdGraph.h"
#include "EdGraph/EdGraphNode.h"
#include "EdGraph/EdGraphPin.h"
#include "KismetCompiler.h"
#include "BlueprintCompilerCppBackend.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Kismet2/KismetEditorUtilities.h"
#include "AnimationBlueprintLibrary.h"
#include "AnimGraphNode_Base.h"
#include "AnimGraphNode_StateMachine.h"
#include "AnimGraphNode_BlendSpacePlayer.h"
#include "AnimGraphNode_SequencePlayer.h"
#include "Async/Async.h"

DEFINE_LOG_CATEGORY(LogAuracronAnimationBlueprint);

// ========================================
// FAuracronAnimationBlueprint Implementation
// ========================================

FAuracronAnimationBlueprint::FAuracronAnimationBlueprint()
    : AnimBlueprintCacheMemoryUsage(0)
    , TotalAnimBlueprintGenerationTime(0.0f)
{
}

FAuracronAnimationBlueprint::~FAuracronAnimationBlueprint()
{
    ClearAnimBlueprintCache();
}

UAnimBlueprint* FAuracronAnimationBlueprint::GenerateAnimationBlueprint(const FAnimBlueprintGenerationParameters& Parameters)
{
    FScopeLock Lock(&AnimBlueprintGenerationMutex);

    FString ValidationError;
    if (!ValidateAnimBlueprintGenerationParameters(Parameters, ValidationError))
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Invalid animation blueprint generation parameters: %s"), *ValidationError);
        return nullptr;
    }

    double StartTime = FPlatformTime::Seconds();

    try
    {
        // Generate cache key for animation blueprint using UE5.6 hashing
        FString CacheKey = CalculateAnimBlueprintGenerationHash(Parameters);
        
        // Check cache first using UE5.6 caching system
        if (AnimBlueprintCache.Contains(CacheKey))
        {
            TWeakObjectPtr<UAnimBlueprint> CachedBlueprint = AnimBlueprintCache[CacheKey];
            if (CachedBlueprint.IsValid())
            {
                UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Returning cached animation blueprint for key: %s"), *CacheKey);
                return CachedBlueprint.Get();
            }
            else
            {
                // Remove invalid cache entry
                AnimBlueprintCache.Remove(CacheKey);
            }
        }

        // Create new animation blueprint using UE5.6 animation blueprint system
        UAnimBlueprint* NewAnimBlueprint = FKismetEditorUtilities::CreateBlueprint(
            UAnimInstance::StaticClass(),
            GetTransientPackage(),
            *Parameters.BlueprintName,
            BPTYPE_Normal,
            UAnimBlueprint::StaticClass(),
            UAnimBlueprintGeneratedClass::StaticClass()
        );

        if (!NewAnimBlueprint)
        {
            UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Failed to create animation blueprint"));
            return nullptr;
        }

        // Set target skeleton using UE5.6 skeleton assignment
        if (Parameters.TargetSkeleton)
        {
            NewAnimBlueprint->TargetSkeleton = Parameters.TargetSkeleton;
        }

        // Initialize animation blueprint with UE5.6 blueprint initialization
        if (!InitializeAnimationBlueprint(NewAnimBlueprint, Parameters))
        {
            UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Failed to initialize animation blueprint"));
            return nullptr;
        }

        // Create animation graph using UE5.6 animation graph system
        if (!CreateAnimationGraph(NewAnimBlueprint, Parameters.AnimGraphData))
        {
            UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Failed to create animation graph"));
            return nullptr;
        }

        // Create state machine if requested using UE5.6 state machine system
        if (Parameters.bCreateStateMachine && Parameters.StateMachineData.States.Num() > 0)
        {
            CreateStateMachine(NewAnimBlueprint, Parameters.StateMachineData);
        }

        // Add blend spaces if provided using UE5.6 blend space system
        if (Parameters.BlendSpaceData.BlendSpaces.Num() > 0)
        {
            AddBlendSpaces(NewAnimBlueprint, Parameters.BlendSpaceData);
        }

        // Add animation sequences if provided using UE5.6 animation sequence system
        if (Parameters.AnimSequenceData.AnimSequences.Num() > 0)
        {
            AddAnimationSequences(NewAnimBlueprint, Parameters.AnimSequenceData);
        }

        // Create custom events if requested using UE5.6 event system
        if (Parameters.CustomEventData.Events.Num() > 0)
        {
            CreateCustomEvents(NewAnimBlueprint, Parameters.CustomEventData);
        }

        // Add variables if provided using UE5.6 variable system
        if (Parameters.VariableData.Variables.Num() > 0)
        {
            AddBlueprintVariables(NewAnimBlueprint, Parameters.VariableData);
        }

        // Compile the blueprint using UE5.6 blueprint compilation
        if (!CompileAnimationBlueprint(NewAnimBlueprint))
        {
            UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Failed to compile animation blueprint"));
            return nullptr;
        }

        // Cache the result using UE5.6 caching system
        AnimBlueprintCache.Add(CacheKey, NewAnimBlueprint);
        UpdateAnimBlueprintCacheStats();

        // Update generation statistics
        double GenerationTime = FPlatformTime::Seconds() - StartTime;
        TotalAnimBlueprintGenerationTime += GenerationTime;
        UpdateAnimBlueprintGenerationStats(TEXT("GenerateAnimationBlueprint"), GenerationTime, true);

        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Successfully generated animation blueprint '%s' in %.3f seconds"), 
               *Parameters.BlueprintName, GenerationTime);

        return NewAnimBlueprint;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception generating animation blueprint: %s"), UTF8_TO_TCHAR(e.what()));
        UpdateAnimBlueprintGenerationStats(TEXT("GenerateAnimationBlueprint"), FPlatformTime::Seconds() - StartTime, false);
        return nullptr;
    }
}

bool FAuracronAnimationBlueprint::InitializeAnimationBlueprint(UAnimBlueprint* AnimBlueprint, const FAnimBlueprintGenerationParameters& Parameters)
{
    if (!AnimBlueprint)
    {
        return false;
    }

    try
    {
        // Set blueprint properties using UE5.6 blueprint APIs
        AnimBlueprint->BlueprintDescription = Parameters.BlueprintDescription;
        AnimBlueprint->BlueprintCategory = Parameters.BlueprintCategory;

        // Initialize blueprint graphs using UE5.6 graph system
        if (!AnimBlueprint->UbergraphPages.Num())
        {
            // Create default event graph using UE5.6 graph creation
            UEdGraph* EventGraph = FBlueprintEditorUtils::CreateNewGraph(
                AnimBlueprint,
                FBlueprintEditorUtils::FindUniqueKismetName(AnimBlueprint, TEXT("EventGraph")),
                UEdGraph::StaticClass(),
                UEdGraphSchema_K2::StaticClass()
            );

            if (EventGraph)
            {
                AnimBlueprint->UbergraphPages.Add(EventGraph);
                FBlueprintEditorUtils::AddUbergraphPage(AnimBlueprint, EventGraph);
            }
        }

        // Initialize animation graph using UE5.6 animation graph system
        if (!AnimBlueprint->FunctionGraphs.Num())
        {
            // Create animation graph using UE5.6 animation graph creation
            UEdGraph* AnimGraph = FBlueprintEditorUtils::CreateNewGraph(
                AnimBlueprint,
                TEXT("AnimGraph"),
                UAnimationGraph::StaticClass(),
                UAnimationGraphSchema::StaticClass()
            );

            if (AnimGraph)
            {
                AnimBlueprint->FunctionGraphs.Add(AnimGraph);
            }
        }

        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Successfully initialized animation blueprint"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception initializing animation blueprint: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronAnimationBlueprint::CreateAnimationGraph(UAnimBlueprint* AnimBlueprint, const FAnimGraphData& AnimGraphData)
{
    if (!AnimBlueprint)
    {
        return false;
    }

    try
    {
        // Get or create animation graph using UE5.6 animation graph system
        UEdGraph* AnimGraph = nullptr;
        for (UEdGraph* Graph : AnimBlueprint->FunctionGraphs)
        {
            if (Graph && Graph->GetClass()->IsChildOf(UAnimationGraph::StaticClass()))
            {
                AnimGraph = Graph;
                break;
            }
        }

        if (!AnimGraph)
        {
            UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("No animation graph found"));
            return false;
        }

        // Create output pose node using UE5.6 animation node system
        UAnimGraphNode_Root* OutputNode = NewObject<UAnimGraphNode_Root>(AnimGraph);
        if (OutputNode)
        {
            AnimGraph->AddNode(OutputNode, true);
            OutputNode->AllocateDefaultPins();
            OutputNode->ReconstructNode();
        }

        // Add animation nodes based on graph data using UE5.6 node creation
        for (const FAnimNodeData& NodeData : AnimGraphData.AnimNodes)
        {
            CreateAnimationNode(AnimGraph, NodeData);
        }

        // Connect nodes based on connection data using UE5.6 pin connection
        for (const FAnimNodeConnection& Connection : AnimGraphData.NodeConnections)
        {
            ConnectAnimationNodes(AnimGraph, Connection);
        }

        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Successfully created animation graph with %d nodes"), AnimGraphData.AnimNodes.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception creating animation graph: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronAnimationBlueprint::CreateStateMachine(UAnimBlueprint* AnimBlueprint, const FStateMachineData& StateMachineData)
{
    if (!AnimBlueprint)
    {
        return false;
    }

    try
    {
        // Get animation graph using UE5.6 graph access
        UEdGraph* AnimGraph = GetAnimationGraph(AnimBlueprint);
        if (!AnimGraph)
        {
            UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("No animation graph found for state machine"));
            return false;
        }

        // Create state machine node using UE5.6 state machine system
        UAnimGraphNode_StateMachine* StateMachineNode = NewObject<UAnimGraphNode_StateMachine>(AnimGraph);
        if (!StateMachineNode)
        {
            UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Failed to create state machine node"));
            return false;
        }

        // Configure state machine using UE5.6 state machine configuration
        StateMachineNode->EditorStateMachineGraph = FBlueprintEditorUtils::CreateNewGraph(
            AnimBlueprint,
            *StateMachineData.StateMachineName,
            UAnimationStateMachineGraph::StaticClass(),
            UAnimationStateMachineSchema::StaticClass()
        );

        // Add state machine to animation graph
        AnimGraph->AddNode(StateMachineNode, true);
        StateMachineNode->AllocateDefaultPins();
        StateMachineNode->ReconstructNode();

        // Create states using UE5.6 state creation
        for (const FAnimStateData& StateData : StateMachineData.States)
        {
            CreateAnimationState(StateMachineNode->EditorStateMachineGraph, StateData);
        }

        // Create transitions using UE5.6 transition creation
        for (const FAnimTransitionData& TransitionData : StateMachineData.Transitions)
        {
            CreateStateTransition(StateMachineNode->EditorStateMachineGraph, TransitionData);
        }

        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Successfully created state machine '%s' with %d states"), 
               *StateMachineData.StateMachineName, StateMachineData.States.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception creating state machine: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronAnimationBlueprint::AddBlendSpaces(UAnimBlueprint* AnimBlueprint, const FBlendSpaceData& BlendSpaceData)
{
    if (!AnimBlueprint)
    {
        return false;
    }

    try
    {
        // Get animation graph using UE5.6 graph access
        UEdGraph* AnimGraph = GetAnimationGraph(AnimBlueprint);
        if (!AnimGraph)
        {
            UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("No animation graph found for blend spaces"));
            return false;
        }

        // Add blend space nodes using UE5.6 blend space system
        for (const FBlendSpaceNodeData& BlendSpaceNodeData : BlendSpaceData.BlendSpaces)
        {
            if (BlendSpaceNodeData.BlendSpace)
            {
                // Create blend space player node using UE5.6 blend space player
                UAnimGraphNode_BlendSpacePlayer* BlendSpaceNode = NewObject<UAnimGraphNode_BlendSpacePlayer>(AnimGraph);
                if (BlendSpaceNode)
                {
                    // Configure blend space node using UE5.6 node configuration
                    BlendSpaceNode->Node.BlendSpace = BlendSpaceNodeData.BlendSpace;
                    BlendSpaceNode->Node.X = BlendSpaceNodeData.XInput;
                    BlendSpaceNode->Node.Y = BlendSpaceNodeData.YInput;
                    BlendSpaceNode->Node.PlayRate = BlendSpaceNodeData.PlayRate;
                    BlendSpaceNode->Node.bLoop = BlendSpaceNodeData.bLoop;

                    // Add to graph using UE5.6 graph management
                    AnimGraph->AddNode(BlendSpaceNode, true);
                    BlendSpaceNode->AllocateDefaultPins();
                    BlendSpaceNode->ReconstructNode();

                    // Set node position using UE5.6 node positioning
                    BlendSpaceNode->NodePosX = BlendSpaceNodeData.NodePosition.X;
                    BlendSpaceNode->NodePosY = BlendSpaceNodeData.NodePosition.Y;
                }
            }
        }

        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Successfully added %d blend spaces"), BlendSpaceData.BlendSpaces.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception adding blend spaces: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronAnimationBlueprint::AddAnimationSequences(UAnimBlueprint* AnimBlueprint, const FAnimSequenceData& AnimSequenceData)
{
    if (!AnimBlueprint)
    {
        return false;
    }

    try
    {
        // Get animation graph using UE5.6 graph access
        UEdGraph* AnimGraph = GetAnimationGraph(AnimBlueprint);
        if (!AnimGraph)
        {
            UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("No animation graph found for animation sequences"));
            return false;
        }

        // Add animation sequence nodes using UE5.6 sequence player system
        for (const FAnimSequenceNodeData& SequenceNodeData : AnimSequenceData.AnimSequences)
        {
            if (SequenceNodeData.AnimSequence)
            {
                // Create sequence player node using UE5.6 sequence player
                UAnimGraphNode_SequencePlayer* SequenceNode = NewObject<UAnimGraphNode_SequencePlayer>(AnimGraph);
                if (SequenceNode)
                {
                    // Configure sequence node using UE5.6 node configuration
                    SequenceNode->Node.Sequence = SequenceNodeData.AnimSequence;
                    SequenceNode->Node.PlayRate = SequenceNodeData.PlayRate;
                    SequenceNode->Node.bLoopAnimation = SequenceNodeData.bLoop;
                    SequenceNode->Node.StartPosition = SequenceNodeData.StartPosition;

                    // Add to graph using UE5.6 graph management
                    AnimGraph->AddNode(SequenceNode, true);
                    SequenceNode->AllocateDefaultPins();
                    SequenceNode->ReconstructNode();

                    // Set node position using UE5.6 node positioning
                    SequenceNode->NodePosX = SequenceNodeData.NodePosition.X;
                    SequenceNode->NodePosY = SequenceNodeData.NodePosition.Y;
                }
            }
        }

        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Successfully added %d animation sequences"), AnimSequenceData.AnimSequences.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception adding animation sequences: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronAnimationBlueprint::CreateCustomEvents(UAnimBlueprint* AnimBlueprint, const FCustomEventData& CustomEventData)
{
    if (!AnimBlueprint)
    {
        return false;
    }

    try
    {
        // Get event graph using UE5.6 graph access
        UEdGraph* EventGraph = GetEventGraph(AnimBlueprint);
        if (!EventGraph)
        {
            UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("No event graph found for custom events"));
            return false;
        }

        // Create custom events using UE5.6 event system
        for (const FCustomEventNodeData& EventNodeData : CustomEventData.Events)
        {
            // Create custom event node using UE5.6 event node creation
            UK2Node_CustomEvent* CustomEventNode = NewObject<UK2Node_CustomEvent>(EventGraph);
            if (CustomEventNode)
            {
                // Configure custom event using UE5.6 event configuration
                CustomEventNode->CustomFunctionName = *EventNodeData.EventName;
                CustomEventNode->bCallInEditor = EventNodeData.bCallInEditor;
                CustomEventNode->bOverride = EventNodeData.bOverride;

                // Add parameters using UE5.6 parameter system
                for (const FEventParameterData& ParamData : EventNodeData.Parameters)
                {
                    FBPVariableDescription VariableDesc;
                    VariableDesc.VarName = *ParamData.ParameterName;
                    VariableDesc.VarType = ParamData.ParameterType;
                    VariableDesc.FriendlyName = ParamData.FriendlyName;

                    CustomEventNode->UserDefinedPins.Add(VariableDesc);
                }

                // Add to graph using UE5.6 graph management
                EventGraph->AddNode(CustomEventNode, true);
                CustomEventNode->AllocateDefaultPins();
                CustomEventNode->ReconstructNode();

                // Set node position using UE5.6 node positioning
                CustomEventNode->NodePosX = EventNodeData.NodePosition.X;
                CustomEventNode->NodePosY = EventNodeData.NodePosition.Y;
            }
        }

        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Successfully created %d custom events"), CustomEventData.Events.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception creating custom events: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronAnimationBlueprint::AddBlueprintVariables(UAnimBlueprint* AnimBlueprint, const FVariableData& VariableData)
{
    if (!AnimBlueprint)
    {
        return false;
    }

    try
    {
        // Add variables using UE5.6 variable system
        for (const FBlueprintVariableData& VarData : VariableData.Variables)
        {
            // Create variable description using UE5.6 variable description
            FBPVariableDescription VariableDesc;
            VariableDesc.VarName = *VarData.VariableName;
            VariableDesc.VarType = VarData.VariableType;
            VariableDesc.FriendlyName = VarData.FriendlyName;
            VariableDesc.Category = VarData.Category;
            VariableDesc.PropertyFlags = VarData.PropertyFlags;
            VariableDesc.VarGuid = FGuid::NewGuid();

            // Set default value using UE5.6 default value system
            if (!VarData.DefaultValue.IsEmpty())
            {
                VariableDesc.DefaultValue = VarData.DefaultValue;
            }

            // Add variable to blueprint using UE5.6 variable management
            FBlueprintEditorUtils::AddMemberVariable(AnimBlueprint, VariableDesc);
        }

        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Successfully added %d blueprint variables"), VariableData.Variables.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception adding blueprint variables: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronAnimationBlueprint::CompileAnimationBlueprint(UAnimBlueprint* AnimBlueprint)
{
    if (!AnimBlueprint)
    {
        return false;
    }

    try
    {
        // Compile blueprint using UE5.6 blueprint compilation system
        FKismetEditorUtilities::CompileBlueprint(AnimBlueprint, EBlueprintCompileOptions::None);

        // Check compilation status using UE5.6 compilation status
        if (AnimBlueprint->Status == BS_Error)
        {
            UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Animation blueprint compilation failed"));
            return false;
        }

        // Mark blueprint as up to date using UE5.6 blueprint management
        FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(AnimBlueprint);

        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Successfully compiled animation blueprint"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception compiling animation blueprint: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

// ========================================
// Helper Methods Implementation
// ========================================

bool FAuracronAnimationBlueprint::ValidateAnimBlueprintGenerationParameters(const FAnimBlueprintGenerationParameters& Parameters, FString& OutError)
{
    // Validate blueprint name
    if (Parameters.BlueprintName.IsEmpty())
    {
        OutError = TEXT("Blueprint name cannot be empty");
        return false;
    }

    // Validate target skeleton
    if (!Parameters.TargetSkeleton)
    {
        OutError = TEXT("Target skeleton is required");
        return false;
    }

    // Validate animation graph data
    if (Parameters.AnimGraphData.AnimNodes.Num() == 0)
    {
        OutError = TEXT("Animation graph must have at least one node");
        return false;
    }

    // Validate state machine data if state machine is requested
    if (Parameters.bCreateStateMachine)
    {
        if (Parameters.StateMachineData.States.Num() == 0)
        {
            OutError = TEXT("State machine must have at least one state");
            return false;
        }

        if (Parameters.StateMachineData.StateMachineName.IsEmpty())
        {
            OutError = TEXT("State machine name cannot be empty");
            return false;
        }
    }

    // Validate blend space data
    for (const FBlendSpaceNodeData& BlendSpaceNode : Parameters.BlendSpaceData.BlendSpaces)
    {
        if (!BlendSpaceNode.BlendSpace)
        {
            OutError = TEXT("Invalid blend space reference");
            return false;
        }
    }

    // Validate animation sequence data
    for (const FAnimSequenceNodeData& SequenceNode : Parameters.AnimSequenceData.AnimSequences)
    {
        if (!SequenceNode.AnimSequence)
        {
            OutError = TEXT("Invalid animation sequence reference");
            return false;
        }
    }

    return true;
}

FString FAuracronAnimationBlueprint::CalculateAnimBlueprintGenerationHash(const FAnimBlueprintGenerationParameters& Parameters)
{
    // Create unique hash based on generation parameters using UE5.6 hashing
    FString BaseKey = FString::Printf(TEXT("%s_%s_%d_%d_%d"),
        *Parameters.BlueprintName,
        Parameters.TargetSkeleton ? *Parameters.TargetSkeleton->GetName() : TEXT("NoSkeleton"),
        Parameters.AnimGraphData.AnimNodes.Num(),
        Parameters.bCreateStateMachine ? 1 : 0,
        Parameters.BlendSpaceData.BlendSpaces.Num()
    );

    // Add animation graph data hash
    uint32 AnimGraphHash = GetTypeHash(Parameters.AnimGraphData);
    BaseKey += FString::Printf(TEXT("_animgraph%u"), AnimGraphHash);

    // Add state machine data hash if applicable
    if (Parameters.bCreateStateMachine)
    {
        uint32 StateMachineHash = GetTypeHash(Parameters.StateMachineData);
        BaseKey += FString::Printf(TEXT("_statemachine%u"), StateMachineHash);
    }

    return FString::Printf(TEXT("%u"), GetTypeHash(BaseKey));
}

UEdGraph* FAuracronAnimationBlueprint::GetAnimationGraph(UAnimBlueprint* AnimBlueprint)
{
    if (!AnimBlueprint)
    {
        return nullptr;
    }

    // Find animation graph using UE5.6 graph search
    for (UEdGraph* Graph : AnimBlueprint->FunctionGraphs)
    {
        if (Graph && Graph->GetClass()->IsChildOf(UAnimationGraph::StaticClass()))
        {
            return Graph;
        }
    }

    return nullptr;
}

UEdGraph* FAuracronAnimationBlueprint::GetEventGraph(UAnimBlueprint* AnimBlueprint)
{
    if (!AnimBlueprint)
    {
        return nullptr;
    }

    // Find event graph using UE5.6 graph search
    for (UEdGraph* Graph : AnimBlueprint->UbergraphPages)
    {
        if (Graph && Graph->GetClass()->IsChildOf(UEdGraph::StaticClass()))
        {
            return Graph;
        }
    }

    return nullptr;
}

bool FAuracronAnimationBlueprint::CreateAnimationNode(UEdGraph* AnimGraph, const FAnimNodeData& NodeData)
{
    if (!AnimGraph)
    {
        return false;
    }

    try
    {
        // Create animation node based on type using UE5.6 node creation
        UAnimGraphNode_Base* AnimNode = nullptr;

        switch (NodeData.NodeType)
        {
            case EAnimNodeType::SequencePlayer:
            {
                UAnimGraphNode_SequencePlayer* SequenceNode = NewObject<UAnimGraphNode_SequencePlayer>(AnimGraph);
                if (SequenceNode && NodeData.AnimSequence)
                {
                    SequenceNode->Node.Sequence = NodeData.AnimSequence;
                    SequenceNode->Node.PlayRate = NodeData.PlayRate;
                    SequenceNode->Node.bLoopAnimation = NodeData.bLoop;
                }
                AnimNode = SequenceNode;
                break;
            }

            case EAnimNodeType::BlendSpacePlayer:
            {
                UAnimGraphNode_BlendSpacePlayer* BlendSpaceNode = NewObject<UAnimGraphNode_BlendSpacePlayer>(AnimGraph);
                if (BlendSpaceNode && NodeData.BlendSpace)
                {
                    BlendSpaceNode->Node.BlendSpace = NodeData.BlendSpace;
                    BlendSpaceNode->Node.X = NodeData.XInput;
                    BlendSpaceNode->Node.Y = NodeData.YInput;
                    BlendSpaceNode->Node.PlayRate = NodeData.PlayRate;
                }
                AnimNode = BlendSpaceNode;
                break;
            }

            case EAnimNodeType::StateMachine:
            {
                UAnimGraphNode_StateMachine* StateMachineNode = NewObject<UAnimGraphNode_StateMachine>(AnimGraph);
                AnimNode = StateMachineNode;
                break;
            }

            default:
                UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("Unsupported animation node type"));
                return false;
        }

        if (AnimNode)
        {
            // Add node to graph using UE5.6 graph management
            AnimGraph->AddNode(AnimNode, true);
            AnimNode->AllocateDefaultPins();
            AnimNode->ReconstructNode();

            // Set node position using UE5.6 node positioning
            AnimNode->NodePosX = NodeData.NodePosition.X;
            AnimNode->NodePosY = NodeData.NodePosition.Y;

            return true;
        }
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception creating animation node: %s"), UTF8_TO_TCHAR(e.what()));
    }

    return false;
}

bool FAuracronAnimationBlueprint::ConnectAnimationNodes(UEdGraph* AnimGraph, const FAnimNodeConnection& Connection)
{
    if (!AnimGraph)
    {
        return false;
    }

    try
    {
        // Find source and target nodes using UE5.6 node search
        UEdGraphNode* SourceNode = FindNodeByName(AnimGraph, Connection.SourceNodeName);
        UEdGraphNode* TargetNode = FindNodeByName(AnimGraph, Connection.TargetNodeName);

        if (!SourceNode || !TargetNode)
        {
            UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("Could not find nodes for connection: %s -> %s"),
                   *Connection.SourceNodeName, *Connection.TargetNodeName);
            return false;
        }

        // Find source and target pins using UE5.6 pin search
        UEdGraphPin* SourcePin = FindPinByName(SourceNode, Connection.SourcePinName, EGPD_Output);
        UEdGraphPin* TargetPin = FindPinByName(TargetNode, Connection.TargetPinName, EGPD_Input);

        if (!SourcePin || !TargetPin)
        {
            UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("Could not find pins for connection"));
            return false;
        }

        // Make connection using UE5.6 pin connection
        SourcePin->MakeLinkTo(TargetPin);

        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception connecting animation nodes: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronAnimationBlueprint::CreateAnimationState(UEdGraph* StateMachineGraph, const FAnimStateData& StateData)
{
    if (!StateMachineGraph)
    {
        return false;
    }

    try
    {
        // Create animation state node using UE5.6 state node creation
        UAnimStateNode* StateNode = NewObject<UAnimStateNode>(StateMachineGraph);
        if (!StateNode)
        {
            return false;
        }

        // Configure state node using UE5.6 state configuration
        StateNode->BoundGraph = FBlueprintEditorUtils::CreateNewGraph(
            StateMachineGraph->GetBlueprint(),
            *StateData.StateName,
            UAnimationStateGraph::StaticClass(),
            UAnimationStateGraphSchema::StaticClass()
        );

        // Set state properties using UE5.6 state properties
        StateNode->SetStateName(*StateData.StateName);

        // Add state to graph using UE5.6 graph management
        StateMachineGraph->AddNode(StateNode, true);
        StateNode->AllocateDefaultPins();
        StateNode->ReconstructNode();

        // Set node position using UE5.6 node positioning
        StateNode->NodePosX = StateData.StatePosition.X;
        StateNode->NodePosY = StateData.StatePosition.Y;

        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception creating animation state: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronAnimationBlueprint::CreateStateTransition(UEdGraph* StateMachineGraph, const FAnimTransitionData& TransitionData)
{
    if (!StateMachineGraph)
    {
        return false;
    }

    try
    {
        // Find source and target states using UE5.6 state search
        UAnimStateNode* SourceState = FindStateByName(StateMachineGraph, TransitionData.SourceStateName);
        UAnimStateNode* TargetState = FindStateByName(StateMachineGraph, TransitionData.TargetStateName);

        if (!SourceState || !TargetState)
        {
            UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("Could not find states for transition: %s -> %s"),
                   *TransitionData.SourceStateName, *TransitionData.TargetStateName);
            return false;
        }

        // Create transition node using UE5.6 transition creation
        UAnimStateTransitionNode* TransitionNode = NewObject<UAnimStateTransitionNode>(StateMachineGraph);
        if (!TransitionNode)
        {
            return false;
        }

        // Configure transition using UE5.6 transition configuration
        TransitionNode->BoundGraph = FBlueprintEditorUtils::CreateNewGraph(
            StateMachineGraph->GetBlueprint(),
            *FString::Printf(TEXT("%s_to_%s"), *TransitionData.SourceStateName, *TransitionData.TargetStateName),
            UAnimationTransitionGraph::StaticClass(),
            UAnimationTransitionSchema::StaticClass()
        );

        // Set transition properties using UE5.6 transition properties
        TransitionNode->CrossfadeDuration = TransitionData.CrossfadeDuration;
        TransitionNode->BlendMode = TransitionData.BlendMode;
        TransitionNode->LogicType = TransitionData.LogicType;

        // Add transition to graph using UE5.6 graph management
        StateMachineGraph->AddNode(TransitionNode, true);
        TransitionNode->AllocateDefaultPins();
        TransitionNode->ReconstructNode();

        // Connect transition to states using UE5.6 state connection
        UEdGraphPin* SourcePin = SourceState->GetOutputPin();
        UEdGraphPin* TargetPin = TargetState->GetInputPin();
        UEdGraphPin* TransitionInputPin = TransitionNode->GetInputPin();
        UEdGraphPin* TransitionOutputPin = TransitionNode->GetOutputPin();

        if (SourcePin && TransitionInputPin)
        {
            SourcePin->MakeLinkTo(TransitionInputPin);
        }

        if (TransitionOutputPin && TargetPin)
        {
            TransitionOutputPin->MakeLinkTo(TargetPin);
        }

        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception creating state transition: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

UEdGraphNode* FAuracronAnimationBlueprint::FindNodeByName(UEdGraph* Graph, const FString& NodeName)
{
    if (!Graph)
    {
        return nullptr;
    }

    // Search for node by name using UE5.6 node search
    for (UEdGraphNode* Node : Graph->Nodes)
    {
        if (Node && Node->GetNodeTitle(ENodeTitleType::ListView).ToString() == NodeName)
        {
            return Node;
        }
    }

    return nullptr;
}

UEdGraphPin* FAuracronAnimationBlueprint::FindPinByName(UEdGraphNode* Node, const FString& PinName, EEdGraphPinDirection Direction)
{
    if (!Node)
    {
        return nullptr;
    }

    // Search for pin by name and direction using UE5.6 pin search
    for (UEdGraphPin* Pin : Node->Pins)
    {
        if (Pin && Pin->PinName.ToString() == PinName && Pin->Direction == Direction)
        {
            return Pin;
        }
    }

    return nullptr;
}

UAnimStateNode* FAuracronAnimationBlueprint::FindStateByName(UEdGraph* StateMachineGraph, const FString& StateName)
{
    if (!StateMachineGraph)
    {
        return nullptr;
    }

    // Search for state by name using UE5.6 state search
    for (UEdGraphNode* Node : StateMachineGraph->Nodes)
    {
        UAnimStateNode* StateNode = Cast<UAnimStateNode>(Node);
        if (StateNode && StateNode->GetStateName() == StateName)
        {
            return StateNode;
        }
    }

    return nullptr;
}

void FAuracronAnimationBlueprint::UpdateAnimBlueprintCacheStats()
{
    FScopeLock Lock(&AnimBlueprintGenerationMutex);

    AnimBlueprintCacheMemoryUsage = 0;

    // Calculate total memory usage of cached animation blueprints
    for (const auto& CachePair : AnimBlueprintCache)
    {
        if (CachePair.Value.IsValid())
        {
            // Estimate memory usage based on animation blueprint complexity
            AnimBlueprintCacheMemoryUsage += EstimateAnimBlueprintMemoryUsage(CachePair.Value.Get());
        }
    }
}

int32 FAuracronAnimationBlueprint::EstimateAnimBlueprintMemoryUsage(UAnimBlueprint* AnimBlueprint)
{
    if (!AnimBlueprint)
    {
        return 0;
    }

    // Estimate memory usage based on animation blueprint data
    int32 EstimatedMemory = sizeof(UAnimBlueprint);

    // Add memory for graphs
    EstimatedMemory += AnimBlueprint->FunctionGraphs.Num() * 1024; // 1KB per graph estimate
    EstimatedMemory += AnimBlueprint->UbergraphPages.Num() * 1024; // 1KB per event graph estimate

    // Add memory for variables
    EstimatedMemory += AnimBlueprint->NewVariables.Num() * 256; // 256 bytes per variable estimate

    return EstimatedMemory;
}

void FAuracronAnimationBlueprint::ClearAnimBlueprintCache()
{
    FScopeLock Lock(&AnimBlueprintGenerationMutex);

    AnimBlueprintCache.Empty();
    AnimBlueprintCacheMemoryUsage = 0;

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Animation blueprint cache cleared"));
}

void FAuracronAnimationBlueprint::UpdateAnimBlueprintGenerationStats(const FString& OperationName, double ExecutionTime, bool bSuccess)
{
    FScopeLock Lock(&AnimBlueprintGenerationMutex);

    FString StatsValue = FString::Printf(TEXT("Time: %.3fs, Success: %s"), ExecutionTime, bSuccess ? TEXT("Yes") : TEXT("No"));
    AnimBlueprintGenerationStats.Add(OperationName, StatsValue);
}
