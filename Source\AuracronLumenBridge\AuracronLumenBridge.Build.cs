﻿using UnrealBuildTool;
public class AuracronLumenBridge : ModuleRules
{
    public AuracronLumenBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(new string[] 
        {
            "Core",
            "CoreUObject",
            "Engine",
            "RenderCore",
            "RHI","DeveloperSettings"
        });
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "Slate",
                "SlateCore",
                "EngineSettings",
                "GameplayTags",
                "GameplayTasks",
                "GameplayAbilities",
                "MeshUtilitiesCommon",
                "MeshDescription",
                "StaticMeshDescription",
                "MeshConversion",

                "DynamicMesh",
                "GeometryCore",
                "InteractiveToolsFramework",
                "ModelingComponents",
                "ModelingOperators",
                "Engine",
                "Landscape",
                "Foliage",
                "TextureUtilitiesCommon",
                "ImageCore",
                "ImageWrapper"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "AssetTools",
                    "ComponentVisualizers",
                    "ContentBrowser",
                    "DetailCustomizations",
                    "EditorStyle",
                    "EditorSubsystem",
                    "EditorWidgets",
                    "LevelEditor",
                    "MaterialEditor",
                    "PropertyEditor",
                    "SourceControl",
                    "ToolMenus",
                    "UnrealEd",
                    "ContentBrowserData",
                    "MaterialBaking",
                    "GPULightmass"
                }
            );
        }
        if (Target.bBuildEditor)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "UnrealEd",
                "ToolMenus",
                "EditorStyle",
                "EditorWidgets",
                "PropertyEditor",
                "DetailCustomizations",
                "ComponentVisualizers",
                "MaterialEditor",});
        }
        // Enable RTTI for Python integration
        bUseRTTI = true;
        // Enable exceptions for Python integration
        bEnableExceptions = true;
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        // Platform specific settings
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDefinitions.Add("AURACRON_LUMEN_BRIDGE_PLATFORM_WINDOWS=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.Android)
        {
            PublicDefinitions.Add("AURACRON_LUMEN_BRIDGE_PLATFORM_ANDROID=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_LUMEN_BRIDGE_PLATFORM_IOS=1");
        }
        // Lumen specific definitions
        PublicDefinitions.Add("WITH_LUMEN=1");
        PublicDefinitions.Add("AURACRON_LUMEN_BRIDGE_VERSION_MAJOR=1");
        PublicDefinitions.Add("AURACRON_LUMEN_BRIDGE_VERSION_MINOR=0");
        PublicDefinitions.Add("AURACRON_LUMEN_BRIDGE_VERSION_PATCH=0");
    }
}




