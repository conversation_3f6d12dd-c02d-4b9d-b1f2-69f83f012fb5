#pragma once

#include "CoreMinimal.h"
#include "UObject/ObjectMacros.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Array.h"
#include "Math/Vector.h"
#include "Animation/AnimBlueprint.h"
#include "Animation/AnimBlueprintGeneratedClass.h"
#include "Animation/AnimInstance.h"
#include "Animation/AnimSequence.h"
#include "Animation/PoseAsset.h"
#include "Animation/AnimMontage.h"
#include "Animation/BlendSpace.h"
#include "Animation/BlendSpace1D.h"
// BlendSpace2D is now part of BlendSpace.h in UE 5.6
#include "Animation/Skeleton.h"
#include "Engine/SkeletalMesh.h"
#include "Sound/SoundWave.h"
#include "Sound/SoundCue.h"
#include "Components/AudioComponent.h"

#include "AuracronAnimationBlueprint.generated.h"

#ifdef WITH_EDITOR
// Animation Graph Editor includes for UE 5.6
// AnimGraphNode_Base.h is not available in UE 5.6 - using alternative approach
// #include "AnimGraphNode_Base.h"
// AnimGraphNode_Root.h is not available in UE 5.6 - using alternative approach
// #include "AnimGraph/Public/AnimGraphNode_Root.h"
// AnimGraphNode_StateMachine.h is not available in UE 5.6 - using alternative approach
// #include "AnimGraph/Public/AnimGraphNode_StateMachine.h"
// AnimGraphNode_BlendListByBool.h is not available in UE 5.6 - using alternative approach
// #include "AnimGraph/Public/AnimGraphNode_BlendListByBool.h"
// AnimGraphNode_BlendSpacePlayer.h is not available in UE 5.6 - using alternative approach
// #include "AnimGraph/Public/AnimGraphNode_BlendSpacePlayer.h"
// AnimGraph includes removed - not needed for runtime functionality
// Blueprint editor includes removed - not needed for runtime functionality
// KismetEditor includes removed - not needed for runtime functionality
#endif

/**
 * Estrutura wrapper para TArray<FString> em TMap (Animation Blueprint)
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAnimationStringArrayWrapper
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "String Array")
    TArray<FString> Strings;

    FAnimationStringArrayWrapper()
    {
    }

    FAnimationStringArrayWrapper(const TArray<FString>& InStrings)
        : Strings(InStrings)
    {
    }
};

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronAnimationBlueprint, Log, All);

// Enums for animation blueprint generation
UENUM(BlueprintType)
enum class EAnimationBlueprintType : uint8
{
    Basic           UMETA(DisplayName = "Basic"),
    Facial          UMETA(DisplayName = "Facial"),
    FullBody        UMETA(DisplayName = "Full Body"),
    LipSync         UMETA(DisplayName = "Lip Sync"),
    Emotion         UMETA(DisplayName = "Emotion"),
    Advanced        UMETA(DisplayName = "Advanced")
};

UENUM(BlueprintType)
enum class EFacialAnimationType : uint8
{
    BlendShapes     UMETA(DisplayName = "Blend Shapes"),
    BoneTransforms  UMETA(DisplayName = "Bone Transforms"),
    Hybrid          UMETA(DisplayName = "Hybrid")
};

UENUM(BlueprintType)
enum class ELipSyncMethod : uint8
{
    Phoneme         UMETA(DisplayName = "Phoneme Based"),
    Viseme          UMETA(DisplayName = "Viseme Based"),
    AudioAnalysis   UMETA(DisplayName = "Audio Analysis"),
    ML              UMETA(DisplayName = "Machine Learning")
};

UENUM(BlueprintType)
enum class EEmotionBlendMode : uint8
{
    Replace         UMETA(DisplayName = "Replace"),
    Additive        UMETA(DisplayName = "Additive"),
    Multiply        UMETA(DisplayName = "Multiply"),
    Overlay         UMETA(DisplayName = "Overlay")
};

// Structures for animation blueprint generation
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FFacialAnimationData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    EFacialAnimationType AnimationType = EFacialAnimationType::BlendShapes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    TArray<FString> BlendShapeNames;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    TArray<FString> ControlBoneNames;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    bool bEnableEyeTracking = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    bool bEnableEyeBlink = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    bool bEnableEyebrowControl = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    bool bEnableJawControl = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    float BlendShapeMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    float AnimationSpeed = 1.0f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FLipSyncData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    ELipSyncMethod LipSyncMethod = ELipSyncMethod::Phoneme;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    TMap<FString, FString> PhonemeToBlendShapeMapping;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    TMap<FString, FString> VisemeToBlendShapeMapping;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    TSoftObjectPtr<USoundWave> AudioAsset;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    float LipSyncIntensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    float SmoothingFactor = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    bool bEnableAudioAnalysis = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    int32 FFTSize = 1024;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    float FrequencyRange = 8000.0f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FEmotionMappingData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Emotion Mapping")
    TMap<FString, FAnimationStringArrayWrapper> EmotionToBlendShapeMapping;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Emotion Mapping")
    TMap<FString, float> EmotionIntensities;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Emotion Mapping")
    EEmotionBlendMode BlendMode = EEmotionBlendMode::Replace;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Emotion Mapping")
    float TransitionSpeed = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Emotion Mapping")
    bool bEnableEmotionBlending = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Emotion Mapping")
    bool bEnableSubtleExpressions = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Emotion Mapping")
    float SubtleExpressionIntensity = 0.3f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAnimationBlueprintLODData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint LOD")
    int32 NumLODs = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint LOD")
    TArray<float> LODDistances = {500.0f, 1000.0f, 2000.0f};

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint LOD")
    TArray<float> BlendShapeReductions = {1.0f, 0.7f, 0.4f};

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint LOD")
    TArray<int32> UpdateFrequencies = {60, 30, 15};

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint LOD")
    bool bEnableScreenSizeBasedLOD = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint LOD")
    float ScreenSizeThreshold = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint LOD")
    bool bDisableFacialAnimationAtDistance = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint LOD")
    float FacialAnimationCullDistance = 1500.0f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAnimationBlueprintGenerationParameters
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    EAnimationBlueprintType BlueprintType = EAnimationBlueprintType::Facial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    TSoftObjectPtr<USkeletalMesh> TargetSkeletalMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    FFacialAnimationData FacialData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    FLipSyncData LipSyncData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    FEmotionMappingData EmotionData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    FAnimationBlueprintLODData LODData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    bool bGenerateLODs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    bool bEnableRealTimeUpdates = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    bool bOptimizeForPerformance = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    FString BlueprintName = TEXT("MetaHuman_AnimBP");
};

/**
 * Animation Blueprint generation system for MetaHuman Bridge
 * Provides advanced Animation Blueprint creation with UE5.6 Animation APIs
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronAnimationBlueprint
{
public:
    FAuracronAnimationBlueprint();
    ~FAuracronAnimationBlueprint();

    // Core Animation Blueprint generation
    UAnimBlueprint* GenerateAnimationBlueprint(const FAnimationBlueprintGenerationParameters& Parameters);
    bool SetupFacialAnimationSystem(UAnimBlueprint* AnimBlueprint, const FFacialAnimationData& FacialData);
    bool SetupLipSyncSystem(UAnimBlueprint* AnimBlueprint, const FLipSyncData& LipSyncData);
    bool SetupEmotionMappingSystem(UAnimBlueprint* AnimBlueprint, const FEmotionMappingData& EmotionData);

    // Animation Blueprint LOD generation
    bool GenerateAnimationBlueprintLODs(UAnimBlueprint* AnimBlueprint, const FAnimationBlueprintLODData& LODData);

    // System management
    void InitializeAnimationBlueprintSystem();
    void CleanupAnimationBlueprintSystem();

    // Validation
    bool ValidateAnimationBlueprintGenerationParameters(const FAnimationBlueprintGenerationParameters& Parameters, FString& ErrorMessage) const;

    // Templates and presets
    void InitializeDefaultAnimationBlueprintTemplates();
    void InitializeDefaultPhonemeMappings();
    void InitializeDefaultEmotionMappings();
    void InitializeAnimationBlueprintLODConfigurations();

    // Cache management
    void CacheAnimationBlueprint(const FString& CacheKey, UAnimBlueprint* AnimBlueprint);
    void ClearAnimationBlueprintCache();

    // Statistics
    void UpdateAnimationBlueprintGenerationStats(const FString& OperationName, double ExecutionTime, bool bSuccess);

    // Thread safety
    mutable FCriticalSection AnimationBlueprintGenerationMutex;

private:
    // Animation Blueprint cache
    mutable TMap<FString, TWeakObjectPtr<UAnimBlueprint>> AnimationBlueprintCache;
    mutable TMap<FString, FString> AnimationBlueprintGenerationStats;

    // Default templates and mappings
    TMap<FString, FAnimationBlueprintGenerationParameters> DefaultTemplates;
    TMap<FString, FString> DefaultPhonemeMappings;
    TMap<FString, TArray<FString>> DefaultEmotionMappings;
    TArray<FAnimationBlueprintLODData> DefaultLODConfigurations;

    // Prevent copying
    FAuracronAnimationBlueprint(const FAuracronAnimationBlueprint&) = delete;
    FAuracronAnimationBlueprint& operator=(const FAuracronAnimationBlueprint&) = delete;
};
