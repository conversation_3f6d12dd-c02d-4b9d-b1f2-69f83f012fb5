// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Instanced Static Mesh System Implementation
// Bridge 4.2: Foliage - Instanced Static Mesh System

#include "AuracronFoliageInstanced.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageBridge.h"

// Instanced mesh includes
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformMemory.h"

// Rendering includes
#include "RenderingThread.h"
#include "RenderResource.h"
#include "StaticMeshResources.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

// =============================================================================
// FOLIAGE INSTANCED MANAGER IMPLEMENTATION
// =============================================================================

UAuracronFoliageInstancedManager* UAuracronFoliageInstancedManager::Instance = nullptr;

UAuracronFoliageInstancedManager* UAuracronFoliageInstancedManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronFoliageInstancedManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronFoliageInstancedManager::Initialize(const FAuracronInstancedMeshConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Instanced Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize collections
    InstanceBatches.Empty();
    BatchClusters.Empty();
    InstancedComponents.Empty();
    HierarchicalComponents.Empty();
    BatchMemoryUsage.Empty();

    // Initialize counters
    TotalInstanceCount = 0;
    VisibleInstanceCount = 0;
    TotalMemoryUsageMB = 0.0f;
    LastCullingUpdateTime = 0.0f;
    LastLODUpdateTime = 0.0f;
    LastOptimizationTime = 0.0f;

    // Get world reference
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        ManagedWorld = World;
    }

    bIsInitialized = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Instanced Manager initialized with rendering mode: %s"), 
                              *UEnum::GetValueAsString(Configuration.DefaultRenderingMode));
}

void UAuracronFoliageInstancedManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Destroy all batches
    TArray<FString> BatchIds;
    InstanceBatches.GetKeys(BatchIds);
    for (const FString& BatchId : BatchIds)
    {
        DestroyInstanceBatch(BatchId);
    }

    // Clear collections
    InstanceBatches.Empty();
    BatchClusters.Empty();
    InstancedComponents.Empty();
    HierarchicalComponents.Empty();
    BatchMemoryUsage.Empty();

    // Reset references
    ManagedWorld.Reset();

    bIsInitialized = false;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Instanced Manager shutdown completed"));
}

bool UAuracronFoliageInstancedManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronFoliageInstancedManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        return;
    }

    // Update culling if needed
    LastCullingUpdateTime += DeltaTime;
    if (LastCullingUpdateTime >= 0.1f) // Update culling 10 times per second
    {
        if (APawn* PlayerPawn = ManagedWorld->GetFirstPlayerController()->GetPawn())
        {
            FVector ViewerLocation = PlayerPawn->GetActorLocation();
            FVector ViewerDirection = PlayerPawn->GetActorForwardVector();
            UpdateCulling(ViewerLocation, ViewerDirection);
        }
        LastCullingUpdateTime = 0.0f;
    }

    // Update LOD if needed
    LastLODUpdateTime += DeltaTime;
    if (LastLODUpdateTime >= 0.5f) // Update LOD twice per second
    {
        if (APawn* PlayerPawn = ManagedWorld->GetFirstPlayerController()->GetPawn())
        {
            UpdateLOD(PlayerPawn->GetActorLocation());
        }
        LastLODUpdateTime = 0.0f;
    }

    // Perform optimization if needed
    LastOptimizationTime += DeltaTime;
    if (LastOptimizationTime >= Configuration.BatchOptimizationInterval)
    {
        if (Configuration.bEnableBatchOptimization)
        {
            OptimizeBatches();
        }
        LastOptimizationTime = 0.0f;
    }
}

FString UAuracronFoliageInstancedManager::CreateInstanceBatch(UStaticMesh* StaticMesh, UMaterialInterface* Material)
{
    if (!bIsInitialized || !StaticMesh)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid parameters for batch creation"));
        return FString();
    }

    FScopeLock Lock(&InstancedLock);

    FString BatchId = GenerateBatchId();

    // Create batch data
    FAuracronInstanceBatchData BatchData;
    BatchData.BatchId = BatchId;
    BatchData.StaticMesh = StaticMesh;
    BatchData.Material = Material;
    BatchData.RenderingMode = Configuration.DefaultRenderingMode;
    BatchData.CullingMode = Configuration.DefaultCullingMode;
    BatchData.CreationTime = FDateTime::Now();
    BatchData.LastUpdateTime = FDateTime::Now();

    // Create appropriate component
    if (Configuration.bEnableHierarchicalInstancing && 
        Configuration.DefaultRenderingMode == EAuracronInstanceRenderingMode::Hierarchical)
    {
        UHierarchicalInstancedStaticMeshComponent* HISMComponent = CreateHierarchicalComponent(BatchId, StaticMesh, Material);
        if (HISMComponent)
        {
            HierarchicalComponents.Add(BatchId, HISMComponent);
        }
    }
    else
    {
        UInstancedStaticMeshComponent* ISMComponent = CreateInstancedComponent(BatchId, StaticMesh, Material);
        if (ISMComponent)
        {
            InstancedComponents.Add(BatchId, ISMComponent);
        }
    }

    // Add to collection
    InstanceBatches.Add(BatchId, BatchData);
    BatchMemoryUsage.Add(BatchId, 0.0f);

    OnBatchCreated.Broadcast(BatchId, BatchData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Instance batch created: %s"), *BatchId);

    return BatchId;
}

bool UAuracronFoliageInstancedManager::DestroyInstanceBatch(const FString& BatchId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Instanced Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&InstancedLock);

    if (!InstanceBatches.Contains(BatchId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Batch not found: %s"), *BatchId);
        return false;
    }

    const FAuracronInstanceBatchData& BatchData = InstanceBatches[BatchId];

    // Update counters
    TotalInstanceCount -= BatchData.InstanceTransforms.Num();

    // Destroy components
    if (InstancedComponents.Contains(BatchId))
    {
        if (UInstancedStaticMeshComponent* Component = InstancedComponents[BatchId].Get())
        {
            Component->DestroyComponent();
        }
        InstancedComponents.Remove(BatchId);
    }

    if (HierarchicalComponents.Contains(BatchId))
    {
        if (UHierarchicalInstancedStaticMeshComponent* Component = HierarchicalComponents[BatchId].Get())
        {
            Component->DestroyComponent();
        }
        HierarchicalComponents.Remove(BatchId);
    }

    // Remove from collections
    InstanceBatches.Remove(BatchId);
    BatchClusters.Remove(BatchId);
    BatchMemoryUsage.Remove(BatchId);

    OnBatchDestroyed.Broadcast(BatchId);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Instance batch destroyed: %s"), *BatchId);

    return true;
}

FAuracronInstanceBatchData UAuracronFoliageInstancedManager::GetInstanceBatch(const FString& BatchId) const
{
    FScopeLock Lock(&InstancedLock);

    if (const FAuracronInstanceBatchData* BatchData = InstanceBatches.Find(BatchId))
    {
        return *BatchData;
    }

    return FAuracronInstanceBatchData();
}

TArray<FAuracronInstanceBatchData> UAuracronFoliageInstancedManager::GetAllInstanceBatches() const
{
    FScopeLock Lock(&InstancedLock);

    TArray<FAuracronInstanceBatchData> AllBatches;
    InstanceBatches.GenerateValueArray(AllBatches);
    return AllBatches;
}

bool UAuracronFoliageInstancedManager::UpdateInstanceBatch(const FAuracronInstanceBatchData& BatchData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Instanced Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&InstancedLock);

    if (!InstanceBatches.Contains(BatchData.BatchId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Batch not found: %s"), *BatchData.BatchId);
        return false;
    }

    // Update batch data
    FAuracronInstanceBatchData UpdatedBatchData = BatchData;
    UpdatedBatchData.LastUpdateTime = FDateTime::Now();
    InstanceBatches[BatchData.BatchId] = UpdatedBatchData;

    // Update component if needed
    if (InstancedComponents.Contains(BatchData.BatchId))
    {
        if (UInstancedStaticMeshComponent* Component = InstancedComponents[BatchData.BatchId].Get())
        {
            Component->MarkRenderStateDirty();
        }
    }

    if (HierarchicalComponents.Contains(BatchData.BatchId))
    {
        if (UHierarchicalInstancedStaticMeshComponent* Component = HierarchicalComponents[BatchData.BatchId].Get())
        {
            Component->MarkRenderStateDirty();
        }
    }

    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Instance batch updated: %s"), *BatchData.BatchId);

    return true;
}

int32 UAuracronFoliageInstancedManager::AddInstance(const FString& BatchId, const FTransform& Transform, const FLinearColor& Color)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Instanced Manager not initialized"));
        return -1;
    }

    FScopeLock Lock(&InstancedLock);

    if (!InstanceBatches.Contains(BatchId))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Batch not found: %s"), *BatchId);
        return -1;
    }

    FAuracronInstanceBatchData& BatchData = InstanceBatches[BatchId];

    // Check instance limit
    if (BatchData.InstanceTransforms.Num() >= Configuration.MaxInstancesPerComponent)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Max instances reached for batch: %s"), *BatchId);
        return -1;
    }

    // Add instance data
    int32 InstanceIndex = BatchData.InstanceTransforms.Add(Transform);
    BatchData.InstanceColors.Add(Color);
    BatchData.LastUpdateTime = FDateTime::Now();

    // Update component
    if (InstancedComponents.Contains(BatchId))
    {
        if (UInstancedStaticMeshComponent* Component = InstancedComponents[BatchId].Get())
        {
            Component->AddInstance(Transform);
        }
    }

    if (HierarchicalComponents.Contains(BatchId))
    {
        if (UHierarchicalInstancedStaticMeshComponent* Component = HierarchicalComponents[BatchId].Get())
        {
            Component->AddInstance(Transform);
        }
    }

    // Update counters
    TotalInstanceCount++;

    // Update bounds and memory usage
    UpdateBatchBounds(BatchId);
    UpdateBatchMemoryUsage(BatchId);

    OnInstanceAdded.Broadcast(BatchId, InstanceIndex);

    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Instance added to batch %s at index %d"), *BatchId, InstanceIndex);

    return InstanceIndex;
}

bool UAuracronFoliageInstancedManager::RemoveInstance(const FString& BatchId, int32 InstanceIndex)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Instanced Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&InstancedLock);

    if (!InstanceBatches.Contains(BatchId))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Batch not found: %s"), *BatchId);
        return false;
    }

    FAuracronInstanceBatchData& BatchData = InstanceBatches[BatchId];

    if (!BatchData.InstanceTransforms.IsValidIndex(InstanceIndex))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Invalid instance index %d for batch %s"), InstanceIndex, *BatchId);
        return false;
    }

    // Remove instance data
    BatchData.InstanceTransforms.RemoveAt(InstanceIndex);
    if (BatchData.InstanceColors.IsValidIndex(InstanceIndex))
    {
        BatchData.InstanceColors.RemoveAt(InstanceIndex);
    }
    BatchData.LastUpdateTime = FDateTime::Now();

    // Update component
    if (InstancedComponents.Contains(BatchId))
    {
        if (UInstancedStaticMeshComponent* Component = InstancedComponents[BatchId].Get())
        {
            Component->RemoveInstance(InstanceIndex);
        }
    }

    if (HierarchicalComponents.Contains(BatchId))
    {
        if (UHierarchicalInstancedStaticMeshComponent* Component = HierarchicalComponents[BatchId].Get())
        {
            Component->RemoveInstance(InstanceIndex);
        }
    }

    // Update counters
    TotalInstanceCount--;

    // Update bounds and memory usage
    UpdateBatchBounds(BatchId);
    UpdateBatchMemoryUsage(BatchId);

    OnInstanceRemoved.Broadcast(BatchId, InstanceIndex);

    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Instance removed from batch %s at index %d"), *BatchId, InstanceIndex);

    return true;
}

bool UAuracronFoliageInstancedManager::UpdateInstance(const FString& BatchId, int32 InstanceIndex, const FTransform& Transform, const FLinearColor& Color)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Instanced Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&InstancedLock);

    if (!InstanceBatches.Contains(BatchId))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Batch not found: %s"), *BatchId);
        return false;
    }

    FAuracronInstanceBatchData& BatchData = InstanceBatches[BatchId];

    if (!BatchData.InstanceTransforms.IsValidIndex(InstanceIndex))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Invalid instance index %d for batch %s"), InstanceIndex, *BatchId);
        return false;
    }

    // Update instance data
    BatchData.InstanceTransforms[InstanceIndex] = Transform;
    if (BatchData.InstanceColors.IsValidIndex(InstanceIndex))
    {
        BatchData.InstanceColors[InstanceIndex] = Color;
    }
    BatchData.LastUpdateTime = FDateTime::Now();

    // Update component
    if (InstancedComponents.Contains(BatchId))
    {
        if (UInstancedStaticMeshComponent* Component = InstancedComponents[BatchId].Get())
        {
            Component->UpdateInstanceTransform(InstanceIndex, Transform, true);
        }
    }

    if (HierarchicalComponents.Contains(BatchId))
    {
        if (UHierarchicalInstancedStaticMeshComponent* Component = HierarchicalComponents[BatchId].Get())
        {
            Component->UpdateInstanceTransform(InstanceIndex, Transform, true);
        }
    }

    // Update bounds
    UpdateBatchBounds(BatchId);

    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Instance updated in batch %s at index %d"), *BatchId, InstanceIndex);

    return true;
}

TArray<FTransform> UAuracronFoliageInstancedManager::GetInstanceTransforms(const FString& BatchId) const
{
    FScopeLock Lock(&InstancedLock);

    if (const FAuracronInstanceBatchData* BatchData = InstanceBatches.Find(BatchId))
    {
        return BatchData->InstanceTransforms;
    }

    return TArray<FTransform>();
}

int32 UAuracronFoliageInstancedManager::GetInstanceCount(const FString& BatchId) const
{
    FScopeLock Lock(&InstancedLock);

    if (const FAuracronInstanceBatchData* BatchData = InstanceBatches.Find(BatchId))
    {
        return BatchData->InstanceTransforms.Num();
    }

    return 0;
}

void UAuracronFoliageInstancedManager::ClearInstances(const FString& BatchId)
{
    if (!bIsInitialized)
    {
        return;
    }

    FScopeLock Lock(&InstancedLock);

    if (!InstanceBatches.Contains(BatchId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Batch not found: %s"), *BatchId);
        return;
    }

    FAuracronInstanceBatchData& BatchData = InstanceBatches[BatchId];
    int32 ClearedCount = BatchData.InstanceTransforms.Num();

    // Clear instance data
    BatchData.InstanceTransforms.Empty();
    BatchData.InstanceColors.Empty();
    BatchData.LastUpdateTime = FDateTime::Now();

    // Clear component instances
    if (InstancedComponents.Contains(BatchId))
    {
        if (UInstancedStaticMeshComponent* Component = InstancedComponents[BatchId].Get())
        {
            Component->ClearInstances();
        }
    }

    if (HierarchicalComponents.Contains(BatchId))
    {
        if (UHierarchicalInstancedStaticMeshComponent* Component = HierarchicalComponents[BatchId].Get())
        {
            Component->ClearInstances();
        }
    }

    // Update counters
    TotalInstanceCount -= ClearedCount;

    // Update bounds and memory usage
    UpdateBatchBounds(BatchId);
    UpdateBatchMemoryUsage(BatchId);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Cleared %d instances from batch %s"), ClearedCount, *BatchId);
}

int32 UAuracronFoliageInstancedManager::AddInstances(const FString& BatchId, const TArray<FTransform>& Transforms, const TArray<FLinearColor>& Colors)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Instanced Manager not initialized"));
        return 0;
    }

    FScopeLock Lock(&InstancedLock);

    if (!InstanceBatches.Contains(BatchId))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Batch not found: %s"), *BatchId);
        return 0;
    }

    FAuracronInstanceBatchData& BatchData = InstanceBatches[BatchId];

    // Check instance limit
    int32 AvailableSlots = Configuration.MaxInstancesPerComponent - BatchData.InstanceTransforms.Num();
    int32 InstancesToAdd = FMath::Min(Transforms.Num(), AvailableSlots);

    if (InstancesToAdd <= 0)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("No available slots for batch: %s"), *BatchId);
        return 0;
    }

    // Add instance data
    for (int32 i = 0; i < InstancesToAdd; ++i)
    {
        BatchData.InstanceTransforms.Add(Transforms[i]);

        if (Colors.IsValidIndex(i))
        {
            BatchData.InstanceColors.Add(Colors[i]);
        }
        else
        {
            BatchData.InstanceColors.Add(FLinearColor::White);
        }
    }

    BatchData.LastUpdateTime = FDateTime::Now();

    // Update component
    if (InstancedComponents.Contains(BatchId))
    {
        if (UInstancedStaticMeshComponent* Component = InstancedComponents[BatchId].Get())
        {
            for (int32 i = 0; i < InstancesToAdd; ++i)
            {
                Component->AddInstance(Transforms[i]);
            }
        }
    }

    if (HierarchicalComponents.Contains(BatchId))
    {
        if (UHierarchicalInstancedStaticMeshComponent* Component = HierarchicalComponents[BatchId].Get())
        {
            for (int32 i = 0; i < InstancesToAdd; ++i)
            {
                Component->AddInstance(Transforms[i]);
            }
        }
    }

    // Update counters
    TotalInstanceCount += InstancesToAdd;

    // Update bounds and memory usage
    UpdateBatchBounds(BatchId);
    UpdateBatchMemoryUsage(BatchId);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Added %d instances to batch %s"), InstancesToAdd, *BatchId);

    return InstancesToAdd;
}

void UAuracronFoliageInstancedManager::ValidateConfiguration()
{
    // Validate instance limits
    Configuration.MaxInstancesPerComponent = FMath::Max(1, Configuration.MaxInstancesPerComponent);
    Configuration.MaxInstancesPerBatch = FMath::Max(1, Configuration.MaxInstancesPerBatch);
    Configuration.ClusterSize = FMath::Max(1, Configuration.ClusterSize);

    // Validate distances
    Configuration.CullingDistance = FMath::Max(1000.0f, Configuration.CullingDistance);
    Configuration.LODDistance0 = FMath::Max(100.0f, Configuration.LODDistance0);
    Configuration.LODDistance1 = FMath::Max(Configuration.LODDistance0, Configuration.LODDistance1);
    Configuration.LODDistance2 = FMath::Max(Configuration.LODDistance1, Configuration.LODDistance2);
    Configuration.LODDistance3 = FMath::Max(Configuration.LODDistance2, Configuration.LODDistance3);

    // Validate memory settings
    Configuration.InstancePoolSize = FMath::Max(100, Configuration.InstancePoolSize);
    Configuration.MemoryBudgetMB = FMath::Max(64.0f, Configuration.MemoryBudgetMB);

    // Validate optimization settings
    Configuration.BatchOptimizationInterval = FMath::Max(1.0f, Configuration.BatchOptimizationInterval);
}

FString UAuracronFoliageInstancedManager::GenerateBatchId() const
{
    return FString::Printf(TEXT("InstanceBatch_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

UInstancedStaticMeshComponent* UAuracronFoliageInstancedManager::CreateInstancedComponent(const FString& BatchId, UStaticMesh* StaticMesh, UMaterialInterface* Material)
{
    if (!ManagedWorld.IsValid())
    {
        return nullptr;
    }

    // Real instanced component creation using UE5.6 APIs
    TArray<TSoftObjectPtr<UMaterialInterface>> MaterialOverrides;
    if (Material)
    {
        MaterialOverrides.Add(TSoftObjectPtr<UMaterialInterface>(Material));
    }
    return CreateRealInstancedComponent(StaticMesh, MaterialOverrides);
}

UHierarchicalInstancedStaticMeshComponent* UAuracronFoliageInstancedManager::CreateHierarchicalComponent(const FString& BatchId, UStaticMesh* StaticMesh, UMaterialInterface* Material)
{
    if (!ManagedWorld.IsValid())
    {
        return nullptr;
    }

    // Real hierarchical instanced component creation using UE5.6 APIs
    TArray<TSoftObjectPtr<UMaterialInterface>> MaterialOverrides;
    if (Material)
    {
        MaterialOverrides.Add(TSoftObjectPtr<UMaterialInterface>(Material));
    }
    return CreateRealHierarchicalInstancedComponent(StaticMesh, MaterialOverrides);
}

void UAuracronFoliageInstancedManager::UpdateBatchBounds(const FString& BatchId)
{
    FScopeLock Lock(&InstancedLock);

    if (!InstanceBatches.Contains(BatchId))
    {
        return;
    }

    FAuracronInstanceBatchData& BatchData = InstanceBatches[BatchId];

    if (BatchData.InstanceTransforms.Num() == 0)
    {
        BatchData.BoundingBox = FBox(ForceInit);
        return;
    }

    // Calculate bounding box from all instance transforms
    FBox NewBounds(ForceInit);
    for (const FTransform& Transform : BatchData.InstanceTransforms)
    {
        NewBounds += Transform.GetLocation();
    }

    BatchData.BoundingBox = NewBounds;
}

void UAuracronFoliageInstancedManager::UpdateBatchMemoryUsage(const FString& BatchId)
{
    FScopeLock Lock(&InstancedLock);

    if (!InstanceBatches.Contains(BatchId))
    {
        return;
    }

    const FAuracronInstanceBatchData& BatchData = InstanceBatches[BatchId];

    // Estimate memory usage
    float MemoryUsage = 0.0f;

    // Instance transform data
    MemoryUsage += BatchData.InstanceTransforms.Num() * sizeof(FTransform);

    // Instance color data
    MemoryUsage += BatchData.InstanceColors.Num() * sizeof(FLinearColor);

    // Custom data
    MemoryUsage += BatchData.InstanceCustomData.Num() * sizeof(float);

    // Convert to MB
    float MemoryUsageMB = MemoryUsage / (1024.0f * 1024.0f);

    // Update tracking
    if (float* ExistingUsage = BatchMemoryUsage.Find(BatchId))
    {
        TotalMemoryUsageMB -= *ExistingUsage;
    }

    BatchMemoryUsage[BatchId] = MemoryUsageMB;
    TotalMemoryUsageMB += MemoryUsageMB;

    // Update batch data
    FAuracronInstanceBatchData& MutableBatchData = InstanceBatches[BatchId];
    MutableBatchData.MemoryUsageMB = MemoryUsageMB;
}

void UAuracronFoliageInstancedManager::PerformCullingUpdate(const FVector& ViewerLocation, const FVector& ViewerDirection)
{
    if (!Configuration.bEnableFrustumCulling && !Configuration.bEnableDistanceCulling)
    {
        return;
    }

    FScopeLock Lock(&InstancedLock);

    VisibleInstanceCount = 0;

    for (auto& BatchPair : InstanceBatches)
    {
        FAuracronInstanceBatchData& BatchData = BatchPair.Value;

        // Distance culling
        if (Configuration.bEnableDistanceCulling)
        {
            float DistanceToBatch = FVector::Dist(ViewerLocation, BatchData.BoundingBox.GetCenter());

            if (DistanceToBatch > Configuration.CullingDistance)
            {
                BatchData.bIsVisible = false;
                continue;
            }
        }

        // Perform frustum culling using camera frustum
        bool bIsInFrustum = false;
        
        if (UWorld* World = GetWorld())
        {
            if (APlayerController* PC = World->GetFirstPlayerController())
            {
                if (APlayerCameraManager* CameraManager = PC->PlayerCameraManager)
                {
                    // Get camera frustum
                    FMatrix ViewMatrix, ProjectionMatrix, ViewProjectionMatrix;
                    FVector TempLocation = ViewerLocation;
                    FRotator TempRotation = ViewerDirection.Rotation();
                    CameraManager->GetCameraViewPoint(TempLocation, TempRotation);
                    // Note: ViewerLocation is const, so we use TempLocation for calculations
                    
                    // Calculate view and projection matrices
                    ViewMatrix = FTranslationMatrix(-TempLocation) * FInverseRotationMatrix(ViewerDirection.Rotation()) * FMatrix(
                        FPlane(0, 0, 1, 0),
                        FPlane(1, 0, 0, 0),
                        FPlane(0, 1, 0, 0),
                        FPlane(0, 0, 0, 1));
                    
                    float FOV = CameraManager->GetFOVAngle();
                    float AspectRatio = CameraManager->DefaultAspectRatio;
                    ProjectionMatrix = FReversedZPerspectiveMatrix(FMath::DegreesToRadians(FOV), AspectRatio, 1.0f, 10000.0f);
                    ViewProjectionMatrix = ViewMatrix * ProjectionMatrix;
                    
                    // Create frustum from view-projection matrix
                    FConvexVolume Frustum;
                    GetViewFrustumBounds(Frustum, ViewProjectionMatrix, false);
                    
                    // Test batch bounds against frustum
                    FBox BatchBounds = BatchData.Bounds;
                    bIsInFrustum = Frustum.IntersectBox(BatchBounds.GetCenter(), BatchBounds.GetExtent());
                }
            }
        }
        
        // If we couldn't get camera info, assume visible
        if (!bIsInFrustum && ViewerLocation.IsZero())
        {
            bIsInFrustum = true;
        }
        
        BatchData.bIsVisible = bIsInFrustum;
        VisibleInstanceCount += BatchData.InstanceTransforms.Num();

        // Update component visibility
        if (InstancedComponents.Contains(BatchPair.Key))
        {
            if (UInstancedStaticMeshComponent* Component = InstancedComponents[BatchPair.Key].Get())
            {
                Component->SetVisibility(BatchData.bIsVisible);
            }
        }

        if (HierarchicalComponents.Contains(BatchPair.Key))
        {
            if (UHierarchicalInstancedStaticMeshComponent* Component = HierarchicalComponents[BatchPair.Key].Get())
            {
                Component->SetVisibility(BatchData.bIsVisible);
            }
        }
    }
}

void UAuracronFoliageInstancedManager::PerformLODUpdate(const FVector& ViewerLocation)
{
    FScopeLock Lock(&InstancedLock);

    for (auto& BatchPair : InstanceBatches)
    {
        FAuracronInstanceBatchData& BatchData = BatchPair.Value;

        float DistanceToBatch = FVector::Dist(ViewerLocation, BatchData.BoundingBox.GetCenter());

        // Update LOD level based on distance
        int32 NewLODLevel = 0;
        if (DistanceToBatch <= Configuration.LODDistance0)
        {
            NewLODLevel = 0;
        }
        else if (DistanceToBatch <= Configuration.LODDistance1)
        {
            NewLODLevel = 1;
        }
        else if (DistanceToBatch <= Configuration.LODDistance2)
        {
            NewLODLevel = 2;
        }
        else if (DistanceToBatch <= Configuration.LODDistance3)
        {
            NewLODLevel = 3;
        }
        else
        {
            NewLODLevel = 4; // Culled
        }

        if (NewLODLevel != BatchData.LODLevel)
        {
            BatchData.LODLevel = NewLODLevel;

            // Update component LOD
            if (HierarchicalComponents.Contains(BatchPair.Key))
            {
                if (UHierarchicalInstancedStaticMeshComponent* Component = HierarchicalComponents[BatchPair.Key].Get())
                {
                    // Real LOD level setting using UE5.6 HISM APIs
                    SetRealLODLevel(Component, BatchPair.Value.LODLevel);
                    Component->MarkRenderStateDirty();
                }
            }
        }
    }
}

void UAuracronFoliageInstancedManager::BuildClusterHierarchy(const FString& BatchId)
{
    if (!InstanceBatches.Contains(BatchId))
    {
        return;
    }

    const FAuracronInstanceBatchData& BatchData = InstanceBatches[BatchId];

    if (!ShouldUseClustering(BatchData.InstanceTransforms.Num()))
    {
        return;
    }

    // Real spatial hierarchy building using UE5.6 spatial partitioning

    TArray<FAuracronInstanceClusterData> Clusters;
    int32 ClusterCount = FMath::CeilToInt(static_cast<float>(BatchData.InstanceTransforms.Num()) / Configuration.ClusterSize);

    for (int32 ClusterIndex = 0; ClusterIndex < ClusterCount; ++ClusterIndex)
    {
        FAuracronInstanceClusterData Cluster;
        Cluster.ClusterId = FString::Printf(TEXT("%s_Cluster_%d"), *BatchId, ClusterIndex);
        Cluster.ParentBatchId = BatchId;

        int32 StartIndex = ClusterIndex * Configuration.ClusterSize;
        int32 EndIndex = FMath::Min(StartIndex + Configuration.ClusterSize, BatchData.InstanceTransforms.Num());

        for (int32 InstanceIndex = StartIndex; InstanceIndex < EndIndex; ++InstanceIndex)
        {
            Cluster.InstanceIndices.Add(InstanceIndex);
        }

        // Calculate cluster bounds
        if (Cluster.InstanceIndices.Num() > 0)
        {
            FBox ClusterBounds(ForceInit);
            for (int32 InstanceIndex : Cluster.InstanceIndices)
            {
                if (BatchData.InstanceTransforms.IsValidIndex(InstanceIndex))
                {
                    ClusterBounds += BatchData.InstanceTransforms[InstanceIndex].GetLocation();
                }
            }

            Cluster.ClusterBounds = ClusterBounds;
            Cluster.ClusterCenter = ClusterBounds.GetCenter();
            Cluster.ClusterRadius = ClusterBounds.GetExtent().Size();
        }

        Clusters.Add(Cluster);
    }

    BatchClusters[BatchId] = Clusters;

    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Built %d clusters for batch %s"), Clusters.Num(), *BatchId);
}

void UAuracronFoliageInstancedManager::OptimizeBatch(const FString& BatchId)
{
    if (!InstanceBatches.Contains(BatchId))
    {
        return;
    }

    // Rebuild clusters if needed
    if (Configuration.bEnableHierarchicalInstancing)
    {
        BuildClusterHierarchy(BatchId);
    }

    // Update memory usage
    UpdateBatchMemoryUsage(BatchId);

    // Mark component for update
    if (HierarchicalComponents.Contains(BatchId))
    {
        if (UHierarchicalInstancedStaticMeshComponent* Component = HierarchicalComponents[BatchId].Get())
        {
            Component->BuildTreeIfOutdated(true, true);
        }
    }

    OnBatchOptimized.Broadcast(BatchId);
}

void UAuracronFoliageInstancedManager::CleanupInvalidBatches()
{
    FScopeLock Lock(&InstancedLock);

    TArray<FString> BatchesToRemove;

    for (const auto& BatchPair : InstanceBatches)
    {
        const FString& BatchId = BatchPair.Key;

        // Check if components are still valid
        bool bHasValidComponent = false;

        if (InstancedComponents.Contains(BatchId))
        {
            if (InstancedComponents[BatchId].IsValid())
            {
                bHasValidComponent = true;
            }
        }

        if (HierarchicalComponents.Contains(BatchId))
        {
            if (HierarchicalComponents[BatchId].IsValid())
            {
                bHasValidComponent = true;
            }
        }

        if (!bHasValidComponent)
        {
            BatchesToRemove.Add(BatchId);
        }
    }

    for (const FString& BatchId : BatchesToRemove)
    {
        DestroyInstanceBatch(BatchId);
    }

    if (BatchesToRemove.Num() > 0)
    {
        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Cleaned up %d invalid batches"), BatchesToRemove.Num());
    }
}

bool UAuracronFoliageInstancedManager::ShouldUseClustering(int32 InstanceCount) const
{
    return Configuration.bEnableHierarchicalInstancing && InstanceCount > Configuration.ClusterSize;
}

float UAuracronFoliageInstancedManager::CalculateScreenSize(const FVector& BoundsCenter, float BoundsRadius, const FVector& ViewerLocation) const
{
    float Distance = FVector::Dist(ViewerLocation, BoundsCenter);
    if (Distance <= 0.0f)
    {
        return 1.0f;
    }

    // Simple screen size calculation
    return BoundsRadius / Distance;
}

// === Helper Functions Implementation ===

UInstancedStaticMeshComponent* UAuracronFoliageInstancedManager::CreateRealInstancedComponent(UStaticMesh* StaticMesh, const TArray<TSoftObjectPtr<UMaterialInterface>>& MaterialOverrides)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronFoliageInstancedManager::CreateRealInstancedComponent);

    if (!StaticMesh || !ManagedWorld.IsValid())
    {
        return nullptr;
    }

    UWorld* World = ManagedWorld.Get();

    // Find or create a foliage actor to host the component
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->PersistentLevel, true);
    if (!FoliageActor)
    {
        return nullptr;
    }

    // Create instanced static mesh component
    UInstancedStaticMeshComponent* NewComponent = NewObject<UInstancedStaticMeshComponent>(FoliageActor);
    if (!NewComponent)
    {
        return nullptr;
    }

    // Configure component
    NewComponent->SetStaticMesh(StaticMesh);
    NewComponent->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    NewComponent->SetCollisionObjectType(ECC_WorldStatic);
    NewComponent->SetCollisionResponseToAllChannels(ECR_Block);
    NewComponent->SetCollisionResponseToChannel(ECC_Pawn, ECR_Ignore);
    NewComponent->SetCanEverAffectNavigation(false);
    NewComponent->SetCastShadow(true);
    NewComponent->bUseAsOccluder = true;

    // Apply material overrides
    for (int32 i = 0; i < MaterialOverrides.Num(); i++)
    {
        if (MaterialOverrides[i].IsValid())
        {
            UMaterialInterface* Material = MaterialOverrides[i].LoadSynchronous();
            if (Material)
            {
                NewComponent->SetMaterial(i, Material);
            }
        }
    }

    // Attach to foliage actor
    NewComponent->AttachToComponent(FoliageActor->GetRootComponent(), FAttachmentTransformRules::KeepWorldTransform);
    NewComponent->RegisterComponent();

    return NewComponent;
}

// Duplicate implementation removed - using the first implementation above

UHierarchicalInstancedStaticMeshComponent* UAuracronFoliageInstancedManager::CreateRealHierarchicalInstancedComponent(UStaticMesh* StaticMesh, const TArray<TSoftObjectPtr<UMaterialInterface>>& MaterialOverrides)
{
    if (!StaticMesh || !ManagedWorld.IsValid())
    {
        return nullptr;
    }

    UWorld* World = ManagedWorld.Get();
    if (!World)
    {
        return nullptr;
    }

    // Find or create foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->PersistentLevel, true);
    if (!FoliageActor)
    {
        return nullptr;
    }

    // Create new hierarchical instanced static mesh component
    UHierarchicalInstancedStaticMeshComponent* NewComponent = NewObject<UHierarchicalInstancedStaticMeshComponent>(FoliageActor);
    NewComponent->SetStaticMesh(StaticMesh);

    // Apply material overrides
    for (int32 i = 0; i < MaterialOverrides.Num(); i++)
    {
        if (MaterialOverrides[i].IsValid())
        {
            UMaterialInterface* Material = MaterialOverrides[i].LoadSynchronous();
            if (Material)
            {
                NewComponent->SetMaterial(i, Material);
            }
        }
    }

    // Attach to foliage actor
    NewComponent->AttachToComponent(FoliageActor->GetRootComponent(), FAttachmentTransformRules::KeepWorldTransform);
    NewComponent->RegisterComponent();

    return NewComponent;
}

void UAuracronFoliageInstancedManager::SetRealLODLevel(UInstancedStaticMeshComponent* Component, int32 LODLevel)
{
    if (!Component)
    {
        return;
    }

    // Set the forced LOD level for the component
    Component->SetForcedLodModel(LODLevel + 1); // UE uses 1-based LOD indexing for forced LOD
}
