// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de MonetizaÃ§Ã£o Bridge
// IntegraÃ§Ã£o C++ para monetizaÃ§Ã£o Ã©tica usando APIs modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "Components/GameFrameworkComponent.h"
#include "Net/UnrealNetwork.h"
#include "OnlineSubsystem.h"
#include "OnlineSubsystemUtils.h"
#include "Interfaces/OnlineStoreInterfaceV2.h"
#include "Interfaces/OnlinePurchaseInterface.h"
#include "GameplayTagContainer.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "MediaSource.h"
#include "Net/Core/PushModel/PushModel.h"
#include "TimerManager.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "Internationalization/Text.h"
#include "AuracronMonetizationBridge.generated.h"

/**
 * EnumeraÃ§Ã£o para tipos de moeda
 */
UENUM(BlueprintType)
enum class EAuracronCurrencyType : uint8
{
    None                UMETA(DisplayName = "None"),
    AuraCoins           UMETA(DisplayName = "Aura Coins (Premium)"),
    ChronosShards       UMETA(DisplayName = "Chronos Shards (Free)"),
    RealmEssence        UMETA(DisplayName = "Realm Essence (Earned)"),
    SigiloFragments     UMETA(DisplayName = "Sigilo Fragments (Special)"),
    SeasonTokens        UMETA(DisplayName = "Season Tokens (Limited)")
};

/**
 * EnumeraÃ§Ã£o para tipos de produto
 */
UENUM(BlueprintType)
enum class EAuracronProductType : uint8
{
    None                UMETA(DisplayName = "None"),
    Champion            UMETA(DisplayName = "Champion"),
    Skin                UMETA(DisplayName = "Skin"),
    BattlePass          UMETA(DisplayName = "Battle Pass"),
    CurrencyPack        UMETA(DisplayName = "Currency Pack"),
    Subscription        UMETA(DisplayName = "Subscription"),
    Cosmetic            UMETA(DisplayName = "Cosmetic"),
    Emote               UMETA(DisplayName = "Emote"),
    VoicePack           UMETA(DisplayName = "Voice Pack"),
    Bundle              UMETA(DisplayName = "Bundle"),
    SeasonalItem        UMETA(DisplayName = "Seasonal Item")
};

/**
 * EnumeraÃ§Ã£o para raridade de item
 */
UENUM(BlueprintType)
enum class EAuracronItemRarity : uint8
{
    Common              UMETA(DisplayName = "Common"),
    Uncommon            UMETA(DisplayName = "Uncommon"),
    Rare                UMETA(DisplayName = "Rare"),
    Epic                UMETA(DisplayName = "Epic"),
    Legendary           UMETA(DisplayName = "Legendary"),
    Mythic              UMETA(DisplayName = "Mythic"),
    Limited             UMETA(DisplayName = "Limited Edition"),
    Exclusive           UMETA(DisplayName = "Exclusive")
};

/**
 * Estrutura para produto da loja
 */
USTRUCT(BlueprintType)
struct AURACRONMONETIZATIONBRIDGE_API FAuracronStoreProduct
{
    GENERATED_BODY()

    /** ID Ãºnico do produto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    FString ProductID;

    /** Nome do produto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    FText ProductName;

    /** DescriÃ§Ã£o do produto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    FText ProductDescription;

    /** Tipo do produto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    EAuracronProductType ProductType = EAuracronProductType::Cosmetic;

    /** Raridade do item */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    EAuracronItemRarity ItemRarity = EAuracronItemRarity::Common;

    /** PreÃ§o em moeda premium */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product", meta = (ClampMin = "0"))
    int32 PremiumPrice = 0;

    /** PreÃ§o em moeda gratuita */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product", meta = (ClampMin = "0"))
    int32 FreePrice = 0;

    /** PreÃ§o em dinheiro real (centavos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product", meta = (ClampMin = "0"))
    int32 RealMoneyPrice = 0;

    /** Tipo de moeda aceita */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    EAuracronCurrencyType AcceptedCurrency = EAuracronCurrencyType::AuraCoins;

    /** Produto estÃ¡ disponÃ­vel */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    bool bIsAvailable = true;

    /** Produto Ã© limitado por tempo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    bool bIsTimeLimited = false;

    /** Data de expiraÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    FDateTime ExpirationDate;

    /** Desconto aplicado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float DiscountPercentage = 0.0f;

    /** Produto Ã© destaque */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    bool bIsFeatured = false;

    /** Produto Ã© novo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    bool bIsNew = false;

    /** Ãcone do produto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    TSoftObjectPtr<UTexture2D> ProductIcon;

    /** Imagem de preview */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    TSoftObjectPtr<UTexture2D> PreviewImage;

    /** VÃ­deo de preview */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    TSoftObjectPtr<UMediaSource> PreviewVideo;

    /** Categoria do produto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    FString ProductCategory;

    /** Tags do produto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    FGameplayTagContainer ProductTags;

    /** Metadados do produto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    TMap<FString, FString> ProductMetadata;

    /** Requer idade mÃ­nima */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product", meta = (ClampMin = "0", ClampMax = "21"))
    int32 MinimumAge = 0;

    /** RegiÃµes onde estÃ¡ disponÃ­vel */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Store Product")
    TArray<FString> AvailableRegions;
};

/**
 * Estrutura para Battle Pass
 */
USTRUCT(BlueprintType)
struct AURACRONMONETIZATIONBRIDGE_API FAuracronBattlePass
{
    GENERATED_BODY()

    /** ID do Battle Pass */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    FString BattlePassID;

    /** Nome do Battle Pass */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    FText BattlePassName;

    /** Temporada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    int32 Season = 1;

    /** NÃ­vel atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass", meta = (ClampMin = "1", ClampMax = "100"))
    int32 CurrentLevel = 1;

    /** XP atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass", meta = (ClampMin = "0"))
    int32 CurrentXP = 0;

    /** XP necessÃ¡rio para prÃ³ximo nÃ­vel */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass", meta = (ClampMin = "100"))
    int32 XPToNextLevel = 1000;

    /** Battle Pass premium foi comprado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    bool bPremiumPurchased = false;

    /** Data de inÃ­cio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    FDateTime StartDate;

    /** Data de fim */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    FDateTime EndDate;

    /** Recompensas gratuitas (não pode ser replicado) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass", NotReplicated)
    TMap<int32, FString> FreeRewards;

    /** Recompensas premium (não pode ser replicado) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass", NotReplicated)
    TMap<int32, FString> PremiumRewards;

    /** Recompensas coletadas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    TArray<int32> CollectedRewards;

    /** PreÃ§o do Battle Pass premium */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass", meta = (ClampMin = "0"))
    int32 PremiumPrice = 950; // Aura Coins

    /** Boost de XP ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    bool bXPBoostActive = false;

    /** Multiplicador de XP */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass", meta = (ClampMin = "1.0", ClampMax = "5.0"))
    float XPMultiplier = 1.0f;
};

/**
 * Estrutura para analytics de compras
 */
USTRUCT(BlueprintType)
struct AURACRONMONETIZATIONBRIDGE_API FAuracronPurchaseAnalytics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Purchase Analytics")
    FString TransactionID;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Purchase Analytics")
    FString ProductID;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Purchase Analytics")
    FString ProductName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Purchase Analytics")
    FString ProductCategory;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Purchase Analytics")
    int32 PricePaid;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Purchase Analytics")
    EAuracronCurrencyType CurrencyType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Purchase Analytics")
    int32 Quantity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Purchase Analytics")
    FDateTime PurchaseTimestamp;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Purchase Analytics")
    FString PlayerID;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Purchase Analytics")
    int32 PlayerLevel;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Purchase Analytics")
    float SessionDuration;

    FAuracronPurchaseAnalytics()
    {
        TransactionID = TEXT("");
        ProductID = TEXT("");
        ProductName = TEXT("");
        ProductCategory = TEXT("");
        PricePaid = 0;
        CurrencyType = EAuracronCurrencyType::None;
        Quantity = 1;
        PurchaseTimestamp = FDateTime::Now();
        PlayerID = TEXT("");
        PlayerLevel = 1;
        SessionDuration = 0.0f;
    }
};

/**
 * Estrutura para entrada de saldo de moeda (substitui TMap para replicação)
 */
USTRUCT(BlueprintType)
struct AURACRONMONETIZATIONBRIDGE_API FAuracronCurrencyBalanceEntry
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Currency Balance")
    EAuracronCurrencyType CurrencyType = EAuracronCurrencyType::AuraCoins;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Currency Balance")
    int32 Balance = 0;

    FAuracronCurrencyBalanceEntry()
    {
        CurrencyType = EAuracronCurrencyType::AuraCoins;
        Balance = 0;
    }

    FAuracronCurrencyBalanceEntry(EAuracronCurrencyType InCurrencyType, int32 InBalance)
        : CurrencyType(InCurrencyType), Balance(InBalance)
    {
    }
};

/**
 * Estrutura para transação
 */
USTRUCT(BlueprintType)
struct AURACRONMONETIZATIONBRIDGE_API FAuracronTransaction
{
    GENERATED_BODY()

    /** ID Ãºnico da transaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transaction")
    FString TransactionID;

    /** ID do jogador */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transaction")
    FString PlayerID;

    /** ID do produto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transaction")
    FString ProductID;

    /** Quantidade comprada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transaction", meta = (ClampMin = "1"))
    int32 Quantity = 1;

    /** PreÃ§o pago */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transaction", meta = (ClampMin = "0"))
    int32 PricePaid = 0;

    /** Tipo de moeda usada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transaction")
    EAuracronCurrencyType CurrencyUsed = EAuracronCurrencyType::AuraCoins;

    /** Timestamp da transaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transaction")
    FDateTime TransactionTime;

    /** Status da transaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transaction")
    FString TransactionStatus = TEXT("Pending");

    /** Plataforma da compra */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transaction")
    FString PurchasePlatform;

    /** Receipt da plataforma */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transaction")
    FString PlatformReceipt;

    /** TransaÃ§Ã£o foi validada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transaction")
    bool bIsValidated = false;

    /** TransaÃ§Ã£o foi processada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transaction")
    bool bIsProcessed = false;

    /** Dados adicionais */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transaction")
    TMap<FString, FString> AdditionalData;
};

/**
 * Classe principal do Bridge para Sistema de MonetizaÃ§Ã£o
 * ResponsÃ¡vel pelo gerenciamento completo de monetizaÃ§Ã£o Ã©tica
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Monetization", meta = (DisplayName = "AURACRON Monetization Bridge", BlueprintSpawnableComponent))
class AURACRONMONETIZATIONBRIDGE_API UAuracronMonetizationBridge : public UGameFrameworkComponent
{
    GENERATED_BODY()

public:
    UAuracronMonetizationBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Store Management ===

    /**
     * Carregar produtos da loja
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Store", CallInEditor)
    bool LoadStoreProducts();

    /**
     * Obter produtos por categoria
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Store", CallInEditor)
    TArray<FAuracronStoreProduct> GetProductsByCategory(const FString& Category) const;

    /**
     * Obter produtos em destaque
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Store", CallInEditor)
    TArray<FAuracronStoreProduct> GetFeaturedProducts() const;

    /**
     * Verificar se produto estÃ¡ disponÃ­vel
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Store", CallInEditor)
    bool IsProductAvailable(const FString& ProductID) const;

    /**
     * Obter preÃ§o do produto
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Store", CallInEditor)
    int32 GetProductPrice(const FString& ProductID, EAuracronCurrencyType CurrencyType) const;

    // === Purchase System ===

    /**
     * Iniciar compra
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Purchase", CallInEditor)
    bool InitiatePurchase(const FString& ProductID, int32 Quantity = 1);

    /**
     * Processar compra
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Purchase", CallInEditor)
    bool ProcessPurchase(const FAuracronTransaction& Transaction);

    /**
     * Validar compra
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Purchase", CallInEditor)
    bool ValidatePurchase(const FString& TransactionID, const FString& Receipt);

    /**
     * Finalizar compra
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Purchase", CallInEditor)
    bool FinalizePurchase(const FString& TransactionID);

    /**
     * Cancelar compra
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Purchase", CallInEditor)
    bool CancelPurchase(const FString& TransactionID);

    // === Currency Management ===

    /**
     * Obter saldo de moeda
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Currency", CallInEditor)
    int32 GetCurrencyBalance(EAuracronCurrencyType CurrencyType) const;

    /**
     * Adicionar moeda
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Currency", CallInEditor)
    bool AddCurrency(EAuracronCurrencyType CurrencyType, int32 Amount, const FString& Source);

    /**
     * Gastar moeda
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Currency", CallInEditor)
    bool SpendCurrency(EAuracronCurrencyType CurrencyType, int32 Amount, const FString& Purpose);

    /**
     * Transferir moeda
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Currency", CallInEditor)
    bool TransferCurrency(const FString& ToPlayerID, EAuracronCurrencyType CurrencyType, int32 Amount);

    // === Battle Pass ===

    /**
     * Comprar Battle Pass
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|BattlePass", CallInEditor)
    bool PurchaseBattlePass(int32 Season);

    /**
     * Adicionar XP ao Battle Pass
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|BattlePass", CallInEditor)
    bool AddBattlePassXP(int32 XPAmount, const FString& Source);

    /**
     * Coletar recompensa do Battle Pass
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|BattlePass", CallInEditor)
    bool ClaimBattlePassReward(int32 Level, bool bPremiumReward = false);

    /**
     * Obter progresso do Battle Pass
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|BattlePass", CallInEditor)
    FAuracronBattlePass GetBattlePassProgress() const;

    // === Gifting System ===

    /**
     * Enviar presente
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Gifting", CallInEditor)
    bool SendGift(const FString& RecipientID, const FString& ProductID, const FText& Message);

    /**
     * Receber presente
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Gifting", CallInEditor)
    bool ReceiveGift(const FString& GiftID);

    /**
     * Obter presentes pendentes
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Gifting", CallInEditor)
    TArray<FString> GetPendingGifts() const;

    // === Subscription ===

    /**
     * Ativar assinatura
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Subscription", CallInEditor)
    bool ActivateSubscription(const FString& SubscriptionType);

    /**
     * Cancelar assinatura
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Subscription", CallInEditor)
    bool CancelSubscription();

    /**
     * Verificar status da assinatura
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Subscription", CallInEditor)
    bool IsSubscriptionActive() const;

    // === Analytics ===

    /**
     * Rastrear compra
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Analytics", CallInEditor)
    bool TrackPurchase(const FAuracronTransaction& Transaction);

    /**
     * Obter estatÃ­sticas de monetizaÃ§Ã£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Monetization|Analytics", CallInEditor)
    TMap<FString, float> GetMonetizationStatistics() const;

protected:
    // === Internal Methods ===
    
    /** Inicializar sistema de monetizaÃ§Ã£o */
    bool InitializeMonetizationSystem();
    
    /** Configurar loja */
    bool SetupStore();
    
    /** Processar transaÃ§Ãµes pendentes */
    void ProcessPendingTransactions(float DeltaTime);
    
    /** Validar produto */
    bool ValidateProduct(const FAuracronStoreProduct& Product) const;
    
    /** Atualizar estatísticas de monetização */
    void UpdateMonetizationStatistics(const FAuracronPurchaseAnalytics& PurchaseEvent);
    
    /** Enviar evento de analytics */
    void SendAnalyticsEvent(const FAuracronPurchaseAnalytics& PurchaseEvent);

public:
    // === Configuration Properties ===

    /** Produtos da loja */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TArray<FAuracronStoreProduct> StoreProducts;

    /** Battle Pass atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    FAuracronBattlePass CurrentBattlePass;

    /** Saldos de moeda */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    TArray<FAuracronCurrencyBalanceEntry> CurrencyBalances;

    /** TransaÃ§Ãµes ativas */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    TArray<FAuracronTransaction> ActiveTransactions;

    /** Produtos possuÃ­dos */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    TArray<FString> OwnedProducts;

private:
    // === Internal State ===
    
    /** Sistema inicializado */
    bool bSystemInitialized = false;
    
    /** Timer para processamento */
    FTimerHandle ProcessingTimer;
    
    /** Mutex para thread safety */
    mutable FCriticalSection MonetizationMutex;

    /** Histórico de analytics de compras */
    TArray<FAuracronPurchaseAnalytics> PurchaseAnalyticsHistory;

public:
    // === Delegates ===
    
    /** Delegate chamado quando compra Ã© completada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPurchaseCompleted, FAuracronTransaction, Transaction);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Monetization|Events")
    FOnPurchaseCompleted OnPurchaseCompleted;
    
    /** Delegate chamado quando Battle Pass level up */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnBattlePassLevelUp, int32, OldLevel, int32, NewLevel);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Monetization|Events")
    FOnBattlePassLevelUp OnBattlePassLevelUp;
};

