// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Realms Dinâmicos Bridge Implementation

#include "AuracronRealmsBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/GameStateBase.h"
#include "Components/TimelineComponent.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerInstance.h"
#include "WorldPartition/DataLayer/WorldDataLayers.h"
#include "PCG/Public/PCGComponent.h"
#include "PCG/Public/PCGSubsystem.h"
#include "PCG/Public/PCGGraph.h"
#include "PCG/Public/PCGManagedResource.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/AudioComponent.h"
#include "Kismet/GameplayStatics.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Engine/ActorChannel.h"
#include "DrawDebugHelpers.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "Landscape.h"
#include "InstancedFoliageActor.h"
#include "NavigationSystem.h"
#include "AI/NavigationSystemBase.h"
#include "Engine/DirectionalLight.h"
#include "Engine/SkyLight.h"
#include "Components/LightComponent.h"
#include "Components/SkyLightComponent.h"
#include "Kismet/KismetMaterialLibrary.h"

UAuracronRealmsBridge::UAuracronRealmsBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // 10 FPS para otimização
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Criar componentes de timeline
    TransitionTimeline = CreateDefaultSubobject<UTimelineComponent>(TEXT("TransitionTimeline"));
    EvolutionTimeline = CreateDefaultSubobject<UTimelineComponent>(TEXT("EvolutionTimeline"));
    
    // Inicializar configurações padrão
    LoadDefaultRealmConfigurations();
}

void UAuracronRealmsBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Realms Dinâmicos"));

    // Obter referências aos subsistemas
    if (UWorld* World = GetWorld())
    {
        WorldPartitionSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>();
        DataLayerSubsystem = World->GetSubsystem<UDataLayerSubsystem>();
        PCGSubsystem = World->GetSubsystem<UPCGSubsystem>();
        
        if (!WorldPartitionSubsystem)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: WorldPartitionSubsystem não encontrado"));
        }
        
        if (!DataLayerSubsystem)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: DataLayerSubsystem não encontrado"));
        }
        
        if (!PCGSubsystem)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: PCGSubsystem não encontrado"));
        }
    }

    // Inicializar sistema
    bSystemInitialized = InitializeRealmSystem();
    
    if (bSystemInitialized)
    {
        // Configurar timer para verificações periódicas
        GetWorld()->GetTimerManager().SetTimer(
            SystemUpdateTimer,
            [this]()
            {
                // Verificar evoluções programadas
                float GameTime = GetWorld()->GetTimeSeconds();
                for (int32 i = 0; i < ScheduledEvolutions.Num(); ++i)
                {
                    const FAuracronMapEvolution& Evolution = ScheduledEvolutions[i];
                    if (Evolution.bEvolutionActive && GameTime >= Evolution.StartTime && CurrentEvolutionIndex == -1)
                    {
                        StartMapEvolution(Evolution);
                        CurrentEvolutionIndex = i;
                        break;
                    }
                }
            },
            5.0f, // A cada 5 segundos
            true  // Loop
        );
        
        // Ativar Realm inicial
        ActivateRealm(EAuracronRealmType::PlanicieRadiante);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Realms inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Realms"));
    }
}

void UAuracronRealmsBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(SystemUpdateTimer);
        GetWorld()->GetTimerManager().ClearTimer(EvolutionTimer);
    }
    
    // Limpar componentes PCG
    for (auto& RealmPCGPair : ActivePCGComponents)
    {
        for (UPCGComponent* Component : RealmPCGPair.Value)
        {
            if (IsValid(Component))
            {
                Component->CleanupLocalImmediate(true);
                Component->DestroyComponent();
            }
        }
    }
    ActivePCGComponents.Empty();
    
    // Limpar efeitos visuais
    for (UNiagaraComponent* Component : ActiveRealmEffects)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }
    ActiveRealmEffects.Empty();

    Super::EndPlay(EndPlayReason);
}

void UAuracronRealmsBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronRealmsBridge, RealmConfigurations);
    DOREPLIFETIME(UAuracronRealmsBridge, DimensionalPortals);
    DOREPLIFETIME(UAuracronRealmsBridge, VerticalConnectors);
    DOREPLIFETIME(UAuracronRealmsBridge, ProceduralConfigs);
    DOREPLIFETIME(UAuracronRealmsBridge, ScheduledEvolutions);
    DOREPLIFETIME(UAuracronRealmsBridge, CurrentActiveRealm);
    DOREPLIFETIME(UAuracronRealmsBridge, CurrentTransitionState);
    DOREPLIFETIME(UAuracronRealmsBridge, TransitionStartTime);
    DOREPLIFETIME(UAuracronRealmsBridge, TransitionFromRealm);
    DOREPLIFETIME(UAuracronRealmsBridge, TransitionToRealm);
    DOREPLIFETIME(UAuracronRealmsBridge, CurrentEvolutionIndex);
    DOREPLIFETIME(UAuracronRealmsBridge, EvolutionStartTime);
}

void UAuracronRealmsBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar transição em andamento
    if (CurrentTransitionState == EAuracronRealmTransitionState::Transitioning)
    {
        ProcessRealmTransition(DeltaTime);
    }

    // Processar evolução em andamento
    if (CurrentTransitionState == EAuracronRealmTransitionState::Evolving)
    {
        ProcessMapEvolution(DeltaTime);
    }
}

// === Core Realm Management ===

bool UAuracronRealmsBridge::ActivateRealm(EAuracronRealmType RealmType)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado"));
        return false;
    }

    if (RealmType == EAuracronRealmType::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tentativa de ativar Realm None"));
        return false;
    }

    if (CurrentActiveRealm == RealmType)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Realm %d já está ativo"), (int32)RealmType);
        return true;
    }

    // Carregar Data Layers do Realm
    if (!LoadRealmDataLayers(RealmType))
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao carregar Data Layers do Realm %d"), (int32)RealmType);
        return false;
    }

    // Executar geração procedural se necessário
    if (ProceduralConfigs.Contains(RealmType))
    {
        ExecuteProceduralGeneration(RealmType);
    }

    // Atualizar efeitos ambientais
    UpdateAmbientEffects(RealmType);

    CurrentActiveRealm = RealmType;
    LoadedRealms.Add(RealmType);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Realm %d ativado com sucesso"), (int32)RealmType);

    // Broadcast evento
    OnRealmActivated.Broadcast(RealmType);

    return true;
}

bool UAuracronRealmsBridge::DeactivateRealm(EAuracronRealmType RealmType)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado"));
        return false;
    }

    if (RealmType == EAuracronRealmType::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tentativa de desativar Realm None"));
        return false;
    }

    if (!LoadedRealms.Contains(RealmType))
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Realm %d já está desativado"), (int32)RealmType);
        return true;
    }

    // Descarregar Data Layers do Realm
    if (!UnloadRealmDataLayers(RealmType))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Falha ao descarregar Data Layers do Realm %d"), (int32)RealmType);
    }

    // Limpar conteúdo procedural
    ClearProceduralContent(RealmType);

    LoadedRealms.Remove(RealmType);

    // Se era o Realm ativo, definir um novo ativo
    if (CurrentActiveRealm == RealmType)
    {
        // Ativar Planície Radiante como padrão
        CurrentActiveRealm = EAuracronRealmType::PlanicieRadiante;
        if (!LoadedRealms.Contains(CurrentActiveRealm))
        {
            ActivateRealm(CurrentActiveRealm);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Realm %d desativado com sucesso"), (int32)RealmType);

    // Broadcast evento
    OnRealmDeactivated.Broadcast(RealmType);

    return true;
}

bool UAuracronRealmsBridge::ToggleRealmVisibility(EAuracronRealmType RealmType)
{
    if (LoadedRealms.Contains(RealmType))
    {
        return DeactivateRealm(RealmType);
    }
    else
    {
        return ActivateRealm(RealmType);
    }
}

TArray<EAuracronRealmType> UAuracronRealmsBridge::GetActiveRealms() const
{
    TArray<EAuracronRealmType> ActiveRealms;
    for (EAuracronRealmType RealmType : LoadedRealms)
    {
        ActiveRealms.Add(RealmType);
    }
    return ActiveRealms;
}

bool UAuracronRealmsBridge::IsRealmActive(EAuracronRealmType RealmType) const
{
    return LoadedRealms.Contains(RealmType);
}

// === Transition Management ===

bool UAuracronRealmsBridge::StartRealmTransition(EAuracronRealmType FromRealm, EAuracronRealmType ToRealm)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado"));
        return false;
    }

    if (FromRealm == ToRealm || FromRealm == EAuracronRealmType::None || ToRealm == EAuracronRealmType::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Parâmetros de transição inválidos"));
        return false;
    }

    if (CurrentTransitionState != EAuracronRealmTransitionState::Stable)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Transição já em andamento"));
        return false;
    }

    CurrentTransitionState = EAuracronRealmTransitionState::Transitioning;
    TransitionFromRealm = FromRealm;
    TransitionToRealm = ToRealm;
    TransitionStartTime = GetWorld()->GetTimeSeconds();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Iniciando transição de %d para %d"), (int32)FromRealm, (int32)ToRealm);

    // Carregar Realm de destino
    ActivateRealm(ToRealm);

    // Configurar timeline de transição
    if (TransitionTimeline)
    {
        TransitionTimeline->SetLooping(false);
        TransitionTimeline->PlayFromStart();
    }

    // Broadcast evento
    OnTransitionStarted.Broadcast(FromRealm, ToRealm);

    return true;
}

bool UAuracronRealmsBridge::CompleteRealmTransition()
{
    if (CurrentTransitionState != EAuracronRealmTransitionState::Transitioning)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Nenhuma transição em andamento"));
        return false;
    }

    CurrentActiveRealm = TransitionToRealm;
    CurrentTransitionState = EAuracronRealmTransitionState::Stable;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Transição completada para Realm %d"), (int32)TransitionToRealm);

    // Broadcast evento
    OnTransitionCompleted.Broadcast(TransitionFromRealm, TransitionToRealm);

    // Reset valores de transição
    TransitionFromRealm = EAuracronRealmType::None;
    TransitionToRealm = EAuracronRealmType::None;
    TransitionStartTime = 0.0f;

    return true;
}

bool UAuracronRealmsBridge::CancelRealmTransition()
{
    if (CurrentTransitionState != EAuracronRealmTransitionState::Transitioning)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Nenhuma transição em andamento para cancelar"));
        return false;
    }

    CurrentTransitionState = EAuracronRealmTransitionState::Stable;

    // Parar timeline
    if (TransitionTimeline)
    {
        TransitionTimeline->Stop();
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Transição cancelada"));

    // Reset valores
    TransitionFromRealm = EAuracronRealmType::None;
    TransitionToRealm = EAuracronRealmType::None;
    TransitionStartTime = 0.0f;

    return true;
}

float UAuracronRealmsBridge::GetTransitionProgress() const
{
    if (CurrentTransitionState != EAuracronRealmTransitionState::Transitioning)
    {
        return 0.0f;
    }

    float GameTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    float ElapsedTime = GameTime - TransitionStartTime;
    float TransitionDuration = 3.0f; // 3 segundos de transição padrão

    return FMath::Clamp(ElapsedTime / TransitionDuration, 0.0f, 1.0f);
}

// === Portal Management ===

int32 UAuracronRealmsBridge::CreateDimensionalPortal(const FAuracronDimensionalPortal& PortalConfig)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado"));
        return -1;
    }

    if (PortalConfig.SourceRealm == EAuracronRealmType::None || PortalConfig.DestinationRealm == EAuracronRealmType::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração de portal inválida"));
        return -1;
    }

    int32 PortalIndex = DimensionalPortals.Add(PortalConfig);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Portal dimensional criado: %d (de %d para %d)"),
        PortalIndex, (int32)PortalConfig.SourceRealm, (int32)PortalConfig.DestinationRealm);

    // Criar efeitos visuais do portal
    if (PortalConfig.PortalParticleSystem.IsValid())
    {
        UNiagaraSystem* ParticleSystem = PortalConfig.PortalParticleSystem.LoadSynchronous();
        if (ParticleSystem && GetOwner())
        {
            UNiagaraComponent* PortalEffect = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                ParticleSystem,
                PortalConfig.PortalLocation,
                PortalConfig.PortalRotation,
                PortalConfig.PortalScale,
                true
            );

            if (PortalEffect)
            {
                ActiveRealmEffects.Add(PortalEffect);
            }
        }
    }

    // Broadcast evento
    OnPortalCreated.Broadcast(PortalIndex);

    return PortalIndex;
}

bool UAuracronRealmsBridge::RemoveDimensionalPortal(int32 PortalIndex)
{
    if (!DimensionalPortals.IsValidIndex(PortalIndex))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Índice de portal inválido: %d"), PortalIndex);
        return false;
    }

    DimensionalPortals.RemoveAt(PortalIndex);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Portal dimensional removido: %d"), PortalIndex);

    // Broadcast evento
    OnPortalRemoved.Broadcast(PortalIndex);

    return true;
}

bool UAuracronRealmsBridge::SetPortalActive(int32 PortalIndex, bool bActive)
{
    if (!DimensionalPortals.IsValidIndex(PortalIndex))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Índice de portal inválido: %d"), PortalIndex);
        return false;
    }

    DimensionalPortals[PortalIndex].bPortalActive = bActive;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Portal %d %s"), PortalIndex, bActive ? TEXT("ativado") : TEXT("desativado"));

    return true;
}

TArray<FAuracronDimensionalPortal> UAuracronRealmsBridge::GetActivePortals() const
{
    TArray<FAuracronDimensionalPortal> ActivePortals;

    for (const FAuracronDimensionalPortal& Portal : DimensionalPortals)
    {
        if (Portal.bPortalActive)
        {
            ActivePortals.Add(Portal);
        }
    }

    return ActivePortals;
}

int32 UAuracronRealmsBridge::FindNearestPortal(const FVector& Location, float MaxDistance) const
{
    int32 NearestPortalIndex = -1;
    float NearestDistance = MaxDistance;

    for (int32 i = 0; i < DimensionalPortals.Num(); ++i)
    {
        const FAuracronDimensionalPortal& Portal = DimensionalPortals[i];
        if (!Portal.bPortalActive)
        {
            continue;
        }

        float Distance = FVector::Dist(Location, Portal.PortalLocation);
        if (Distance < NearestDistance)
        {
            NearestDistance = Distance;
            NearestPortalIndex = i;
        }
    }

    return NearestPortalIndex;
}

// === Procedural Generation ===

bool UAuracronRealmsBridge::ExecuteProceduralGeneration(EAuracronRealmType RealmType)
{
    if (!bSystemInitialized || !PCGSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou PCGSubsystem inválido"));
        return false;
    }

    const FAuracronProceduralGenerationConfig* Config = ProceduralConfigs.Find(RealmType);
    if (!Config || !Config->bGenerationActive)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração PCG não encontrada ou inativa para Realm %d"), (int32)RealmType);
        return false;
    }

    return ExecutePCGForRealm(RealmType, *Config);
}

bool UAuracronRealmsBridge::RegenerateProceduralContent(EAuracronRealmType RealmType, bool bForceRegeneration)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado"));
        return false;
    }

    // Limpar conteúdo existente se forçado
    if (bForceRegeneration)
    {
        ClearProceduralContent(RealmType);
    }

    // Executar nova geração
    return ExecuteProceduralGeneration(RealmType);
}

bool UAuracronRealmsBridge::ClearProceduralContent(EAuracronRealmType RealmType)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    // Limpar componentes PCG do Realm
    if (ActivePCGComponents.Contains(RealmType))
    {
        TArray<TObjectPtr<UPCGComponent>>& Components = ActivePCGComponents[RealmType];
        for (UPCGComponent* Component : Components)
        {
            if (IsValid(Component))
            {
                Component->CleanupLocalImmediate(true);
                Component->DestroyComponent();
            }
        }
        Components.Empty();
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Conteúdo procedural limpo para Realm %d"), (int32)RealmType);
    return true;
}

float UAuracronRealmsBridge::GetProceduralGenerationProgress(EAuracronRealmType RealmType) const
{
    if (!ActivePCGComponents.Contains(RealmType))
    {
        return 0.0f;
    }

    const TArray<TObjectPtr<UPCGComponent>>& Components = ActivePCGComponents[RealmType];
    if (Components.Num() == 0)
    {
        return 1.0f; // Considerado completo se não há componentes
    }

    int32 CompletedComponents = 0;
    for (const UPCGComponent* Component : Components)
    {
        if (IsValid(Component) && !Component->IsGenerating())
        {
            CompletedComponents++;
        }
    }

    return static_cast<float>(CompletedComponents) / static_cast<float>(Components.Num());
}

// === Map Evolution ===

bool UAuracronRealmsBridge::StartMapEvolution(const FAuracronMapEvolution& EvolutionConfig)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado"));
        return false;
    }

    if (CurrentTransitionState == EAuracronRealmTransitionState::Evolving)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Evolução já em andamento"));
        return false;
    }

    CurrentTransitionState = EAuracronRealmTransitionState::Evolving;
    EvolutionStartTime = GetWorld()->GetTimeSeconds();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Iniciando evolução do mapa"));

    // Executar PCG de evolução para Realms afetados
    for (EAuracronRealmType RealmType : EvolutionConfig.AffectedRealms)
    {
        for (const TSoftObjectPtr<UPCGGraph>& PCGGraph : EvolutionConfig.EvolutionPCGGraphs)
        {
            if (PCGGraph.IsValid())
            {
                // Criar configuração temporária para evolução
                FAuracronProceduralGenerationConfig TempConfig;
                TempConfig.TargetRealm = RealmType;
                TempConfig.MainPCGGraph = PCGGraph;
                TempConfig.bGenerationActive = true;

                ExecutePCGForRealm(RealmType, TempConfig);
            }
        }
    }

    // Criar novos portais
    for (const FAuracronDimensionalPortal& NewPortal : EvolutionConfig.NewPortals)
    {
        CreateDimensionalPortal(NewPortal);
    }

    // Configurar timer para completar evolução
    GetWorld()->GetTimerManager().SetTimer(
        EvolutionTimer,
        [this]()
        {
            CurrentTransitionState = EAuracronRealmTransitionState::Stable;
            CurrentEvolutionIndex = -1;
            EvolutionStartTime = 0.0f;

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Evolução do mapa completada"));
            OnEvolutionCompleted.Broadcast(CurrentEvolutionIndex);
        },
        EvolutionConfig.Duration,
        false
    );

    // Broadcast evento
    OnEvolutionStarted.Broadcast(CurrentEvolutionIndex);

    return true;
}

// === Internal Management Methods ===

bool UAuracronRealmsBridge::InitializeRealmSystem()
{
    if (!GetOwner())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Owner inválido"));
        return false;
    }

    // Configurar World Partition
    if (!SetupWorldPartition())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Falha ao configurar World Partition"));
        return false;
    }

    // Configurar PCG Subsystem
    if (!SetupPCGSubsystem())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Falha ao configurar PCG Subsystem"));
        return false;
    }

    // Configurar timelines
    if (TransitionTimeline)
    {
        TransitionTimeline->SetLooping(false);
        TransitionTimeline->SetIgnoreTimeDilation(false);
    }

    if (EvolutionTimeline)
    {
        EvolutionTimeline->SetLooping(false);
        EvolutionTimeline->SetIgnoreTimeDilation(false);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Realms inicializado"));
    return true;
}

bool UAuracronRealmsBridge::SetupWorldPartition()
{
    if (!WorldPartitionSubsystem)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: WorldPartitionSubsystem não disponível"));
        return false;
    }

    // Verificar se World Partition está habilitado
    UWorld* World = GetWorld();
    if (!World || !World->GetWorldPartition())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: World Partition não está habilitado neste mundo"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: World Partition configurado com sucesso"));
    return true;
}

bool UAuracronRealmsBridge::SetupPCGSubsystem()
{
    if (!PCGSubsystem)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: PCGSubsystem não disponível"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: PCG Subsystem configurado com sucesso"));
    return true;
}

void UAuracronRealmsBridge::ProcessRealmTransition(float DeltaTime)
{
    if (CurrentTransitionState != EAuracronRealmTransitionState::Transitioning)
    {
        return;
    }

    float Progress = GetTransitionProgress();

    // Completar transição automaticamente quando progresso chega a 100%
    if (Progress >= 1.0f)
    {
        CompleteRealmTransition();
    }
}

void UAuracronRealmsBridge::ProcessMapEvolution(float DeltaTime)
{
    if (CurrentTransitionState != EAuracronRealmTransitionState::Evolving)
    {
        return;
    }

    float Progress = GetEvolutionProgress();

    // A evolução é gerenciada pelo timer, mas podemos atualizar efeitos aqui
    if (Progress >= 1.0f && CurrentEvolutionIndex != -1)
    {
        // Evolução completada
        CurrentTransitionState = EAuracronRealmTransitionState::Stable;
        OnEvolutionCompleted.Broadcast(CurrentEvolutionIndex);
        CurrentEvolutionIndex = -1;
        EvolutionStartTime = 0.0f;
    }
}

bool UAuracronRealmsBridge::UpdateDataLayers(EAuracronRealmType RealmType, bool bLoad)
{
    if (bLoad)
    {
        return LoadRealmDataLayers(RealmType);
    }
    else
    {
        return UnloadRealmDataLayers(RealmType);
    }
}

bool UAuracronRealmsBridge::ExecutePCGForRealm(EAuracronRealmType RealmType, const FAuracronProceduralGenerationConfig& Config)
{
    if (!PCGSubsystem || !Config.MainPCGGraph.IsValid())
    {
        return false;
    }

    UPCGGraph* PCGGraph = Config.MainPCGGraph.LoadSynchronous();
    if (!PCGGraph)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Falha ao carregar PCG Graph para Realm %d"), (int32)RealmType);
        return false;
    }

    // Criar componente PCG
    UPCGComponent* PCGComponent = NewObject<UPCGComponent>(GetOwner());
    if (!PCGComponent)
    {
        return false;
    }

    PCGComponent->SetGraph(PCGGraph);
    PCGComponent->SetGenerationTrigger(EPCGComponentGenerationTrigger::GenerateOnDemand);

    // Configurar seed
    PCGComponent->Seed = Config.GenerationSeed;

    // Adicionar à lista de componentes ativos
    if (!ActivePCGComponents.Contains(RealmType))
    {
        ActivePCGComponents.Add(RealmType, TArray<TObjectPtr<UPCGComponent>>());
    }
    ActivePCGComponents[RealmType].Add(PCGComponent);

    // Executar geração
    PCGComponent->GenerateLocal(true);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: PCG executado para Realm %d"), (int32)RealmType);
    return true;
}

bool UAuracronRealmsBridge::ValidateRealmConfiguration(const FAuracronRealmConfiguration& Configuration) const
{
    // Validar alturas
    if (Configuration.MinHeight >= Configuration.MaxHeight)
    {
        return false;
    }

    // Validar tipo de Realm
    if (Configuration.RealmType == EAuracronRealmType::None)
    {
        return false;
    }

    // Validar parâmetros numéricos
    if (Configuration.LightingIntensity < 0.0f || Configuration.FogDensity < 0.0f)
    {
        return false;
    }

    return true;
}

bool UAuracronRealmsBridge::UpdateAmbientEffects(EAuracronRealmType RealmType)
{
    const FAuracronRealmConfiguration* Config = RealmConfigurations.Find(RealmType);
    if (!Config)
    {
        return false;
    }

    // Atualizar parâmetros de material global
    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }

    // Configurar iluminação direcional baseada no Realm
    if (ADirectionalLight* DirectionalLight = Cast<ADirectionalLight>(UGameplayStatics::GetActorOfClass(World, ADirectionalLight::StaticClass())))
    {
        ULightComponent* LightComponent = DirectionalLight->GetLightComponent();
        if (LightComponent)
        {
            // Ajustar cor e intensidade baseado no tipo de Realm
            FLinearColor RealmLightColor = FLinearColor::White;
            float LightIntensity = 3.0f;
            
            switch (RealmType)
            {
                case EAuracronRealmType::Fogo:
                    RealmLightColor = FLinearColor(1.0f, 0.4f, 0.1f, 1.0f); // Laranja avermelhado
                    LightIntensity = 4.0f;
                    break;
                case EAuracronRealmType::Agua:
                    RealmLightColor = FLinearColor(0.2f, 0.6f, 1.0f, 1.0f); // Azul claro
                    LightIntensity = 2.5f;
                    break;
                case EAuracronRealmType::Terra:
                    RealmLightColor = FLinearColor(0.8f, 0.6f, 0.3f, 1.0f); // Marrom dourado
                    LightIntensity = 3.5f;
                    break;
                case EAuracronRealmType::Ar:
                    RealmLightColor = FLinearColor(0.9f, 0.9f, 1.0f, 1.0f); // Branco azulado
                    LightIntensity = 3.0f;
                    break;
                case EAuracronRealmType::Luz:
                    RealmLightColor = FLinearColor(1.0f, 1.0f, 0.8f, 1.0f); // Dourado claro
                    LightIntensity = 5.0f;
                    break;
                case EAuracronRealmType::Sombra:
                    RealmLightColor = FLinearColor(0.3f, 0.2f, 0.4f, 1.0f); // Roxo escuro
                    LightIntensity = 1.5f;
                    break;
                default:
                    break;
            }
            
            LightComponent->SetLightColor(RealmLightColor);
            LightComponent->SetIntensity(LightIntensity);
        }
    }

    // Atualizar SkyLight para cor ambiente
    if (ASkyLight* SkyLight = Cast<ASkyLight>(UGameplayStatics::GetActorOfClass(World, ASkyLight::StaticClass())))
    {
        USkyLightComponent* SkyLightComponent = SkyLight->GetLightComponent();
        if (SkyLightComponent)
        {
            // Ajustar cor ambiente baseada no Realm
            FLinearColor AmbientColor = FLinearColor(0.1f, 0.1f, 0.15f, 1.0f);
            
            switch (RealmType)
            {
                case EAuracronRealmType::Fogo:
                    AmbientColor = FLinearColor(0.2f, 0.1f, 0.05f, 1.0f);
                    break;
                case EAuracronRealmType::Agua:
                    AmbientColor = FLinearColor(0.05f, 0.1f, 0.2f, 1.0f);
                    break;
                case EAuracronRealmType::Terra:
                    AmbientColor = FLinearColor(0.15f, 0.12f, 0.08f, 1.0f);
                    break;
                case EAuracronRealmType::Ar:
                    AmbientColor = FLinearColor(0.12f, 0.12f, 0.15f, 1.0f);
                    break;
                case EAuracronRealmType::Luz:
                    AmbientColor = FLinearColor(0.2f, 0.2f, 0.15f, 1.0f);
                    break;
                case EAuracronRealmType::Sombra:
                    AmbientColor = FLinearColor(0.05f, 0.03f, 0.08f, 1.0f);
                    break;
                default:
                    break;
            }
            
            SkyLightComponent->SetLightColor(AmbientColor);
            SkyLightComponent->RecaptureSky();
        }
    }

    // Atualizar parâmetros de material global via Material Parameter Collection
    if (Config->MaterialParameterCollection)
    {
        UKismetMaterialLibrary::SetScalarParameterValue(World, Config->MaterialParameterCollection, 
            TEXT("RealmType"), (float)(int32)RealmType);
        
        // Definir cores específicas do Realm para materiais
        FLinearColor RealmPrimaryColor = FLinearColor::White;
        FLinearColor RealmSecondaryColor = FLinearColor::Gray;
        
        switch (RealmType)
        {
            case EAuracronRealmType::Fogo:
                RealmPrimaryColor = FLinearColor(1.0f, 0.3f, 0.0f, 1.0f);
                RealmSecondaryColor = FLinearColor(1.0f, 0.6f, 0.0f, 1.0f);
                break;
            case EAuracronRealmType::Agua:
                RealmPrimaryColor = FLinearColor(0.0f, 0.4f, 1.0f, 1.0f);
                RealmSecondaryColor = FLinearColor(0.0f, 0.7f, 0.8f, 1.0f);
                break;
            case EAuracronRealmType::Terra:
                RealmPrimaryColor = FLinearColor(0.6f, 0.4f, 0.2f, 1.0f);
                RealmSecondaryColor = FLinearColor(0.4f, 0.6f, 0.2f, 1.0f);
                break;
            case EAuracronRealmType::Ar:
                RealmPrimaryColor = FLinearColor(0.8f, 0.9f, 1.0f, 1.0f);
                RealmSecondaryColor = FLinearColor(0.6f, 0.8f, 0.9f, 1.0f);
                break;
            case EAuracronRealmType::Luz:
                RealmPrimaryColor = FLinearColor(1.0f, 1.0f, 0.7f, 1.0f);
                RealmSecondaryColor = FLinearColor(1.0f, 0.9f, 0.5f, 1.0f);
                break;
            case EAuracronRealmType::Sombra:
                RealmPrimaryColor = FLinearColor(0.2f, 0.1f, 0.3f, 1.0f);
                RealmSecondaryColor = FLinearColor(0.4f, 0.2f, 0.5f, 1.0f);
                break;
            default:
                break;
        }
        
        UKismetMaterialLibrary::SetVectorParameterValue(World, Config->MaterialParameterCollection, 
            TEXT("RealmPrimaryColor"), RealmPrimaryColor);
        UKismetMaterialLibrary::SetVectorParameterValue(World, Config->MaterialParameterCollection, 
            TEXT("RealmSecondaryColor"), RealmSecondaryColor);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Efeitos ambientais atualizados para Realm %d com iluminação e cores específicas"), (int32)RealmType);
    return true;
}

bool UAuracronRealmsBridge::SynchronizeWithOtherSystems()
{
    // Sincronizar com sistema de navegação
    if (UNavigationSystemV1* NavSys = FNavigationSystem::GetCurrent<UNavigationSystemV1>(GetWorld()))
    {
        NavSys->Build();
    }

    return true;
}

// === Replication Callbacks ===

void UAuracronRealmsBridge::OnRep_ActiveRealm()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Realm ativo replicado: %d"), (int32)CurrentActiveRealm);

    // Atualizar efeitos locais baseados no novo Realm ativo
    UpdateAmbientEffects(CurrentActiveRealm);

    // Broadcast evento local
    OnRealmActivated.Broadcast(CurrentActiveRealm);
}

void UAuracronRealmsBridge::OnRep_TransitionState()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Estado de transição replicado: %d"), (int32)CurrentTransitionState);

    // Atualizar efeitos baseados no novo estado
    switch (CurrentTransitionState)
    {
        case EAuracronRealmTransitionState::Transitioning:
            OnTransitionStarted.Broadcast(TransitionFromRealm, TransitionToRealm);
            break;

        case EAuracronRealmTransitionState::Stable:
            OnTransitionCompleted.Broadcast(TransitionFromRealm, TransitionToRealm);
            break;

        case EAuracronRealmTransitionState::Evolving:
            OnEvolutionStarted.Broadcast(CurrentEvolutionIndex);
            break;

        default:
            break;
    }
}
