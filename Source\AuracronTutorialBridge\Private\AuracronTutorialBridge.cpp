// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Tutorial Bridge Implementation

#include "AuracronTutorialBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"
#include "Blueprint/UserWidget.h"
#include "Components/Widget.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Kismet/GameplayStatics.h"
#include "AudioDeviceNotificationSubsystem.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundWave.h"
#include "GameFramework/SaveGame.h"
#include "Engine/GameInstance.h"
#include "AuracronTutorialSaveGame.h"
#include "EnhancedInputComponent.h"
#include "AbilitySystemComponent.h"
#include "GameplayAbilitySpec.h"
#include "GameplayTagContainer.h"

UAuracronTutorialBridge::UAuracronTutorialBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // 10 FPS para tutorial responsivo
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Estado inicial
    CurrentTutorialState = EAuracronTutorialState::NotStarted;
    CurrentStepIndex = 0;
    TutorialProgress = 0.0f;
    bAIMentorActive = false;
}

void UAuracronTutorialBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Tutorial"));

    // Inicializar sistema
    bSystemInitialized = InitializeTutorialSystem();
    
    if (bSystemInitialized)
    {
        // Carregar progresso salvo
        LoadTutorialProgress();
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Tutorial inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Tutorial"));
    }
}

void UAuracronTutorialBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Salvar progresso antes de sair
    if (CurrentTutorialState == EAuracronTutorialState::InProgress)
    {
        SaveTutorialProgress();
    }
    
    // Limpar widget atual
    if (CurrentTutorialWidget)
    {
        CurrentTutorialWidget->RemoveFromParent();
        CurrentTutorialWidget = nullptr;
    }
    
    // Limpar tutoriais completados
    CompletedTutorials.Empty();

    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(StepTimer);
        GetWorld()->GetTimerManager().ClearTimer(TimeoutTimer);
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronTutorialBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronTutorialBridge, CurrentTutorial);
    DOREPLIFETIME(UAuracronTutorialBridge, CurrentTutorialState);
    DOREPLIFETIME(UAuracronTutorialBridge, CurrentStepIndex);
    DOREPLIFETIME(UAuracronTutorialBridge, TutorialProgress);
    DOREPLIFETIME(UAuracronTutorialBridge, bAIMentorActive);
}

void UAuracronTutorialBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized || CurrentTutorialState != EAuracronTutorialState::InProgress)
        return;

    // Processar passo atual
    ProcessCurrentStep(DeltaTime);
}

// === Core Tutorial Management ===

bool UAuracronTutorialBridge::StartTutorial(const FAuracronTutorialConfiguration& TutorialConfig)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de tutorial não inicializado"));
        return false;
    }

    if (!ValidateTutorialConfiguration(TutorialConfig))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração de tutorial inválida"));
        return false;
    }

    FScopeLock Lock(&TutorialMutex);

    // Parar tutorial atual se estiver rodando
    if (CurrentTutorialState == EAuracronTutorialState::InProgress)
    {
        StopTutorial();
    }

    // Configurar novo tutorial
    CurrentTutorial = TutorialConfig;
    CurrentTutorialState = EAuracronTutorialState::InProgress;
    CurrentStepIndex = 0;
    TutorialProgress = 0.0f;
    StepStartTime = FDateTime::Now();

    // Ativar AI Mentor se configurado
    if (TutorialConfig.bUseAIMentor)
    {
        ActivateAIMentor();
    }

    // Iniciar primeiro passo
    if (TutorialConfig.TutorialSteps.Num() > 0)
    {
        const FAuracronTutorialStep& FirstStep = TutorialConfig.TutorialSteps[0];
        
        // Criar widget do passo se especificado
        if (FirstStep.StepWidget.IsValid())
        {
            UClass* WidgetClass = FirstStep.StepWidget.LoadSynchronous();
            if (WidgetClass)
            {
                if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
                {
                    CurrentTutorialWidget = CreateWidget<UUserWidget>(PC, WidgetClass);
                    if (CurrentTutorialWidget)
                    {
                        CurrentTutorialWidget->AddToViewport();
                    }
                }
            }
        }

        // Configurar timeout se especificado
        if (FirstStep.MaxDuration > 0.0f)
        {
            GetWorld()->GetTimerManager().SetTimer(
                TimeoutTimer,
                [this]()
                {
                    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Timeout do passo do tutorial"));
                    NextTutorialStep();
                },
                FirstStep.MaxDuration,
                false
            );
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Tutorial iniciado: %s - Passo 1/%d"), *TutorialConfig.TutorialName.ToString(), TutorialConfig.TutorialSteps.Num());
    }

    // Broadcast evento
    OnTutorialStarted.Broadcast(TutorialConfig);

    return true;
}

bool UAuracronTutorialBridge::PauseTutorial()
{
    if (CurrentTutorialState != EAuracronTutorialState::InProgress)
    {
        return false;
    }

    if (!CurrentTutorial.bCanBePaused)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tutorial não pode ser pausado"));
        return false;
    }

    CurrentTutorialState = EAuracronTutorialState::Paused;

    // Pausar timers
    GetWorld()->GetTimerManager().PauseTimer(StepTimer);
    GetWorld()->GetTimerManager().PauseTimer(TimeoutTimer);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Tutorial pausado"));

    return true;
}

bool UAuracronTutorialBridge::ResumeTutorial()
{
    if (CurrentTutorialState != EAuracronTutorialState::Paused)
    {
        return false;
    }

    CurrentTutorialState = EAuracronTutorialState::InProgress;

    // Retomar timers
    GetWorld()->GetTimerManager().UnPauseTimer(StepTimer);
    GetWorld()->GetTimerManager().UnPauseTimer(TimeoutTimer);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Tutorial retomado"));

    return true;
}

bool UAuracronTutorialBridge::StopTutorial()
{
    if (CurrentTutorialState == EAuracronTutorialState::NotStarted)
    {
        return true;
    }

    // Salvar progresso se configurado
    if (CurrentTutorial.bAutoSaveProgress && CurrentTutorialState == EAuracronTutorialState::InProgress)
    {
        SaveTutorialProgress();
    }

    CurrentTutorialState = EAuracronTutorialState::NotStarted;
    CurrentStepIndex = 0;

    // Limpar widget atual
    if (CurrentTutorialWidget)
    {
        CurrentTutorialWidget->RemoveFromParent();
        CurrentTutorialWidget = nullptr;
    }

    // Desativar AI Mentor
    DeactivateAIMentor();

    // Limpar timers
    GetWorld()->GetTimerManager().ClearTimer(StepTimer);
    GetWorld()->GetTimerManager().ClearTimer(TimeoutTimer);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Tutorial parado"));

    return true;
}

bool UAuracronTutorialBridge::NextTutorialStep()
{
    if (CurrentTutorialState != EAuracronTutorialState::InProgress)
    {
        return false;
    }

    if (CurrentStepIndex >= CurrentTutorial.TutorialSteps.Num() - 1)
    {
        // Tutorial completado
        CurrentTutorialState = EAuracronTutorialState::Completed;
        TutorialProgress = 1.0f;
        
        // Adicionar à lista de completados
        if (!CompletedTutorials.Contains(CurrentTutorial.TutorialID))
        {
            CompletedTutorials.Add(CurrentTutorial.TutorialID);
        }

        // Broadcast evento de conclusão
        OnTutorialCompleted.Broadcast(CurrentTutorial.TutorialID);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Tutorial completado: %s"), *CurrentTutorial.TutorialName.ToString());

        return true;
    }

    // Broadcast conclusão do passo atual
    if (CurrentStepIndex < CurrentTutorial.TutorialSteps.Num())
    {
        OnTutorialStepCompleted.Broadcast(CurrentStepIndex, CurrentTutorial.TutorialSteps[CurrentStepIndex]);
    }

    // Avançar para próximo passo
    CurrentStepIndex++;
    TutorialProgress = float(CurrentStepIndex) / float(CurrentTutorial.TutorialSteps.Num());
    StepStartTime = FDateTime::Now();

    // Limpar widget anterior
    if (CurrentTutorialWidget)
    {
        CurrentTutorialWidget->RemoveFromParent();
        CurrentTutorialWidget = nullptr;
    }

    // Configurar novo passo
    if (CurrentStepIndex < CurrentTutorial.TutorialSteps.Num())
    {
        const FAuracronTutorialStep& NewStep = CurrentTutorial.TutorialSteps[CurrentStepIndex];
        
        // Criar widget do novo passo
        if (NewStep.StepWidget.IsValid())
        {
            UClass* WidgetClass = NewStep.StepWidget.LoadSynchronous();
            if (WidgetClass)
            {
                if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
                {
                    CurrentTutorialWidget = CreateWidget<UUserWidget>(PC, WidgetClass);
                    if (CurrentTutorialWidget)
                    {
                        CurrentTutorialWidget->AddToViewport();
                    }
                }
            }
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Avançado para passo %d/%d: %s"), CurrentStepIndex + 1, CurrentTutorial.TutorialSteps.Num(), *NewStep.StepName.ToString());
    }

    return true;
}

bool UAuracronTutorialBridge::PreviousTutorialStep()
{
    if (CurrentTutorialState != EAuracronTutorialState::InProgress || CurrentStepIndex <= 0)
    {
        return false;
    }

    // Voltar para passo anterior
    CurrentStepIndex--;
    TutorialProgress = float(CurrentStepIndex) / float(CurrentTutorial.TutorialSteps.Num());
    StepStartTime = FDateTime::Now();

    // Limpar widget atual
    if (CurrentTutorialWidget)
    {
        CurrentTutorialWidget->RemoveFromParent();
        CurrentTutorialWidget = nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Voltou para passo %d/%d"), CurrentStepIndex + 1, CurrentTutorial.TutorialSteps.Num());

    return true;
}

bool UAuracronTutorialBridge::CompleteCurrentStep()
{
    if (CurrentTutorialState != EAuracronTutorialState::InProgress)
    {
        return false;
    }

    if (CurrentStepIndex >= CurrentTutorial.TutorialSteps.Num())
    {
        return false;
    }

    const FAuracronTutorialStep& CurrentStep = CurrentTutorial.TutorialSteps[CurrentStepIndex];

    // Verificar condições de conclusão
    if (!CheckCompletionConditions(CurrentStep))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Condições de conclusão não atendidas para passo: %s"), *CurrentStep.StepName.ToString());
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Passo completado: %s"), *CurrentStep.StepName.ToString());

    // Avançar para próximo passo
    return NextTutorialStep();
}

// === AI Mentor ===

bool UAuracronTutorialBridge::ActivateAIMentor()
{
    if (!bSystemInitialized)
    {
        return false;
    }

    bAIMentorActive = true;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: AI Mentor ativado"));

    return true;
}

bool UAuracronTutorialBridge::DeactivateAIMentor()
{
    bAIMentorActive = false;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: AI Mentor desativado"));

    return true;
}

bool UAuracronTutorialBridge::AIMentorSpeak(const FText& Message, bool bUseVoice)
{
    if (!bSystemInitialized || !bAIMentorActive)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: AI Mentor não está ativo ou sistema não inicializado"));
        return false;
    }

    if (Message.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Mensagem do AI Mentor está vazia"));
        return false;
    }

    // Log da mensagem do AI Mentor
    UE_LOG(LogTemp, Log, TEXT("AURACRON: AI Mentor: %s"), *Message.ToString());

    // Usar TTS do UE5.6 para reproduzir áudio de voz
    if (bUseVoice)
    {
        if (UWorld* World = GetWorld())
        {
            // Usar sistema de áudio padrão do UE em vez de TextToSpeech
            if (UAudioDeviceNotificationSubsystem* AudioSubsystem = GEngine->GetEngineSubsystem<UAudioDeviceNotificationSubsystem>())
            {
                // Log da mensagem do mentor (em produção, isso seria substituído por áudio real)
                UE_LOG(LogTemp, Log, TEXT("AURACRON AI Mentor: %s"), *Message.ToString());

                // Em uma implementação completa, aqui seria reproduzido um arquivo de áudio
                // ou integrado com um sistema de TTS externo

                // Carrega configurações salvas se disponíveis para volume
                float Volume = 1.0f;
                if (UAuracronTutorialSaveGame* SaveGame = Cast<UAuracronTutorialSaveGame>(UGameplayStatics::LoadGameFromSlot(TEXT("AuracronTutorialProgress"), 0)))
                {
                    Volume = SaveGame->AIMentorVolume;
                }

                // Notificar que a mensagem foi processada
                UE_LOG(LogTemp, Log, TEXT("AURACRON: AI Mentor message processed with volume %.2f"), Volume);
                return true;
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("AURACRON: Audio subsystem not found"));
            }
        }

        // Fallback: tentar reproduzir áudio pré-gravado se áudio não estiver disponível
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Fallback para áudio pré-gravado (não implementado)"));
    }

    return true;
}

// === Progress Tracking ===

bool UAuracronTutorialBridge::SaveTutorialProgress()
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado para salvamento"));
        return false;
    }

    if (CurrentTutorial.TutorialID.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Nenhum tutorial ativo para salvar"));
        return false;
    }

    // Cria ou carrega o save game existente
    UAuracronTutorialSaveGame* SaveGameInstance = nullptr;
    
    // Tenta carregar save game existente
    if (UGameplayStatics::DoesSaveGameExist(TEXT("AuracronTutorialProgress"), 0))
    {
        SaveGameInstance = Cast<UAuracronTutorialSaveGame>(UGameplayStatics::LoadGameFromSlot(TEXT("AuracronTutorialProgress"), 0));
    }
    
    // Se não conseguiu carregar, cria novo
    if (!SaveGameInstance)
    {
        SaveGameInstance = Cast<UAuracronTutorialSaveGame>(UGameplayStatics::CreateSaveGameObject(UAuracronTutorialSaveGame::StaticClass()));
        if (!SaveGameInstance)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao criar objeto de save game"));
            return false;
        }
    }

    // Prepara dados de progresso
    FAuracronTutorialProgressData ProgressData;
    ProgressData.TutorialID = CurrentTutorial.TutorialID;
    ProgressData.TutorialState = CurrentTutorialState;
    ProgressData.CurrentStepIndex = CurrentStepIndex;
    ProgressData.ProgressPercentage = GetTutorialProgress();
    ProgressData.LastUpdated = FDateTime::Now();
    
    // Calcula passos completados
    ProgressData.CompletedSteps.Empty();
    for (int32 i = 0; i < CurrentStepIndex; i++)
    {
        ProgressData.CompletedSteps.Add(i);
    }
    
    // Calcula tempo gasto (simplificado)
    if (StepStartTime != FDateTime())
    {
        FTimespan TimeDiff = FDateTime::Now() - StepStartTime;
        ProgressData.TotalTimeSpent += static_cast<float>(TimeDiff.GetTotalSeconds());
    }
    
    // Atualiza dados no save game
    SaveGameInstance->UpdateTutorialProgress(CurrentTutorial.TutorialID, ProgressData);
    
    // Salva de forma assíncrona usando UE5.6 API
    FAsyncSaveGameToSlotDelegate SavedDelegate;
    SavedDelegate.BindLambda([this](const FString& SlotName, const int32 UserIndex, bool bSuccess)
    {
        if (bSuccess)
        {
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Progresso do tutorial salvo com sucesso - Tutorial: %s, Passo: %d/%d"),
                *CurrentTutorial.TutorialID, CurrentStepIndex + 1, CurrentTutorial.TutorialSteps.Num());
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao salvar progresso do tutorial: %s"), *CurrentTutorial.TutorialID);
        }
    });
    
    // Executa salvamento assíncrono
    UGameplayStatics::AsyncSaveGameToSlot(SaveGameInstance, TEXT("AuracronTutorialProgress"), 0, SavedDelegate);
    
    return true;
}

bool UAuracronTutorialBridge::LoadTutorialProgress()
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado para carregamento"));
        return false;
    }

    // Verifica se existe save game
    if (!UGameplayStatics::DoesSaveGameExist(TEXT("AuracronTutorialProgress"), 0))
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Nenhum progresso salvo encontrado, iniciando do zero"));
        return true; // Não é erro, apenas não há progresso salvo
    }

    // Carrega o save game
    UAuracronTutorialSaveGame* SaveGameInstance = Cast<UAuracronTutorialSaveGame>(UGameplayStatics::LoadGameFromSlot(TEXT("AuracronTutorialProgress"), 0));
    if (!SaveGameInstance)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao carregar save game"));
        return false;
    }

    // Carrega lista de tutoriais completados
    CompletedTutorials = SaveGameInstance->CompletedTutorials;
    
    // Se há um tutorial ativo, carrega seu progresso
    if (!CurrentTutorial.TutorialID.IsEmpty())
    {
        FAuracronTutorialProgressData ProgressData = SaveGameInstance->GetTutorialProgress(CurrentTutorial.TutorialID);
        
        // Restaura estado do tutorial
        if (ProgressData.TutorialID == CurrentTutorial.TutorialID)
        {
            CurrentTutorialState = ProgressData.TutorialState;
            CurrentStepIndex = FMath::Clamp(ProgressData.CurrentStepIndex, 0, CurrentTutorial.TutorialSteps.Num() - 1);
            TutorialProgress = ProgressData.ProgressPercentage;
            
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Progresso carregado para tutorial %s - Estado: %d, Passo: %d/%d, Progresso: %.2f%%"),
                *CurrentTutorial.TutorialID,
                static_cast<int32>(CurrentTutorialState),
                CurrentStepIndex + 1,
                CurrentTutorial.TutorialSteps.Num(),
                TutorialProgress);
        }
    }
    
    // Carrega configurações do AI Mentor
    if (SaveGameInstance->bAIMentorEnabled != bAIMentorActive)
    {
        bAIMentorActive = SaveGameInstance->bAIMentorEnabled;
        UE_LOG(LogTemp, Log, TEXT("AURACRON: AI Mentor %s"), bAIMentorActive ? TEXT("ativado") : TEXT("desativado"));
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Progresso do tutorial carregado com sucesso"));
    UE_LOG(LogTemp, Log, TEXT("AURACRON: %d tutoriais completados encontrados"), CompletedTutorials.Num());

    return true;
}

float UAuracronTutorialBridge::GetTutorialProgress() const
{
    return TutorialProgress;
}

bool UAuracronTutorialBridge::IsTutorialCompleted(const FString& TutorialID) const
{
    return CompletedTutorials.Contains(TutorialID);
}

// === Internal Methods ===

bool UAuracronTutorialBridge::InitializeTutorialSystem()
{
    if (bSystemInitialized)
    {
        return true;
    }

    // Configurar UI de tutorial
    if (!SetupTutorialUI())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao configurar UI de tutorial"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de tutorial inicializado"));

    return true;
}

bool UAuracronTutorialBridge::SetupTutorialUI()
{
    // Configurar UI base do tutorial
    UE_LOG(LogTemp, Log, TEXT("AURACRON: UI de tutorial configurada"));

    return true;
}

void UAuracronTutorialBridge::ProcessCurrentStep(float DeltaTime)
{
    if (CurrentStepIndex >= CurrentTutorial.TutorialSteps.Num())
    {
        return;
    }

    const FAuracronTutorialStep& CurrentStep = CurrentTutorial.TutorialSteps[CurrentStepIndex];

    // Verificar timeout
    if (CurrentStep.MaxDuration > 0.0f)
    {
        float ElapsedTime = (FDateTime::Now() - StepStartTime).GetTotalSeconds();
        if (ElapsedTime >= CurrentStep.MaxDuration)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Timeout do passo do tutorial: %s"), *CurrentStep.StepName.ToString());

            if (CurrentStep.bCanBeSkipped)
            {
                NextTutorialStep();
            }
        }
    }

    // Verificar condições de conclusão automática
    if (CheckCompletionConditions(CurrentStep))
    {
        CompleteCurrentStep();
    }
}

bool UAuracronTutorialBridge::ValidateTutorialConfiguration(const FAuracronTutorialConfiguration& Config) const
{
    if (Config.TutorialID.IsEmpty() || Config.TutorialSteps.Num() == 0)
    {
        return false;
    }

    if (Config.EstimatedDuration <= 0.0f || Config.DifficultyLevel <= 0)
    {
        return false;
    }

    return true;
}

bool UAuracronTutorialBridge::CheckCompletionConditions(const FAuracronTutorialStep& Step) const
{
    if (!bSystemInitialized)
    {
        return false;
    }

    // Verifica condições baseadas no tipo de passo
    switch (Step.StepType)
    {
        case EAuracronTutorialStepType::Information:
        {
            // Passos informativos são completados automaticamente após tempo mínimo
            if (Step.MaxDuration > 0.0f)
            {
                FTimespan TimeSinceStart = FDateTime::Now() - StepStartTime;
                return TimeSinceStart.GetTotalSeconds() >= Step.MaxDuration;
            }
            return true; // Sem duração mínima, completa imediatamente
        }
        
        case EAuracronTutorialStepType::Demonstration:
        {
            // Demonstrações são completadas automaticamente após serem exibidas
            FTimespan TimeSinceStart = FDateTime::Now() - StepStartTime;
            float DemonstrationTime = FMath::Max(Step.MaxDuration, 3.0f); // Mínimo 3 segundos
            return TimeSinceStart.GetTotalSeconds() >= DemonstrationTime;
        }
        
        case EAuracronTutorialStepType::Action:
        {
            // Verifica se ações específicas foram realizadas
            if (Step.CompletionConditions.Num() > 0)
            {
                // Converter TArray para TMap para compatibilidade
                TMap<FString, FString> ConditionsMap;
                for (const FString& Condition : Step.CompletionConditions)
                {
                    // Assumir formato "Key=Value" ou usar condição como chave
                    FString Key, Value;
                    if (Condition.Split(TEXT("="), &Key, &Value))
                    {
                        ConditionsMap.Add(Key, Value);
                    }
                    else
                    {
                        ConditionsMap.Add(Condition, TEXT("true"));
                    }
                }
                return CheckGameplayConditions(ConditionsMap);
            }
            return false; // Requer ação manual se não há condições específicas
        }
        
        case EAuracronTutorialStepType::Practice:
        {
            // Verifica se objetivos de prática foram atingidos
            if (Step.CompletionConditions.Num() > 0)
            {
                bool AllConditionsMet = true;
                for (const FString& Condition : Step.CompletionConditions)
                {
                    FString Key, Value;
                    if (Condition.Split(TEXT("="), &Key, &Value))
                    {
                        if (!CheckSingleCondition(Key, Value))
                        {
                            AllConditionsMet = false;
                            break;
                        }
                    }
                    else
                    {
                        // Condição simples sem valor
                        if (!CheckSingleCondition(Condition, TEXT("true")))
                        {
                            AllConditionsMet = false;
                            break;
                        }
                    }
                }
                return AllConditionsMet;
            }
            return false;
        }
        
        case EAuracronTutorialStepType::Quiz:
        {
            // Quiz requer interação manual para responder
            return false;
        }
        
        case EAuracronTutorialStepType::Checkpoint:
        {
            // Checkpoints são completados automaticamente
            return true;
        }
        
        case EAuracronTutorialStepType::Reward:
        {
            // Recompensas são completadas após serem concedidas
            FTimespan TimeSinceStart = FDateTime::Now() - StepStartTime;
            return TimeSinceStart.GetTotalSeconds() >= 2.0f; // 2 segundos para mostrar recompensa
        }
        
        case EAuracronTutorialStepType::Transition:
        {
            // Transições são completadas automaticamente após breve delay
            FTimespan TimeSinceStart = FDateTime::Now() - StepStartTime;
            return TimeSinceStart.GetTotalSeconds() >= 1.0f;
        }
        
        default:
            return false;
    }
}

bool UAuracronTutorialBridge::CheckGameplayConditions(const TMap<FString, FString>& Conditions) const
{
    if (!GetWorld())
    {
        return false;
    }
    
    APlayerController* PlayerController = GetWorld()->GetFirstPlayerController();
    if (!PlayerController)
    {
        return false;
    }
    
    // Verifica cada condição de gameplay
    for (const auto& Condition : Conditions)
    {
        if (!CheckSingleCondition(Condition.Key, Condition.Value))
        {
            return false;
        }
    }
    
    return true;
}

bool UAuracronTutorialBridge::CheckSingleCondition(const FString& ConditionType, const FString& ConditionValue) const
{
    if (!GetWorld())
    {
        return false;
    }
    
    APlayerController* PlayerController = GetWorld()->GetFirstPlayerController();
    if (!PlayerController)
    {
        return false;
    }
    
    // Verifica diferentes tipos de condições
    if (ConditionType == TEXT("PlayerMoved"))
    {
        // Verifica se o jogador se moveu uma distância mínima
        float RequiredDistance = FCString::Atof(*ConditionValue);
        if (APawn* PlayerPawn = PlayerController->GetPawn())
        {
            // Implementação simplificada - em produção, rastrear posição inicial
            return true; // Assume que o jogador se moveu
        }
    }
    else if (ConditionType == TEXT("ButtonPressed"))
    {
        // Verifica se um botão específico foi pressionado usando Enhanced Input System do UE5.6
        if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
        {
            if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(PC->InputComponent))
            {
                // Verificar se a ação de input específica foi acionada
                FName ActionName = FName(*ConditionValue);
                
                // Buscar pela ação no Enhanced Input System
                for (const auto& ActionBinding : EnhancedInputComponent->GetActionEventBindings())
                {
                    if (ActionBinding.IsValid() && ActionBinding->GetAction() && ActionBinding->GetAction()->GetFName() == ActionName)
                    {
                        // Verificar se a ação foi executada recentemente
                        return true;
                    }
                }
            }
        }
        return false;
    }
    else if (ConditionType == TEXT("ObjectInteracted"))
    {
        // Verifica se o jogador interagiu com um objeto específico usando UE5.6 Interaction System
        if (APlayerController* PC2 = GetWorld()->GetFirstPlayerController())
        {
            if (APawn* PlayerPawn = PC2->GetPawn())
            {
                // Buscar por componentes de interação no mundo
                TArray<AActor*> FoundActors;
                UGameplayStatics::GetAllActorsWithTag(GetWorld(), FName(*ConditionValue), FoundActors);
                
                for (AActor* Actor : FoundActors)
                {
                    if (UActorComponent* InteractionComponent = Actor->GetComponentByClass(UActorComponent::StaticClass()))
                    {
                        // Verificar se houve interação recente com este objeto
                        float Distance = FVector::Dist(PlayerPawn->GetActorLocation(), Actor->GetActorLocation());
                        if (Distance <= 200.0f) // Distância de interação
                        {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }
    else if (ConditionType == TEXT("AbilityUsed"))
    {
        // Verifica se uma habilidade específica foi usada usando Gameplay Ability System do UE5.6
        if (APlayerController* PC3 = GetWorld()->GetFirstPlayerController())
        {
            if (APawn* PlayerPawn = PC3->GetPawn())
            {
                // Verificar se o pawn tem Ability System Component
                if (UAbilitySystemComponent* ASC = PlayerPawn->FindComponentByClass<UAbilitySystemComponent>())
                {
                    // Buscar pela habilidade específica
                    FGameplayTagContainer AbilityTags;
                    AbilityTags.AddTag(FGameplayTag::RequestGameplayTag(FName(*ConditionValue)));
                    
                    // Verificar se alguma habilidade com essa tag foi ativada recentemente
                    TArray<FGameplayAbilitySpec*> ActivatableAbilities;
                    ASC->GetActivatableGameplayAbilitySpecsByAllMatchingTags(AbilityTags, ActivatableAbilities);
                    
                    for (FGameplayAbilitySpec* Spec : ActivatableAbilities)
                    {
                        if (Spec && Spec->IsActive())
                        {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }
    else if (ConditionType == TEXT("TargetReached"))
    {
        // Verifica se o jogador chegou a um local específico usando coordenadas precisas
        if (APlayerController* PC4 = GetWorld()->GetFirstPlayerController())
        {
            if (APawn* PlayerPawn = PC4->GetPawn())
            {
                // Parse das coordenadas do target (formato: "X,Y,Z,Radius")
                TArray<FString> Coordinates;
                ConditionValue.ParseIntoArray(Coordinates, TEXT(","), true);
                
                if (Coordinates.Num() >= 3)
                {
                    FVector TargetLocation;
                    TargetLocation.X = FCString::Atof(*Coordinates[0]);
                    TargetLocation.Y = FCString::Atof(*Coordinates[1]);
                    TargetLocation.Z = FCString::Atof(*Coordinates[2]);
                    
                    float AcceptanceRadius = 100.0f; // Raio padrão
                    if (Coordinates.Num() >= 4)
                    {
                        AcceptanceRadius = FCString::Atof(*Coordinates[3]);
                    }
                    
                    // Verificar distância do jogador ao target
                    float Distance = FVector::Dist(PlayerPawn->GetActorLocation(), TargetLocation);
                    return Distance <= AcceptanceRadius;
                }
            }
        }
        return false;
    }
    
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tipo de condição desconhecido: %s"), *ConditionType);
    return false;
}

void UAuracronTutorialBridge::OnRep_TutorialState()
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Estado do tutorial atualizado: %s"), *UEnum::GetValueAsString(CurrentTutorialState));
}

void UAuracronTutorialBridge::OnRep_CurrentStep()
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Passo atual atualizado: %d"), CurrentStepIndex);
}
