/**
 * AuracronGameSystemsIntegrator.h
 * 
 * Sistema integrador final que conecta e coordena todos os sistemas
 * implementados para criar uma experiência de jogo completa e otimizada.
 * 
 * Integra:
 * - Sistema de Conectores Verticais
 * - Sistema de Detecção de Hardware
 * - Sistema de Fases da Partida
 * - Sistema de Memory Management
 * - Todos os bridges existentes
 * 
 * Garante que todos os sistemas trabalhem em harmonia para entregar
 * o jogo completo conforme especificado no design document.
 */

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "GameplayTagContainer.h"
#include "AuracronGameSystemsIntegrator.generated.h"

// Forward declarations
class UAuracronMasterOrchestrator;
// class UAuracronVerticalConnectorSystem; // Temporariamente comentado - módulo não encontrado
class UAuracronHardwareDetectionSystem;
class UAuracronGamePhaseSystem;
class UAuracronMemoryManagementSystem;
class UAuracronDynamicRealmSubsystem;
class UHarmonyEngineSubsystem;
class UAuracronSigilosBridge;
class UAuracronVFXBridge;
class UAuracronPCGBridgeAPI;

/**
 * Estados de integração do sistema
 */
UENUM(BlueprintType)
enum class ESystemIntegrationState : uint8
{
    Uninitialized       UMETA(DisplayName = "Uninitialized"),
    Initializing        UMETA(DisplayName = "Initializing"),
    PartiallyReady      UMETA(DisplayName = "Partially Ready"),
    FullyIntegrated     UMETA(DisplayName = "Fully Integrated"),
    Error               UMETA(DisplayName = "Error"),
    Shutdown            UMETA(DisplayName = "Shutdown")
};

/**
 * Prioridades de inicialização de sistemas
 */
UENUM(BlueprintType)
enum class ESystemInitializationPriority : uint8
{
    Critical            UMETA(DisplayName = "Critical"),
    High                UMETA(DisplayName = "High"),
    Normal              UMETA(DisplayName = "Normal"),
    Low                 UMETA(DisplayName = "Low"),
    Optional            UMETA(DisplayName = "Optional")
};

/**
 * Status de sistema individual
 */
USTRUCT(BlueprintType)
struct AURACRONMASTERORCHESTRATOR_API FSystemStatus
{
    GENERATED_BODY()

    /** Nome do sistema */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System Status")
    FString SystemName;

    /** Sistema inicializado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System Status")
    bool bInitialized;

    /** Sistema funcionando corretamente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System Status")
    bool bHealthy;

    /** Prioridade de inicialização */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System Status")
    ESystemInitializationPriority Priority;

    /** Dependências */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System Status")
    TArray<FString> Dependencies;

    /** Tempo de inicialização */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System Status")
    float InitializationTime;

    /** Última verificação de saúde */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System Status")
    FDateTime LastHealthCheck;

    /** Mensagem de erro (se houver) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System Status")
    FString ErrorMessage;

    FSystemStatus()
    {
        SystemName = TEXT("");
        bInitialized = false;
        bHealthy = true;
        Priority = ESystemInitializationPriority::Normal;
        InitializationTime = 0.0f;
        LastHealthCheck = FDateTime::Now();
        ErrorMessage = TEXT("");
    }
};

/**
 * Configuração de integração
 */
USTRUCT(BlueprintType)
struct AURACRONMASTERORCHESTRATOR_API FSystemIntegrationConfig
{
    GENERATED_BODY()

    /** Habilitar inicialização automática */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration Config")
    bool bEnableAutoInitialization;

    /** Habilitar verificação de saúde automática */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration Config")
    bool bEnableAutoHealthCheck;

    /** Intervalo de verificação de saúde */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration Config")
    float HealthCheckInterval;

    /** Habilitar recuperação automática de erros */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration Config")
    bool bEnableAutoErrorRecovery;

    /** Timeout de inicialização por sistema */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration Config")
    float SystemInitializationTimeout;

    /** Máximo de tentativas de recuperação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration Config")
    int32 MaxRecoveryAttempts;

    /** Habilitar otimização baseada em hardware */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration Config")
    bool bEnableHardwareBasedOptimization;

    /** Habilitar adaptação dinâmica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration Config")
    bool bEnableDynamicAdaptation;

    FSystemIntegrationConfig()
    {
        bEnableAutoInitialization = true;
        bEnableAutoHealthCheck = true;
        HealthCheckInterval = 10.0f;
        bEnableAutoErrorRecovery = true;
        SystemInitializationTimeout = 30.0f;
        MaxRecoveryAttempts = 3;
        bEnableHardwareBasedOptimization = true;
        bEnableDynamicAdaptation = true;
    }
};

/**
 * Métricas de integração
 */
USTRUCT(BlueprintType)
struct AURACRONMASTERORCHESTRATOR_API FIntegrationMetrics
{
    GENERATED_BODY()

    /** Número total de sistemas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration Metrics")
    int32 TotalSystems;

    /** Sistemas inicializados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration Metrics")
    int32 InitializedSystems;

    /** Sistemas saudáveis */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration Metrics")
    int32 HealthySystems;

    /** Tempo total de inicialização */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration Metrics")
    float TotalInitializationTime;

    /** Número de erros de integração */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration Metrics")
    int32 IntegrationErrors;

    /** Número de recuperações bem-sucedidas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration Metrics")
    int32 SuccessfulRecoveries;

    /** Performance geral do sistema */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration Metrics")
    float OverallSystemPerformance;

    /** Timestamp da última atualização */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration Metrics")
    FDateTime LastUpdateTime;

    FIntegrationMetrics()
    {
        TotalSystems = 0;
        InitializedSystems = 0;
        HealthySystems = 0;
        TotalInitializationTime = 0.0f;
        IntegrationErrors = 0;
        SuccessfulRecoveries = 0;
        OverallSystemPerformance = 0.0f;
        LastUpdateTime = FDateTime::Now();
    }
};

/**
 * Integrador de sistemas do jogo
 * 
 * Coordena a inicialização, operação e otimização de todos os sistemas
 * para garantir uma experiência de jogo completa e otimizada
 */
UCLASS(BlueprintType)
class AURACRONMASTERORCHESTRATOR_API UAuracronGameSystemsIntegrator : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    // === Core Integration ===
    
    /** Inicializar integração de sistemas */
    UFUNCTION(BlueprintCallable, Category = "System Integration")
    void InitializeSystemIntegration();

    /** Inicializar todos os sistemas */
    UFUNCTION(BlueprintCallable, Category = "System Integration")
    void InitializeAllSystems();

    /** Finalizar integração de sistemas */
    UFUNCTION(BlueprintCallable, Category = "System Integration")
    void ShutdownSystemIntegration();

    /** Verificar saúde de todos os sistemas */
    UFUNCTION(BlueprintCallable, Category = "System Integration")
    void PerformSystemHealthCheck();

    // === System Management ===
    
    /** Inicializar sistema específico */
    UFUNCTION(BlueprintCallable, Category = "System Integration")
    bool InitializeSystem(const FString& SystemName);

    /** Reinicializar sistema específico */
    UFUNCTION(BlueprintCallable, Category = "System Integration")
    bool RestartSystem(const FString& SystemName);

    /** Obter status de sistema */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "System Integration")
    FSystemStatus GetSystemStatus(const FString& SystemName) const;

    /** Obter status de todos os sistemas */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "System Integration")
    TMap<FString, FSystemStatus> GetAllSystemStatuses() const;

    /** Verificar se sistema está inicializado */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "System Integration")
    bool IsSystemInitialized(const FString& SystemName) const;

    /** Verificar se todos os sistemas estão prontos */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "System Integration")
    bool AreAllSystemsReady() const;

    // === Integration State ===
    
    /** Obter estado de integração atual */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "System Integration")
    ESystemIntegrationState GetIntegrationState() const;

    /** Obter métricas de integração */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "System Integration")
    FIntegrationMetrics GetIntegrationMetrics() const;

    /** Obter progresso de inicialização */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "System Integration")
    float GetInitializationProgress() const;

    // === Configuration ===
    
    /** Configurar integração */
    UFUNCTION(BlueprintCallable, Category = "System Integration")
    void ConfigureIntegration(const FSystemIntegrationConfig& Config);

    /** Obter configuração atual */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "System Integration")
    FSystemIntegrationConfig GetCurrentConfiguration() const;

    // === Optimization ===
    
    /** Otimizar sistemas baseado em hardware */
    UFUNCTION(BlueprintCallable, Category = "System Integration")
    void OptimizeSystemsForHardware();

    /** Aplicar adaptação dinâmica */
    UFUNCTION(BlueprintCallable, Category = "System Integration")
    void ApplyDynamicAdaptation();

    /** Balancear recursos entre sistemas */
    UFUNCTION(BlueprintCallable, Category = "System Integration")
    void BalanceSystemResources();

    // === Error Recovery ===
    
    /** Recuperar sistema com erro */
    UFUNCTION(BlueprintCallable, Category = "System Integration")
    bool RecoverFailedSystem(const FString& SystemName);

    /** Recuperar todos os sistemas com erro */
    UFUNCTION(BlueprintCallable, Category = "System Integration")
    void RecoverAllFailedSystems();

    /** Verificar e corrigir dependências */
    UFUNCTION(BlueprintCallable, Category = "System Integration")
    void ValidateAndFixDependencies();

    // === Events ===
    
    /** Evento quando integração é completada */
    UFUNCTION(BlueprintImplementableEvent, Category = "System Integration")
    void OnIntegrationCompleted();

    /** Evento quando sistema é inicializado */
    UFUNCTION(BlueprintImplementableEvent, Category = "System Integration")
    void OnSystemInitialized(const FString& SystemName, bool bSuccess);

    /** Evento quando erro de sistema é detectado */
    UFUNCTION(BlueprintImplementableEvent, Category = "System Integration")
    void OnSystemError(const FString& SystemName, const FString& ErrorMessage);

    /** Evento quando sistema é recuperado */
    UFUNCTION(BlueprintImplementableEvent, Category = "System Integration")
    void OnSystemRecovered(const FString& SystemName);

    /** Evento quando otimização é aplicada */
    UFUNCTION(BlueprintImplementableEvent, Category = "System Integration")
    void OnOptimizationApplied(float PerformanceImprovement);

protected:
    // === Configuration ===
    
    /** Configuração de integração */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FSystemIntegrationConfig IntegrationConfig;

    /** Status de todos os sistemas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TMap<FString, FSystemStatus> SystemStatuses;

    /** Métricas de integração */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FIntegrationMetrics CurrentMetrics;

    /** Estado atual de integração */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    ESystemIntegrationState CurrentIntegrationState;

private:
    // === Internal Implementation ===
    void SetupSystemDependencies();
    void InitializeSystemsInOrder();
    void ValidateSystemIntegration();
    void StartHealthCheckTimer();
    void ProcessSystemInitialization();
    void UpdateIntegrationMetrics();
    
    // === System Management Implementation ===
    bool InitializeSystemInternal(const FString& SystemName);
    void RegisterSystemStatus(const FString& SystemName, ESystemInitializationPriority Priority);
    void UpdateSystemStatus(const FString& SystemName, bool bInitialized, bool bHealthy, const FString& ErrorMessage = TEXT(""));
    bool CheckSystemDependencies(const FString& SystemName);
    
    // === Health Check Implementation ===
    void PerformIndividualHealthCheck(const FString& SystemName);
    void AnalyzeSystemHealth();
    void DetectSystemIssues();
    
    // === Error Recovery Implementation ===
    bool AttemptSystemRecovery(const FString& SystemName);
    void LogSystemError(const FString& SystemName, const FString& ErrorMessage);
    void NotifySystemRecovery(const FString& SystemName);
    
    // === Optimization Implementation ===
    void ApplyHardwareOptimizations();
    void AdjustSystemPriorities();
    void OptimizeResourceAllocation();
    
    // === Utility Methods ===
    ESystemIntegrationState DetermineIntegrationState();
    float CalculateInitializationProgress();
    void LogIntegrationStatus();
    
    // === Cached System References ===
    UPROPERTY()
    TObjectPtr<UAuracronMasterOrchestrator> CachedMasterOrchestrator;
    
    // UPROPERTY()
    // TObjectPtr<UAuracronVerticalConnectorSystem> CachedVerticalConnectorSystem; // Temporariamente comentado
    
    UPROPERTY()
    TObjectPtr<UAuracronHardwareDetectionSystem> CachedHardwareDetectionSystem;
    
    UPROPERTY()
    TObjectPtr<UAuracronGamePhaseSystem> CachedGamePhaseSystem;
    
    UPROPERTY()
    TObjectPtr<UAuracronMemoryManagementSystem> CachedMemoryManagementSystem;
    
    UPROPERTY()
    TObjectPtr<UAuracronDynamicRealmSubsystem> CachedDynamicRealmSubsystem;
    
    UPROPERTY()
    TObjectPtr<UHarmonyEngineSubsystem> CachedHarmonyEngine;
    
    UPROPERTY()
    TObjectPtr<UAuracronSigilosBridge> CachedSigilosBridge;
    
    UPROPERTY()
    TObjectPtr<UAuracronVFXBridge> CachedVFXBridge;
    
    UPROPERTY()
    TObjectPtr<UAuracronPCGBridgeAPI> CachedPCGBridge;
    
    // === Timers ===
    FTimerHandle HealthCheckTimer;
    FTimerHandle InitializationTimer;
    FTimerHandle OptimizationTimer;
    
    // === State Tracking ===
    bool bIntegrationInitialized;
    float InitializationStartTime;
    int32 RecoveryAttempts;
    TMap<FString, int32> SystemRecoveryAttempts;
};
