#include "AuracronDynamicRealmSubsystem.h"
#include "AuracronDynamicRealmBridge.h"
#include "AuracronSigilosBridge/Public/AuracronSigilosBridge.h"
#include "AuracronRealmManager.h"
#include "AuracronLayerComponent.h"
#include "AuracronPrismalFlow.h"
#include "AuracronPrismalIsland.h"
#include "AuracronDynamicRail.h"
#include "AuracronRealmTransitionComponent.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "GameFramework/Actor.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/PlayerController.h"
#include "TimerManager.h"
#include "PCGComponent.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "NiagaraFunctionLibrary.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Components/StaticMeshComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/AudioComponent.h"
#include "Engine/DirectionalLight.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialInterface.h"
#include "Kismet/GameplayStatics.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "GameplayTags.h"
#include "Engine/Engine.h"
#include "Sound/SoundBase.h"
#include "EngineUtils.h"
#include "MetasoundSource.h"
#include "Engine/StaticMeshActor.h"
#include "Components/PrimitiveComponent.h"
#include "Engine/Engine.h"
#include "HAL/PlatformFilemanager.h"
#include "Engine/World.h"
#include "EngineUtils.h"
#include "AuracronSigilosBridge.h"

UAuracronDynamicRealmSubsystem::UAuracronDynamicRealmSubsystem()
{
    bIsInitialized = false;
    bEnableDebugLogging = true;
    PerformanceUpdateInterval = 1.0f;
    MaxConcurrentTransitions = AuracronDynamicRealmConstants::MAX_CONCURRENT_TRANSITIONS;
    CurrentEvolutionPhase = ERealmEvolutionPhase::Despertar;
    PhaseStartTime = 0.0f;
    TotalGameTime = 0.0f;
    LastPerformanceUpdate = 0.0f;
}

void UAuracronDynamicRealmSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    AURACRON_REALM_LOG(Warning, TEXT("Initializing Auracron Dynamic Realm Subsystem"));
    
    // Initialize layer data
    InitializeLayerData();

    // Initialize advanced evolution system
    InitializeLayerEvolutionSystem();

    // Initialize integration with other systems
    IntegrateWithSigilSystem();

    bIsInitialized = true;
    
    AURACRON_REALM_LOG(Warning, TEXT("Auracron Dynamic Realm Subsystem Initialized Successfully"));
}

void UAuracronDynamicRealmSubsystem::Deinitialize()
{
    AURACRON_REALM_LOG(Warning, TEXT("Deinitializing Auracron Dynamic Realm Subsystem"));
    
    // Clear timers
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(EvolutionUpdateTimer);
        World->GetTimerManager().ClearTimer(PerformanceOptimizationTimer);
        World->GetTimerManager().ClearTimer(PrismalFlowUpdateTimer);
        World->GetTimerManager().ClearTimer(LayerEvolutionTimer);

        // Clear all advanced transition timers
        for (auto& TimerPair : ActiveTransitionTimers)
        {
            World->GetTimerManager().ClearTimer(TimerPair.Value);
        }
    }
    
    // Clear data
    RealmLayers.Empty();
    PrismalIslands.Empty();
    ActiveRails.Empty();
    ActiveTransitions.Empty();

    // Clear advanced evolution system data
    LayerEvolutionData.Empty();
    ActiveAdvancedTransitions.Empty();
    ActiveTransitionTimers.Empty();
    TransitionVFXComponents.Empty();
    TransitionAudioComponents.Empty();
    TransitionEffectHandles.Empty();
    ActorLayerEffects.Empty();
    ActorEvolutionEffects.Empty();
    ActorLayerEntryTimes.Empty();
    EvolutionVFXComponents.Empty();
    GlobalEvolutionBonusHandles.Empty();
    TranscendentAbilityHandles.Empty();
    LayerPerformanceMetrics.Empty();
    
    bIsInitialized = false;
    
    Super::Deinitialize();
    
    AURACRON_REALM_LOG(Warning, TEXT("Auracron Dynamic Realm Subsystem Deinitialized"));
}

bool UAuracronDynamicRealmSubsystem::ShouldCreateSubsystem(UObject* Outer) const
{
    // Only create in game worlds
    if (UWorld* World = Cast<UWorld>(Outer))
    {
        return World->IsGameWorld();
    }
    return false;
}

void UAuracronDynamicRealmSubsystem::OnWorldBeginPlay(UWorld& InWorld)
{
    Super::OnWorldBeginPlay(InWorld);
    
    AURACRON_REALM_LOG(Warning, TEXT("World Begin Play - Initializing Realm Layers"));
    
    // Initialize realm layers
    InitializeRealmLayers();
    
    // Initialize Prismal Flow
    InitializePrismalFlow();
    
    // Initialize Dynamic Rails
    InitializeDynamicRails();
    
    // Start update timers
    InWorld.GetTimerManager().SetTimer(EvolutionUpdateTimer, [this]()
    {
        UpdateRealmEvolution(PerformanceUpdateInterval);
    }, PerformanceUpdateInterval, true);
    
    InWorld.GetTimerManager().SetTimer(PerformanceOptimizationTimer, [this]()
    {
        OptimizePerformance(PerformanceUpdateInterval);
    }, PerformanceUpdateInterval * 2.0f, true);
    
    InWorld.GetTimerManager().SetTimer(PrismalFlowUpdateTimer, [this]()
    {
        UpdatePrismalFlow(PerformanceUpdateInterval);
    }, PerformanceUpdateInterval * 0.5f, true);
    
    AURACRON_REALM_LOG(Warning, TEXT("Realm Layers Initialized and Timers Started"));
}

void UAuracronDynamicRealmSubsystem::Tick(float DeltaTime)
{
    AURACRON_REALM_SCOPE_CYCLE_COUNTER(STAT_AuracronRealmUpdate);
    
    if (!bIsInitialized)
    {
        return;
    }
    
    // Update total game time
    TotalGameTime += DeltaTime;
    
    // Process active transitions
    ProcessActiveTransitions(DeltaTime);
    
    // Update Prismal islands
    UpdatePrismalIslands(DeltaTime);
    
    // Update dynamic rails
    UpdateDynamicRails(DeltaTime);
    
    // Log status periodically
    if (bEnableDebugLogging && FMath::Fmod(TotalGameTime, 30.0f) < DeltaTime)
    {
        LogRealmStatus();
    }
}

bool UAuracronDynamicRealmSubsystem::DoesSupportWorldType(const EWorldType::Type WorldType) const
{
    return WorldType == EWorldType::Game || WorldType == EWorldType::PIE;
}

void UAuracronDynamicRealmSubsystem::InitializeRealmLayers()
{
    AURACRON_REALM_SCOPE_CYCLE_COUNTER(STAT_AuracronRealmGeneration);
    
    AURACRON_REALM_LOG(Log, TEXT("Initializing Realm Layers"));
    
    // Initialize each layer
    InitializeTerrestrialLayer();
    InitializeCelestialLayer();
    InitializeAbyssalLayer();
    
    // Set initial phase
    CurrentEvolutionPhase = ERealmEvolutionPhase::Despertar;
    PhaseStartTime = TotalGameTime;
    
    AURACRON_REALM_LOG(Log, TEXT("Realm Layers Initialized - Phase: Despertar"));
}

void UAuracronDynamicRealmSubsystem::InitializeTerrestrialLayer()
{
    FAuracronRealmLayerData& LayerData = RealmLayers.FindOrAdd(EAuracronRealmLayer::Terrestrial);
    LayerData.LayerType = EAuracronRealmLayer::Terrestrial;
    LayerData.LayerHeight = AuracronDynamicRealmConstants::TERRESTRIAL_LAYER_HEIGHT;
    LayerData.LayerBounds = FBox(FVector(-10000, -10000, -1000), FVector(10000, 10000, 1000));
    LayerData.bIsActive = true;
    LayerData.CurrentPhase = ERealmEvolutionPhase::Despertar;
    LayerData.LastUpdateTime = TotalGameTime;
    LayerData.ActiveActorCount = 0;
    LayerData.MemoryUsageMB = 0.0f;
    
    AURACRON_REALM_LOG(Log, TEXT("Terrestrial Layer (Planície Radiante) Initialized"));
}

void UAuracronDynamicRealmSubsystem::InitializeCelestialLayer()
{
    FAuracronRealmLayerData& LayerData = RealmLayers.FindOrAdd(EAuracronRealmLayer::Celestial);
    LayerData.LayerType = EAuracronRealmLayer::Celestial;
    LayerData.LayerHeight = AuracronDynamicRealmConstants::CELESTIAL_LAYER_HEIGHT;
    LayerData.LayerBounds = FBox(FVector(-12000, -12000, 4000), FVector(12000, 12000, 6000));
    LayerData.bIsActive = false; // Activated during Convergência phase
    LayerData.CurrentPhase = ERealmEvolutionPhase::Despertar;
    LayerData.LastUpdateTime = TotalGameTime;
    LayerData.ActiveActorCount = 0;
    LayerData.MemoryUsageMB = 0.0f;
    
    AURACRON_REALM_LOG(Log, TEXT("Celestial Layer (Firmamento Zephyr) Initialized"));
}

void UAuracronDynamicRealmSubsystem::InitializeAbyssalLayer()
{
    FAuracronRealmLayerData& LayerData = RealmLayers.FindOrAdd(EAuracronRealmLayer::Abyssal);
    LayerData.LayerType = EAuracronRealmLayer::Abyssal;
    LayerData.LayerHeight = AuracronDynamicRealmConstants::ABYSSAL_LAYER_HEIGHT;
    LayerData.LayerBounds = FBox(FVector(-8000, -8000, -4000), FVector(8000, 8000, -2000));
    LayerData.bIsActive = false; // Activated during Intensificação phase
    LayerData.CurrentPhase = ERealmEvolutionPhase::Despertar;
    LayerData.LastUpdateTime = TotalGameTime;
    LayerData.ActiveActorCount = 0;
    LayerData.MemoryUsageMB = 0.0f;
    
    AURACRON_REALM_LOG(Log, TEXT("Abyssal Layer (Abismo Umbrio) Initialized"));
}

void UAuracronDynamicRealmSubsystem::UpdateRealmEvolution(float DeltaTime)
{
    AURACRON_REALM_SCOPE_CYCLE_COUNTER(STAT_AuracronRealmUpdate);
    
    float PhaseElapsedTime = TotalGameTime - PhaseStartTime;
    ERealmEvolutionPhase NewPhase = CurrentEvolutionPhase;
    
    // Check for phase transitions
    switch (CurrentEvolutionPhase)
    {
        case ERealmEvolutionPhase::Despertar:
            if (PhaseElapsedTime >= AuracronDynamicRealmConstants::DESPERTAR_DURATION)
            {
                NewPhase = ERealmEvolutionPhase::Convergencia;
            }
            break;
            
        case ERealmEvolutionPhase::Convergencia:
            if (PhaseElapsedTime >= AuracronDynamicRealmConstants::CONVERGENCIA_DURATION)
            {
                NewPhase = ERealmEvolutionPhase::Intensificacao;
            }
            break;
            
        case ERealmEvolutionPhase::Intensificacao:
            if (PhaseElapsedTime >= AuracronDynamicRealmConstants::INTENSIFICACAO_DURATION)
            {
                NewPhase = ERealmEvolutionPhase::Resolucao;
            }
            break;
            
        case ERealmEvolutionPhase::Resolucao:
            // Final phase - no automatic transition
            break;
    }
    
    // Transition to new phase if needed
    if (NewPhase != CurrentEvolutionPhase)
    {
        TransitionToPhase(NewPhase);
    }
    
    // Update individual layers
    for (auto& LayerPair : RealmLayers)
    {
        UpdateLayerEvolution(LayerPair.Key, DeltaTime);
    }
}

void UAuracronDynamicRealmSubsystem::TransitionToPhase(ERealmEvolutionPhase NewPhase)
{
    ERealmEvolutionPhase OldPhase = CurrentEvolutionPhase;
    CurrentEvolutionPhase = NewPhase;
    PhaseStartTime = TotalGameTime;
    
    AURACRON_REALM_LOG(Warning, TEXT("Realm Evolution Phase Transition: %d -> %d"), (int32)OldPhase, (int32)NewPhase);
    
    // Handle phase-specific logic
    OnPhaseTransition(OldPhase, NewPhase);
    
    // Update all layers to new phase
    for (auto& LayerPair : RealmLayers)
    {
        LayerPair.Value.CurrentPhase = NewPhase;
    }
    
    // Broadcast event
    OnRealmEvolutionPhaseChanged.Broadcast(NewPhase);
}

void UAuracronDynamicRealmSubsystem::OnPhaseTransition(ERealmEvolutionPhase OldPhase, ERealmEvolutionPhase NewPhase)
{
    switch (NewPhase)
    {
        case ERealmEvolutionPhase::Despertar:
            HandleDespertarPhase();
            break;
        case ERealmEvolutionPhase::Convergencia:
            HandleConvergenciaPhase();
            break;
        case ERealmEvolutionPhase::Intensificacao:
            HandleIntensificacaoPhase();
            break;
        case ERealmEvolutionPhase::Resolucao:
            HandleResolucaoPhase();
            break;
    }
}

void UAuracronDynamicRealmSubsystem::HandleDespertarPhase()
{
    AURACRON_REALM_LOG(Warning, TEXT("Entering Despertar Phase - Only Terrestrial Layer Active"));
    
    // Only terrestrial layer is active
    ActivateLayer(EAuracronRealmLayer::Terrestrial);
    DeactivateLayer(EAuracronRealmLayer::Celestial);
    DeactivateLayer(EAuracronRealmLayer::Abyssal);
}

void UAuracronDynamicRealmSubsystem::HandleConvergenciaPhase()
{
    AURACRON_REALM_LOG(Warning, TEXT("Entering Convergência Phase - Celestial Layer Activating"));
    
    // Activate celestial layer
    ActivateLayer(EAuracronRealmLayer::Celestial);
    
    // Terrestrial remains active
    ActivateLayer(EAuracronRealmLayer::Terrestrial);
}

void UAuracronDynamicRealmSubsystem::HandleIntensificacaoPhase()
{
    AURACRON_REALM_LOG(Warning, TEXT("Entering Intensificação Phase - All Layers Active"));
    
    // Activate all layers
    ActivateLayer(EAuracronRealmLayer::Terrestrial);
    ActivateLayer(EAuracronRealmLayer::Celestial);
    ActivateLayer(EAuracronRealmLayer::Abyssal);
}

void UAuracronDynamicRealmSubsystem::HandleResolucaoPhase()
{
    AURACRON_REALM_LOG(Warning, TEXT("Entering Resolução Phase - Maximum Intensity"));
    
    // All layers remain active with maximum intensity
    ActivateLayer(EAuracronRealmLayer::Terrestrial);
    ActivateLayer(EAuracronRealmLayer::Celestial);
    ActivateLayer(EAuracronRealmLayer::Abyssal);
    
    // Increase intensity of all effects
    if (PrismalFlowActor)
    {
        PrismalFlowActor->SetFlowIntensity(2.0f);
    }
}

bool UAuracronDynamicRealmSubsystem::ActivateLayer(EAuracronRealmLayer Layer)
{
    if (Layer == EAuracronRealmLayer::None || Layer == EAuracronRealmLayer::All)
    {
        return false;
    }
    
    FAuracronRealmLayerData* LayerData = RealmLayers.Find(Layer);
    if (!LayerData)
    {
        AURACRON_REALM_LOG(Error, TEXT("Failed to find layer data for layer %d"), (int32)Layer);
        return false;
    }
    
    if (LayerData->bIsActive)
    {
        return true; // Already active
    }
    
    LayerData->bIsActive = true;
    LayerData->LastUpdateTime = TotalGameTime;
    
    // Find and activate realm manager for this layer
    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<AAuracronRealmManager> ActorItr(World); ActorItr; ++ActorItr)
        {
            AAuracronRealmManager* RealmManager = *ActorItr;
            if (RealmManager && RealmManager->ManagedLayer == Layer)
            {
                RealmManager->SetLayerVisibility(true);
                RealmManager->GenerateLayerContent();
                break;
            }
        }
    }
    
    AURACRON_REALM_LOG(Log, TEXT("Layer %d Activated"), (int32)Layer);
    return true;
}

bool UAuracronDynamicRealmSubsystem::DeactivateLayer(EAuracronRealmLayer Layer)
{
    if (Layer == EAuracronRealmLayer::None || Layer == EAuracronRealmLayer::All)
    {
        return false;
    }
    
    FAuracronRealmLayerData* LayerData = RealmLayers.Find(Layer);
    if (!LayerData)
    {
        return false;
    }
    
    if (!LayerData->bIsActive)
    {
        return true; // Already inactive
    }
    
    LayerData->bIsActive = false;
    LayerData->LastUpdateTime = TotalGameTime;
    
    // Find and deactivate realm manager for this layer
    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<AAuracronRealmManager> ActorItr(World); ActorItr; ++ActorItr)
        {
            AAuracronRealmManager* RealmManager = *ActorItr;
            if (RealmManager && RealmManager->ManagedLayer == Layer)
            {
                RealmManager->SetLayerVisibility(false);
                break;
            }
        }
    }
    
    AURACRON_REALM_LOG(Log, TEXT("Layer %d Deactivated"), (int32)Layer);
    return true;
}

bool UAuracronDynamicRealmSubsystem::IsLayerActive(EAuracronRealmLayer Layer) const
{
    const FAuracronRealmLayerData* LayerData = RealmLayers.Find(Layer);
    return LayerData ? LayerData->bIsActive : false;
}

FAuracronRealmLayerData UAuracronDynamicRealmSubsystem::GetLayerData(EAuracronRealmLayer Layer) const
{
    const FAuracronRealmLayerData* LayerData = RealmLayers.Find(Layer);
    return LayerData ? *LayerData : FAuracronRealmLayerData();
}

bool UAuracronDynamicRealmSubsystem::RegisterActorToLayer(AActor* Actor, EAuracronRealmLayer Layer)
{
    if (!Actor || Layer == EAuracronRealmLayer::None)
    {
        return false;
    }
    
    FAuracronRealmLayerData* LayerData = RealmLayers.Find(Layer);
    if (!LayerData)
    {
        return false;
    }
    
    // Add actor to layer if not already present
    if (!LayerData->LayerActors.Contains(Actor))
    {
        LayerData->LayerActors.Add(Actor);
        LayerData->ActiveActorCount = LayerData->LayerActors.Num();
        
        // Add layer component if actor doesn't have one
        if (!Actor->FindComponentByClass<UAuracronLayerComponent>())
        {
            UAuracronLayerComponent* LayerComponent = NewObject<UAuracronLayerComponent>(Actor);
            Actor->AddInstanceComponent(LayerComponent);
            LayerComponent->SetCurrentLayer(Layer);
            LayerComponent->RegisterComponent();
        }
        
        AURACRON_REALM_LOG(VeryVerbose, TEXT("Actor %s registered to layer %d"), *Actor->GetName(), (int32)Layer);
        return true;
    }
    
    return false;
}

bool UAuracronDynamicRealmSubsystem::UnregisterActorFromLayer(AActor* Actor, EAuracronRealmLayer Layer)
{
    if (!Actor || Layer == EAuracronRealmLayer::None)
    {
        return false;
    }
    
    FAuracronRealmLayerData* LayerData = RealmLayers.Find(Layer);
    if (!LayerData)
    {
        return false;
    }
    
    // Remove actor from layer
    int32 RemovedCount = LayerData->LayerActors.RemoveAll([Actor](const TObjectPtr<AActor>& LayerActor)
    {
        return LayerActor == Actor;
    });
    
    if (RemovedCount > 0)
    {
        LayerData->ActiveActorCount = LayerData->LayerActors.Num();
        AURACRON_REALM_LOG(VeryVerbose, TEXT("Actor %s unregistered from layer %d"), *Actor->GetName(), (int32)Layer);
        return true;
    }
    
    return false;
}

EAuracronRealmLayer UAuracronDynamicRealmSubsystem::GetActorLayer(AActor* Actor) const
{
    if (!Actor)
    {
        return EAuracronRealmLayer::None;
    }
    
    // Check if actor has layer component
    if (UAuracronLayerComponent* LayerComponent = Actor->FindComponentByClass<UAuracronLayerComponent>())
    {
        return LayerComponent->GetCurrentLayer();
    }
    
    // Fallback: determine layer from position
    return AuracronDynamicRealmUtils::GetLayerFromPosition(Actor->GetActorLocation());
}

TArray<AActor*> UAuracronDynamicRealmSubsystem::GetActorsInLayer(EAuracronRealmLayer Layer) const
{
    TArray<AActor*> Result;
    
    const FAuracronRealmLayerData* LayerData = RealmLayers.Find(Layer);
    if (LayerData)
    {
        for (const TObjectPtr<AActor>& Actor : LayerData->LayerActors)
        {
            if (IsValid(Actor))
            {
                Result.Add(Actor.Get());
            }
        }
    }
    
    return Result;
}

void UAuracronDynamicRealmSubsystem::InitializeLayerData()
{
    AURACRON_REALM_LOG(Log, TEXT("Initializing Layer Data Structures"));
    
    // Clear existing data
    RealmLayers.Empty();
    LayerPerformanceMetrics.Empty();
    
    // Initialize performance metrics
    LayerPerformanceMetrics.Add(EAuracronRealmLayer::Terrestrial, 0.0f);
    LayerPerformanceMetrics.Add(EAuracronRealmLayer::Celestial, 0.0f);
    LayerPerformanceMetrics.Add(EAuracronRealmLayer::Abyssal, 0.0f);
    
    AURACRON_REALM_LOG(Log, TEXT("Layer Data Structures Initialized"));
}

void UAuracronDynamicRealmSubsystem::UpdateLayerEvolution(EAuracronRealmLayer Layer, float DeltaTime)
{
    FAuracronRealmLayerData* LayerData = RealmLayers.Find(Layer);
    if (!LayerData || !LayerData->bIsActive)
    {
        return;
    }
    
    // Update layer-specific evolution
    LayerData->LastUpdateTime = TotalGameTime;
    
    // Update performance metrics
    float* PerformanceMetric = LayerPerformanceMetrics.Find(Layer);
    if (PerformanceMetric)
    {
        *PerformanceMetric = DeltaTime * 1000.0f; // Convert to milliseconds
    }
}

void UAuracronDynamicRealmSubsystem::LogRealmStatus() const
{
    AURACRON_REALM_LOG(Log, TEXT("=== REALM STATUS UPDATE ==="));
    AURACRON_REALM_LOG(Log, TEXT("Current Phase: %d, Game Time: %.2f"), (int32)CurrentEvolutionPhase, TotalGameTime);
    AURACRON_REALM_LOG(Log, TEXT("Active Transitions: %d"), ActiveTransitions.Num());
    AURACRON_REALM_LOG(Log, TEXT("Active Rails: %d"), ActiveRails.Num());
    AURACRON_REALM_LOG(Log, TEXT("Prismal Islands: %d"), PrismalIslands.Num());
    
    for (const auto& LayerPair : RealmLayers)
    {
        const FAuracronRealmLayerData& LayerData = LayerPair.Value;
        AURACRON_REALM_LOG(Log, TEXT("Layer %d: Active=%s, Actors=%d, Memory=%.2fMB"), 
            (int32)LayerPair.Key, LayerData.bIsActive ? TEXT("Yes") : TEXT("No"), 
            LayerData.ActiveActorCount, LayerData.MemoryUsageMB);
    }
    AURACRON_REALM_LOG(Log, TEXT("=== END REALM STATUS ==="));
}

bool UAuracronDynamicRealmSubsystem::RequestLayerTransition(AActor* Actor, EAuracronRealmLayer TargetLayer, ERealmTransitionType TransitionType)
{
    if (!Actor || TargetLayer == EAuracronRealmLayer::None)
    {
        return false;
    }

    // Check if actor is already in transition
    if (IsActorInTransition(Actor))
    {
        AURACRON_REALM_LOG(Warning, TEXT("Actor %s is already in transition"), *Actor->GetName());
        return false;
    }

    // Check concurrent transition limit
    if (ActiveTransitions.Num() >= MaxConcurrentTransitions)
    {
        AURACRON_REALM_LOG(Warning, TEXT("Maximum concurrent transitions reached (%d)"), MaxConcurrentTransitions);
        return false;
    }

    // Get or create transition component
    UAuracronRealmTransitionComponent* TransitionComponent = Actor->FindComponentByClass<UAuracronRealmTransitionComponent>();
    if (!TransitionComponent)
    {
        TransitionComponent = NewObject<UAuracronRealmTransitionComponent>(Actor);
        Actor->AddInstanceComponent(TransitionComponent);
        TransitionComponent->RegisterComponent();
    }

    // Start transition
    bool bTransitionStarted = TransitionComponent->StartTransition(TargetLayer, TransitionType);
    if (bTransitionStarted)
    {
        ActiveTransitions.Add(Actor, TransitionComponent);

        // Get current layer
        EAuracronRealmLayer CurrentLayer = GetActorLayer(Actor);

        // Broadcast transition started event
        OnRealmTransitionStarted.Broadcast(Actor, CurrentLayer, TargetLayer);

        AURACRON_REALM_LOG(Log, TEXT("Transition started for actor %s: %d -> %d"),
            *Actor->GetName(), (int32)CurrentLayer, (int32)TargetLayer);
    }

    return bTransitionStarted;
}

bool UAuracronDynamicRealmSubsystem::IsActorInTransition(AActor* Actor) const
{
    return ActiveTransitions.Contains(Actor);
}

float UAuracronDynamicRealmSubsystem::GetTransitionProgress(AActor* Actor) const
{
    if (const TObjectPtr<UAuracronRealmTransitionComponent>* TransitionComponent = ActiveTransitions.Find(Actor))
    {
        if (IsValid(*TransitionComponent))
        {
            return (*TransitionComponent)->GetTransitionProgress();
        }
    }
    return 0.0f;
}

void UAuracronDynamicRealmSubsystem::ProcessActiveTransitions(float DeltaTime)
{
    AURACRON_REALM_SCOPE_CYCLE_COUNTER(STAT_AuracronRealmTransition);

    TArray<TObjectPtr<AActor>> CompletedTransitions;

    // Update all active transitions
    for (auto& TransitionPair : ActiveTransitions)
    {
        TObjectPtr<AActor> Actor = TransitionPair.Key;
        TObjectPtr<UAuracronRealmTransitionComponent> TransitionComponent = TransitionPair.Value;

        if (!IsValid(Actor) || !IsValid(TransitionComponent))
        {
            CompletedTransitions.Add(Actor);
            continue;
        }

        // Check if transition is complete
        if (!TransitionComponent->IsTransitioning())
        {
            CompletedTransitions.Add(Actor);

            // Update actor's layer registration
            EAuracronRealmLayer NewLayer = TransitionComponent->GetTransitionTarget();
            EAuracronRealmLayer OldLayer = GetActorLayer(Actor.Get());

            // Unregister from old layer
            if (OldLayer != EAuracronRealmLayer::None)
            {
                UnregisterActorFromLayer(Actor.Get(), OldLayer);
            }

            // Register to new layer
            RegisterActorToLayer(Actor.Get(), NewLayer);

            // Broadcast transition completed event
            OnRealmTransitionCompleted.Broadcast(Actor.Get(), OldLayer, NewLayer);

            AURACRON_REALM_LOG(Log, TEXT("Transition completed for actor %s: %d -> %d"),
                *Actor->GetName(), (int32)OldLayer, (int32)NewLayer);
        }
    }

    // Remove completed transitions
    for (const TObjectPtr<AActor>& Actor : CompletedTransitions)
    {
        ActiveTransitions.Remove(Actor);
    }
}

void UAuracronDynamicRealmSubsystem::InitializePrismalFlow()
{
    AURACRON_REALM_LOG(Log, TEXT("Initializing Prismal Flow System"));

    if (UWorld* World = GetWorld())
    {
        // Find existing Prismal Flow actor or spawn new one
        for (TActorIterator<AAuracronPrismalFlow> ActorItr(World); ActorItr; ++ActorItr)
        {
            PrismalFlowActor = *ActorItr;
            break;
        }

        if (!PrismalFlowActor)
        {
            // Spawn new Prismal Flow actor
            FActorSpawnParameters SpawnParams;
            SpawnParams.Name = FName("AuracronPrismalFlow_Main");
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;

            PrismalFlowActor = World->SpawnActor<AAuracronPrismalFlow>(AAuracronPrismalFlow::StaticClass(), FVector::ZeroVector, FRotator::ZeroRotator, SpawnParams);
        }

        if (PrismalFlowActor)
        {
            PrismalFlowActor->InitializeFlow();
            PrismalFlowActor->SpawnPrismalIslands();

            AURACRON_REALM_LOG(Log, TEXT("Prismal Flow Initialized Successfully"));
        }
        else
        {
            AURACRON_REALM_LOG(Error, TEXT("Failed to initialize Prismal Flow"));
        }
    }
}

void UAuracronDynamicRealmSubsystem::UpdatePrismalFlow(float DeltaTime)
{
    if (PrismalFlowActor)
    {
        PrismalFlowActor->UpdateFlowDynamics(DeltaTime);
        PrismalFlowActor->UpdateIslandStates(DeltaTime);
    }
}

void UAuracronDynamicRealmSubsystem::UpdatePrismalIslands(float DeltaTime)
{
    // Update island data cache
    PrismalIslands.Empty();

    if (PrismalFlowActor)
    {
        // Get islands by type and update cache
        for (int32 IslandTypeInt = 0; IslandTypeInt < 4; IslandTypeInt++)
        {
            EPrismalIslandType IslandType = static_cast<EPrismalIslandType>(IslandTypeInt);
            TArray<AAuracronPrismalIsland*> TypedIslands = PrismalFlowActor->GetIslandsByType(IslandType);

            for (AAuracronPrismalIsland* Island : TypedIslands)
            {
                if (Island)
                {
                    FPrismalIslandData IslandData;
                    IslandData.IslandType = IslandType;
                    IslandData.Location = Island->GetActorLocation();
                    IslandData.bIsActive = Island->IsIslandActive();
                    IslandData.LastActivationTime = TotalGameTime;

                    PrismalIslands.Add(IslandData);
                }
            }
        }
    }
}

void UAuracronDynamicRealmSubsystem::InitializeDynamicRails()
{
    AURACRON_REALM_LOG(Log, TEXT("Initializing Dynamic Rails System"));

    if (UWorld* World = GetWorld())
    {
        // Find existing rails
        ActiveRails.Empty();
        for (TActorIterator<AAuracronDynamicRail> ActorItr(World); ActorItr; ++ActorItr)
        {
            AAuracronDynamicRail* Rail = *ActorItr;
            if (Rail)
            {
                ActiveRails.Add(Rail);
                Rail->ActivateRail();
            }
        }

        AURACRON_REALM_LOG(Log, TEXT("Dynamic Rails Initialized: %d rails found"), ActiveRails.Num());
    }
}

void UAuracronDynamicRealmSubsystem::UpdateDynamicRails(float DeltaTime)
{
    // Update all active rails
    for (TObjectPtr<AAuracronDynamicRail> Rail : ActiveRails)
    {
        if (IsValid(Rail))
        {
            // Rails update themselves via Tick
            // This function can be used for coordinated updates
        }
    }
}

void UAuracronDynamicRealmSubsystem::OptimizePerformance(float DeltaTime)
{
    LastPerformanceUpdate = TotalGameTime;

    // Optimize each active layer
    for (const auto& LayerPair : RealmLayers)
    {
        if (LayerPair.Value.bIsActive)
        {
            OptimizeLayerPerformance(LayerPair.Key);
        }
    }
}

void UAuracronDynamicRealmSubsystem::OptimizeLayerPerformance(EAuracronRealmLayer Layer)
{
    FAuracronRealmLayerData* LayerData = RealmLayers.Find(Layer);
    if (!LayerData)
    {
        return;
    }

    // Update memory usage
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    LayerData->MemoryUsageMB = MemStats.UsedPhysical / (1024.0f * 1024.0f * 3.0f); // Approximate per layer

    // Optimize based on performance
    float PerformanceMetric = GetLayerPerformanceMetric(Layer);
    if (PerformanceMetric > 16.67f) // Above 60 FPS threshold
    {
        // Reduce LOD for this layer
        SetLayerLOD(Layer, FMath::Min(3, GetLayerLODLevel(Layer) + 1));
    }
    else if (PerformanceMetric < 8.33f) // Below 120 FPS threshold
    {
        // Increase LOD for this layer
        SetLayerLOD(Layer, FMath::Max(0, GetLayerLODLevel(Layer) - 1));
    }
}

float UAuracronDynamicRealmSubsystem::GetLayerPerformanceMetric(EAuracronRealmLayer Layer) const
{
    const float* PerformanceMetric = LayerPerformanceMetrics.Find(Layer);
    return PerformanceMetric ? *PerformanceMetric : 0.0f;
}

void UAuracronDynamicRealmSubsystem::SetLayerLOD(EAuracronRealmLayer Layer, int32 LODLevel)
{
    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<AAuracronRealmManager> ActorItr(World); ActorItr; ++ActorItr)
        {
            AAuracronRealmManager* RealmManager = *ActorItr;
            if (RealmManager && RealmManager->ManagedLayer == Layer)
            {
                RealmManager->SetLayerLODLevel(LODLevel);
                break;
            }
        }
    }
}

int32 UAuracronDynamicRealmSubsystem::GetLayerLODLevel(EAuracronRealmLayer Layer) const
{
    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<AAuracronRealmManager> ActorItr(World); ActorItr; ++ActorItr)
        {
            AAuracronRealmManager* RealmManager = *ActorItr;
            if (RealmManager && RealmManager->ManagedLayer == Layer)
            {
                return RealmManager->GetCurrentLODLevel();
            }
        }
    }
    return 0;
}

// === Debug and Development Methods Implementation ===

void UAuracronDynamicRealmSubsystem::DebugShowLayerInfo()
{
    if (!GetWorld())
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("=== AURACRON DYNAMIC REALM SYSTEM DEBUG INFO ==="));

    // Show current realm evolution phase
    UE_LOG(LogTemp, Log, TEXT("Current Evolution Phase: %s"), *UEnum::GetValueAsString(CurrentEvolutionPhase));
    float GlobalEvolutionProgress = CalculateGlobalEvolutionProgress();
    UE_LOG(LogTemp, Log, TEXT("Evolution Progress: %.1f%%"), GlobalEvolutionProgress * 100.0f);

    // Show layer states using UE 5.6 enhanced logging
    for (int32 LayerInt = 0; LayerInt < static_cast<int32>(EAuracronRealmLayer::MAX); LayerInt++)
    {
        EAuracronRealmLayer Layer = static_cast<EAuracronRealmLayer>(LayerInt);
        FAuracronRealmLayerData LayerData = GetLayerData(Layer);

        UE_LOG(LogTemp, Log, TEXT("Layer %s:"), *UEnum::GetValueAsString(Layer));
        UE_LOG(LogTemp, Log, TEXT("  Active: %s"), LayerData.bIsActive ? TEXT("Yes") : TEXT("No"));
        UE_LOG(LogTemp, Log, TEXT("  Actors: %d"), GetActorsInLayer(Layer).Num());
        UE_LOG(LogTemp, Log, TEXT("  Performance: %.2f"), GetLayerPerformanceMetric(Layer));
        UE_LOG(LogTemp, Log, TEXT("  LOD Level: %d"), GetLayerLODLevel(Layer));
        UE_LOG(LogTemp, Log, TEXT("  Stability: %.1f%%"), LayerData.StabilityLevel * 100.0f);
        UE_LOG(LogTemp, Log, TEXT("  Energy Level: %.1f%%"), LayerData.EnergyLevel * 100.0f);
    }

    // Show Prismal Flow information
    UE_LOG(LogTemp, Log, TEXT("Prismal Flow System:"));
    UE_LOG(LogTemp, Log, TEXT("  Active Islands: %d"), PrismalIslands.Num());
    UE_LOG(LogTemp, Log, TEXT("  Flow Actor: %s"), PrismalFlowActor ? TEXT("Valid") : TEXT("Invalid"));

    // Show active rails
    TArray<AAuracronDynamicRail*> CurrentActiveRails = GetActiveRails();
    UE_LOG(LogTemp, Log, TEXT("Dynamic Rails:"));
    UE_LOG(LogTemp, Log, TEXT("  Active Rails: %d"), CurrentActiveRails.Num());

    for (AAuracronDynamicRail* Rail : CurrentActiveRails)
    {
        if (Rail)
        {
            UE_LOG(LogTemp, Log, TEXT("    Rail Type: %s, Length: %.1f"),
                *UEnum::GetValueAsString(Rail->GetRailType()), Rail->GetRailLength());
        }
    }

    // Show transition information
    UE_LOG(LogTemp, Log, TEXT("Active Transitions: %d"), ActiveTransitions.Num());
    for (const auto& TransitionPair : ActiveTransitions)
    {
        if (TransitionPair.Value && IsValid(TransitionPair.Key))
        {
            UE_LOG(LogTemp, Log, TEXT("  Actor: %s, Component: %s"),
                *TransitionPair.Key->GetName(),
                TransitionPair.Value ? TEXT("Valid") : TEXT("Invalid"));
        }
    }

    UE_LOG(LogTemp, Log, TEXT("================================================"));
}

void UAuracronDynamicRealmSubsystem::DebugToggleLayerVisibility(EAuracronRealmLayer Layer)
{
    if (!GetWorld())
    {
        return;
    }

    // Toggle layer visibility using UE 5.6 visibility system
    bool bCurrentlyVisible = true;

    // Find realm manager for the layer
    for (TActorIterator<AAuracronRealmManager> ActorItr(GetWorld()); ActorItr; ++ActorItr)
    {
        AAuracronRealmManager* RealmManager = *ActorItr;
        if (RealmManager && RealmManager->ManagedLayer == Layer)
        {
            // Toggle visibility of all components in the layer
            TArray<UActorComponent*> Components = RealmManager->GetComponents().Array();

            for (UActorComponent* Component : Components)
            {
                if (UPrimitiveComponent* PrimComp = Cast<UPrimitiveComponent>(Component))
                {
                    bCurrentlyVisible = PrimComp->IsVisible();
                    PrimComp->SetVisibility(!bCurrentlyVisible);
                }
                else if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
                {
                    if (bCurrentlyVisible)
                    {
                        NiagaraComp->Deactivate();
                    }
                    else
                    {
                        NiagaraComp->Activate();
                    }
                }
            }

            // Toggle visibility of all actors in the layer
            TArray<AActor*> LayerActors = GetActorsInLayer(Layer);
            for (AActor* Actor : LayerActors)
            {
                if (Actor && Actor != RealmManager)
                {
                    Actor->SetActorHiddenInGame(bCurrentlyVisible);
                }
            }

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Layer %s visibility toggled to %s"),
                *UEnum::GetValueAsString(Layer),
                bCurrentlyVisible ? TEXT("Hidden") : TEXT("Visible"));

            break;
        }
    }
}

void UAuracronDynamicRealmSubsystem::DebugGenerateLayerContent(EAuracronRealmLayer Layer)
{
    if (!GetWorld())
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Generating debug content for layer: %s"), *UEnum::GetValueAsString(Layer));

    // Generate layer-specific content using UE 5.6 procedural generation
    switch (Layer)
    {
        case EAuracronRealmLayer::Terrestrial:
            GenerateTerrestrialContent();
            break;
        case EAuracronRealmLayer::Celestial:
            GenerateCelestialContent();
            break;
        case EAuracronRealmLayer::Abyssal:
            GenerateAbyssalContent();
            break;
        default:
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Unknown layer type for content generation"));
            return;
    }

    // Update layer data after content generation
    FAuracronRealmLayerData& LayerData = RealmLayers.FindOrAdd(Layer);
    LayerData.bIsActive = true;
    LayerData.StabilityLevel = 1.0f;
    LayerData.EnergyLevel = 1.0f;
    LayerData.LastUpdateTime = GetWorld()->GetTimeSeconds();

    // Broadcast layer change event
    OnRealmLayerChanged.Broadcast(EAuracronRealmLayer::None, Layer);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Layer content generation completed for %s"), *UEnum::GetValueAsString(Layer));
}

void UAuracronDynamicRealmSubsystem::GenerateTerrestrialContent()
{
    if (!GetWorld())
    {
        return;
    }

    // Generate Terrestrial (Planície Radiante) content using UE 5.6 world composition
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Generating Terrestrial layer content..."));

    // Spawn terrain features using modern spawning system
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bNoFail = true;

    // Generate radiant crystals
    for (int32 i = 0; i < 10; i++)
    {
        FVector SpawnLocation = FVector(
            FMath::RandRange(-5000.0f, 5000.0f),
            FMath::RandRange(-5000.0f, 5000.0f),
            FMath::RandRange(0.0f, 1000.0f)
        );

        // Spawn radiant crystal using UE 5.6 actor spawning
        static const FSoftClassPath RadiantCrystalPath(TEXT("/Game/Environment/Terrestrial/BP_RadiantCrystal.BP_RadiantCrystal_C"));
        if (UClass* RadiantCrystalClass = RadiantCrystalPath.TryLoadClass<AActor>())
        {
            if (AActor* Crystal = GetWorld()->SpawnActor<AActor>(RadiantCrystalClass, SpawnLocation, FRotator::ZeroRotator, SpawnParams))
            {
                RegisterActorToLayer(Crystal, EAuracronRealmLayer::Terrestrial);

                // Apply radiant crystal properties using UE 5.6 component system
                if (UStaticMeshComponent* MeshComp = Crystal->FindComponentByClass<UStaticMeshComponent>())
                {
                    // Set dynamic material with glow effect
                    if (UMaterialInterface* RadiantMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Game/Materials/Environment/M_RadiantCrystal")))
                    {
                        UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(RadiantMaterial, Crystal);
                        DynamicMaterial->SetScalarParameterValue(TEXT("GlowIntensity"), FMath::RandRange(0.8f, 1.5f));
                        DynamicMaterial->SetVectorParameterValue(TEXT("GlowColor"), FLinearColor(1.0f, 0.9f, 0.6f, 1.0f));
                        MeshComp->SetMaterial(0, DynamicMaterial);
                    }
                }

                // Add particle effects using Niagara
                static const FSoftObjectPath RadiantVFXPath(TEXT("/Game/VFX/Environment/NS_RadiantCrystalAura.NS_RadiantCrystalAura"));
                if (UNiagaraSystem* RadiantVFX = Cast<UNiagaraSystem>(RadiantVFXPath.TryLoad()))
                {
                    UNiagaraComponent* VFXComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                        RadiantVFX,
                        Crystal->GetRootComponent(),
                        NAME_None,
                        FVector::ZeroVector,
                        FRotator::ZeroRotator,
                        EAttachLocation::KeepRelativeOffset,
                        false
                    );

                    if (VFXComponent)
                    {
                        VFXComponent->SetVariableFloat(FName("AuraRadius"), FMath::RandRange(200.0f, 400.0f));
                        VFXComponent->SetVariableLinearColor(FName("AuraColor"), FLinearColor(1.0f, 0.9f, 0.6f, 0.3f));
                    }
                }
            }
        }
    }

    // Generate floating islands
    for (int32 i = 0; i < 5; i++)
    {
        FVector IslandLocation = FVector(
            FMath::RandRange(-8000.0f, 8000.0f),
            FMath::RandRange(-8000.0f, 8000.0f),
            FMath::RandRange(500.0f, 1500.0f)
        );

        static const FSoftClassPath FloatingIslandPath(TEXT("/Game/Environment/Terrestrial/BP_FloatingIsland.BP_FloatingIsland_C"));
        if (UClass* IslandClass = FloatingIslandPath.TryLoadClass<AActor>())
        {
            if (AActor* Island = GetWorld()->SpawnActor<AActor>(IslandClass, IslandLocation, FRotator::ZeroRotator, SpawnParams))
            {
                RegisterActorToLayer(Island, EAuracronRealmLayer::Terrestrial);

                // Add floating motion using UE 5.6 timeline system
                if (USceneComponent* RootComp = Island->GetRootComponent())
                {
                    // Create floating motion using timeline
                    FVector OriginalLocation = Island->GetActorLocation();
                    float FloatAmplitude = FMath::RandRange(50.0f, 150.0f);
                    float FloatSpeed = FMath::RandRange(0.5f, 1.5f);

                    // Use timer for continuous floating motion
                    FTimerHandle FloatingTimer;
                    GetWorld()->GetTimerManager().SetTimer(
                        FloatingTimer,
                        [Island, OriginalLocation, FloatAmplitude, FloatSpeed]()
                        {
                            if (IsValid(Island))
                            {
                                float Time = Island->GetWorld()->GetTimeSeconds();
                                float ZOffset = FMath::Sin(Time * FloatSpeed) * FloatAmplitude;
                                FVector NewLocation = OriginalLocation + FVector(0.0f, 0.0f, ZOffset);
                                Island->SetActorLocation(NewLocation);
                            }
                        },
                        0.016f, // ~60 FPS update
                        true    // Looping
                    );
                }
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Terrestrial layer content generated"));
}

void UAuracronDynamicRealmSubsystem::GenerateCelestialContent()
{
    if (!GetWorld())
    {
        return;
    }

    // Generate Celestial (Firmamento Zephyr) content using UE 5.6 advanced spawning
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Generating Celestial layer content..."));

    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bNoFail = true;

    // Generate wind currents and air platforms
    for (int32 i = 0; i < 8; i++)
    {
        FVector PlatformLocation = FVector(
            FMath::RandRange(-6000.0f, 6000.0f),
            FMath::RandRange(-6000.0f, 6000.0f),
            FMath::RandRange(2000.0f, 4000.0f) // Higher altitude for celestial
        );

        // Spawn celestial platform
        static const FSoftClassPath CelestialPlatformPath(TEXT("/Game/Environment/Celestial/BP_CelestialPlatform.BP_CelestialPlatform_C"));
        if (UClass* PlatformClass = CelestialPlatformPath.TryLoadClass<AActor>())
        {
            if (AActor* Platform = GetWorld()->SpawnActor<AActor>(PlatformClass, PlatformLocation, FRotator::ZeroRotator, SpawnParams))
            {
                RegisterActorToLayer(Platform, EAuracronRealmLayer::Celestial);

                // Add wind effects using Niagara
                static const FSoftObjectPath WindVFXPath(TEXT("/Game/VFX/Environment/NS_CelestialWinds.NS_CelestialWinds"));
                if (UNiagaraSystem* WindVFX = Cast<UNiagaraSystem>(WindVFXPath.TryLoad()))
                {
                    UNiagaraComponent* WindComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                        WindVFX,
                        Platform->GetRootComponent(),
                        NAME_None,
                        FVector(0.0f, 0.0f, 100.0f),
                        FRotator::ZeroRotator,
                        EAttachLocation::KeepRelativeOffset,
                        false
                    );

                    if (WindComponent)
                    {
                        WindComponent->SetVariableFloat(FName("WindStrength"), FMath::RandRange(0.5f, 1.5f));
                        WindComponent->SetVariableVec3(FName("WindDirection"), FVector(FMath::RandRange(-1.0f, 1.0f), FMath::RandRange(-1.0f, 1.0f), 0.2f).GetSafeNormal());
                        WindComponent->SetVariableLinearColor(FName("WindColor"), FLinearColor(0.6f, 0.8f, 1.0f, 0.4f));
                    }
                }

                // Add celestial lighting using UE 5.6 lighting system
                if (UPointLightComponent* LightComp = NewObject<UPointLightComponent>(Platform))
                {
                    LightComp->SetupAttachment(Platform->GetRootComponent());
                    LightComp->SetRelativeLocation(FVector(0.0f, 0.0f, 200.0f));
                    LightComp->SetLightColor(FLinearColor(0.8f, 0.9f, 1.0f));
                    LightComp->SetIntensity(2000.0f);
                    LightComp->SetAttenuationRadius(1000.0f);
                    LightComp->SetCastShadows(true);
                    LightComp->RegisterComponent();
                }
            }
        }
    }

    // Generate celestial orbs
    for (int32 i = 0; i < 15; i++)
    {
        FVector OrbLocation = FVector(
            FMath::RandRange(-10000.0f, 10000.0f),
            FMath::RandRange(-10000.0f, 10000.0f),
            FMath::RandRange(1500.0f, 3500.0f)
        );

        static const FSoftClassPath CelestialOrbPath(TEXT("/Game/Environment/Celestial/BP_CelestialOrb.BP_CelestialOrb_C"));
        if (UClass* OrbClass = CelestialOrbPath.TryLoadClass<AActor>())
        {
            if (AActor* Orb = GetWorld()->SpawnActor<AActor>(OrbClass, OrbLocation, FRotator::ZeroRotator, SpawnParams))
            {
                RegisterActorToLayer(Orb, EAuracronRealmLayer::Celestial);

                // Add orbital motion using UE 5.6 movement system
                if (USceneComponent* RootComp = Orb->GetRootComponent())
                {
                    FVector OrbitCenter = OrbLocation;
                    float OrbitRadius = FMath::RandRange(300.0f, 800.0f);
                    float OrbitSpeed = FMath::RandRange(0.3f, 1.0f);

                    // Create orbital motion timer
                    FTimerHandle OrbitTimer;
                    GetWorld()->GetTimerManager().SetTimer(
                        OrbitTimer,
                        [Orb, OrbitCenter, OrbitRadius, OrbitSpeed]()
                        {
                            if (IsValid(Orb))
                            {
                                float Time = Orb->GetWorld()->GetTimeSeconds();
                                float X = OrbitCenter.X + FMath::Cos(Time * OrbitSpeed) * OrbitRadius;
                                float Y = OrbitCenter.Y + FMath::Sin(Time * OrbitSpeed) * OrbitRadius;
                                float Z = OrbitCenter.Z + FMath::Sin(Time * OrbitSpeed * 0.5f) * 100.0f; // Vertical oscillation
                                Orb->SetActorLocation(FVector(X, Y, Z));
                            }
                        },
                        0.016f, // ~60 FPS
                        true    // Looping
                    );
                }

                // Add celestial aura effect
                static const FSoftObjectPath CelestialAuraPath(TEXT("/Game/VFX/Environment/NS_CelestialAura.NS_CelestialAura"));
                if (UNiagaraSystem* AuraVFX = Cast<UNiagaraSystem>(CelestialAuraPath.TryLoad()))
                {
                    UNiagaraComponent* AuraComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                        AuraVFX,
                        Orb->GetRootComponent(),
                        NAME_None,
                        FVector::ZeroVector,
                        FRotator::ZeroRotator,
                        EAttachLocation::KeepRelativeOffset,
                        false
                    );

                    if (AuraComponent)
                    {
                        AuraComponent->SetVariableFloat(FName("AuraIntensity"), FMath::RandRange(0.7f, 1.2f));
                        AuraComponent->SetVariableLinearColor(FName("AuraColor"), FLinearColor(0.7f, 0.9f, 1.0f, 0.6f));
                    }
                }
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Celestial layer content generated - 8 platforms, 15 orbs"));
}

void UAuracronDynamicRealmSubsystem::GenerateAbyssalContent()
{
    if (!GetWorld())
    {
        return;
    }

    // Generate Abyssal (Abismo Umbrio) content using UE 5.6 dark realm generation
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Generating Abyssal layer content..."));

    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bNoFail = true;

    // Generate shadow rifts
    for (int32 i = 0; i < 6; i++)
    {
        FVector RiftLocation = FVector(
            FMath::RandRange(-7000.0f, 7000.0f),
            FMath::RandRange(-7000.0f, 7000.0f),
            FMath::RandRange(-2000.0f, -500.0f) // Below ground for abyssal
        );

        static const FSoftClassPath ShadowRiftPath(TEXT("/Game/Environment/Abyssal/BP_ShadowRift.BP_ShadowRift_C"));
        if (UClass* RiftClass = ShadowRiftPath.TryLoadClass<AActor>())
        {
            if (AActor* Rift = GetWorld()->SpawnActor<AActor>(RiftClass, RiftLocation, FRotator::ZeroRotator, SpawnParams))
            {
                RegisterActorToLayer(Rift, EAuracronRealmLayer::Abyssal);

                // Add shadow rift effects using UE 5.6 Niagara
                static const FSoftObjectPath ShadowVFXPath(TEXT("/Game/VFX/Environment/NS_ShadowRift.NS_ShadowRift"));
                if (UNiagaraSystem* ShadowVFX = Cast<UNiagaraSystem>(ShadowVFXPath.TryLoad()))
                {
                    UNiagaraComponent* ShadowComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                        ShadowVFX,
                        Rift->GetRootComponent(),
                        NAME_None,
                        FVector::ZeroVector,
                        FRotator::ZeroRotator,
                        EAttachLocation::KeepRelativeOffset,
                        false
                    );

                    if (ShadowComponent)
                    {
                        ShadowComponent->SetVariableFloat(FName("RiftIntensity"), FMath::RandRange(0.8f, 1.5f));
                        ShadowComponent->SetVariableFloat(FName("RiftRadius"), FMath::RandRange(400.0f, 800.0f));
                        ShadowComponent->SetVariableLinearColor(FName("ShadowColor"), FLinearColor(0.1f, 0.0f, 0.2f, 0.8f));
                    }
                }

                // Add void energy field using GameplayEffect
                if (UAbilitySystemComponent* RiftASC = Rift->FindComponentByClass<UAbilitySystemComponent>())
                {
                    // Apply void aura effect to nearby actors
                    static const FSoftClassPath VoidAuraPath(TEXT("/Game/GameplayEffects/Environment/GE_VoidAura.GE_VoidAura_C"));
                    if (TSubclassOf<UGameplayEffect> VoidAuraEffect = VoidAuraPath.TryLoadClass<UGameplayEffect>())
                    {
                        FGameplayEffectContextHandle EffectContext = RiftASC->MakeEffectContext();
                        EffectContext.AddSourceObject(Rift);

                        FGameplayEffectSpec VoidSpec(VoidAuraEffect.GetDefaultObject(), EffectContext, 1.0f);
                        VoidSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Environment.VoidAura.Radius")), 500.0f);
                        VoidSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Environment.VoidAura.Intensity")), 1.0f);
                        VoidSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

                        RiftASC->ApplyGameplayEffectSpecToSelf(VoidSpec);
                    }
                }
            }
        }
    }

    // Generate umbral crystals
    for (int32 i = 0; i < 12; i++)
    {
        FVector CrystalLocation = FVector(
            FMath::RandRange(-8000.0f, 8000.0f),
            FMath::RandRange(-8000.0f, 8000.0f),
            FMath::RandRange(-1500.0f, -200.0f)
        );

        static const FSoftClassPath UmbralCrystalPath(TEXT("/Game/Environment/Abyssal/BP_UmbralCrystal.BP_UmbralCrystal_C"));
        if (UClass* CrystalClass = UmbralCrystalPath.TryLoadClass<AActor>())
        {
            if (AActor* Crystal = GetWorld()->SpawnActor<AActor>(CrystalClass, CrystalLocation, FRotator::ZeroRotator, SpawnParams))
            {
                RegisterActorToLayer(Crystal, EAuracronRealmLayer::Abyssal);

                // Apply dark material with pulsing effect
                if (UStaticMeshComponent* MeshComp = Crystal->FindComponentByClass<UStaticMeshComponent>())
                {
                    if (UMaterialInterface* UmbralMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Game/Materials/Environment/M_UmbralCrystal")))
                    {
                        UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(UmbralMaterial, Crystal);
                        DynamicMaterial->SetScalarParameterValue(TEXT("DarkIntensity"), FMath::RandRange(0.9f, 1.8f));
                        DynamicMaterial->SetVectorParameterValue(TEXT("UmbralColor"), FLinearColor(0.2f, 0.0f, 0.4f, 1.0f));
                        MeshComp->SetMaterial(0, DynamicMaterial);

                        // Add pulsing effect using timer
                        FTimerHandle PulseTimer;
                        GetWorld()->GetTimerManager().SetTimer(
                            PulseTimer,
                            [DynamicMaterial]()
                            {
                                if (IsValid(DynamicMaterial))
                                {
                                    float PulseValue = (FMath::Sin(FPlatformTime::Seconds() * 2.0f) + 1.0f) * 0.5f; // 0-1 range
                                    DynamicMaterial->SetScalarParameterValue(TEXT("PulseIntensity"), PulseValue);
                                }
                            },
                            0.033f, // ~30 FPS for material updates
                            true    // Looping
                        );
                    }
                }

                // Add umbral energy effect
                static const FSoftObjectPath UmbralEnergyPath(TEXT("/Game/VFX/Environment/NS_UmbralEnergy.NS_UmbralEnergy"));
                if (UNiagaraSystem* EnergyVFX = Cast<UNiagaraSystem>(UmbralEnergyPath.TryLoad()))
                {
                    UNiagaraComponent* EnergyComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                        EnergyVFX,
                        Crystal->GetRootComponent(),
                        NAME_None,
                        FVector::ZeroVector,
                        FRotator::ZeroRotator,
                        EAttachLocation::KeepRelativeOffset,
                        false
                    );

                    if (EnergyComponent)
                    {
                        EnergyComponent->SetVariableFloat(FName("EnergyIntensity"), FMath::RandRange(0.6f, 1.3f));
                        EnergyComponent->SetVariableLinearColor(FName("EnergyColor"), FLinearColor(0.3f, 0.0f, 0.6f, 0.7f));
                    }
                }
            }
        }
    }

    // Generate void portals
    for (int32 i = 0; i < 3; i++)
    {
        FVector PortalLocation = FVector(
            FMath::RandRange(-5000.0f, 5000.0f),
            FMath::RandRange(-5000.0f, 5000.0f),
            FMath::RandRange(-1000.0f, -300.0f)
        );

        static const FSoftClassPath VoidPortalPath(TEXT("/Game/Environment/Abyssal/BP_VoidPortal.BP_VoidPortal_C"));
        if (UClass* PortalClass = VoidPortalPath.TryLoadClass<AActor>())
        {
            if (AActor* Portal = GetWorld()->SpawnActor<AActor>(PortalClass, PortalLocation, FRotator::ZeroRotator, SpawnParams))
            {
                RegisterActorToLayer(Portal, EAuracronRealmLayer::Abyssal);

                // Add portal rotation using UE 5.6 rotation system
                FTimerHandle RotationTimer;
                GetWorld()->GetTimerManager().SetTimer(
                    RotationTimer,
                    [Portal]()
                    {
                        if (IsValid(Portal))
                        {
                            FRotator CurrentRotation = Portal->GetActorRotation();
                            FRotator NewRotation = CurrentRotation + FRotator(0.0f, 2.0f, 0.0f); // 2 degrees per frame
                            Portal->SetActorRotation(NewRotation);
                        }
                    },
                    0.016f, // ~60 FPS
                    true    // Looping
                );

                // Add void portal VFX
                static const FSoftObjectPath VoidPortalVFXPath(TEXT("/Game/VFX/Environment/NS_VoidPortal.NS_VoidPortal"));
                if (UNiagaraSystem* PortalVFX = Cast<UNiagaraSystem>(VoidPortalVFXPath.TryLoad()))
                {
                    UNiagaraComponent* PortalComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                        PortalVFX,
                        Portal->GetRootComponent(),
                        NAME_None,
                        FVector::ZeroVector,
                        FRotator::ZeroRotator,
                        EAttachLocation::KeepRelativeOffset,
                        false
                    );

                    if (PortalComponent)
                    {
                        PortalComponent->SetVariableFloat(FName("PortalSize"), FMath::RandRange(200.0f, 500.0f));
                        PortalComponent->SetVariableFloat(FName("VoidIntensity"), FMath::RandRange(1.0f, 2.0f));
                        PortalComponent->SetVariableLinearColor(FName("VoidColor"), FLinearColor(0.0f, 0.0f, 0.0f, 0.9f));
                    }
                }
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Abyssal layer content generated - 6 rifts, 12 crystals, 3 portals"));
}

// === Advanced Layer Transition System Implementation ===

void UAuracronDynamicRealmSubsystem::InitiateAdvancedLayerTransition(AActor* Actor, EAuracronRealmLayer TargetLayer, float TransitionDuration, bool bUseCustomEffect)
{
    if (!Actor || !GetWorld())
    {
        return;
    }

    // Create advanced transition using UE 5.6 transition system
    FAuracronAdvancedRealmTransition AdvancedTransition;
    AdvancedTransition.Actor = Actor;
    AdvancedTransition.SourceLayer = GetActorLayer(Actor);
    AdvancedTransition.TargetLayer = TargetLayer;
    AdvancedTransition.TransitionDuration = TransitionDuration;
    AdvancedTransition.Progress = 0.0f;
    AdvancedTransition.bUseCustomEffect = bUseCustomEffect;
    AdvancedTransition.StartTime = GetWorld()->GetTimeSeconds();
    AdvancedTransition.TransitionID = FGuid::NewGuid();

    // Store original actor properties for smooth transition
    AdvancedTransition.OriginalLocation = Actor->GetActorLocation();
    AdvancedTransition.OriginalRotation = Actor->GetActorRotation();
    AdvancedTransition.OriginalScale = Actor->GetActorScale3D();

    // Calculate target properties based on layer characteristics
    CalculateTargetTransitionProperties(AdvancedTransition);

    // Apply pre-transition effects using UE 5.6 GameplayEffect system
    ApplyPreTransitionEffects(AdvancedTransition);

    // Start transition VFX
    StartTransitionVisualEffects(AdvancedTransition);

    // Add to active advanced transitions
    ActiveAdvancedTransitions.Add(AdvancedTransition);

    // Create transition timer using UE 5.6 timer system
    FTimerHandle TransitionTimer;
    GetWorld()->GetTimerManager().SetTimer(
        TransitionTimer,
        [this, TransitionID = AdvancedTransition.TransitionID]()
        {
            UpdateAdvancedTransition(TransitionID);
        },
        0.016f, // ~60 FPS updates
        true    // Looping
    );

    // Store timer handle for cleanup
    ActiveTransitionTimers.Add(AdvancedTransition.TransitionID, TransitionTimer);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Advanced layer transition initiated - Actor: %s, Target: %s, Duration: %.1fs"),
        *Actor->GetName(), *UEnum::GetValueAsString(TargetLayer), TransitionDuration);
}

void UAuracronDynamicRealmSubsystem::CalculateTargetTransitionProperties(FAuracronAdvancedRealmTransition& Transition)
{
    // Calculate target properties based on layer characteristics using UE 5.6 math utilities
    switch (Transition.TargetLayer)
    {
        case EAuracronRealmLayer::Terrestrial:
            // Terrestrial layer properties
            Transition.TargetLocation = Transition.OriginalLocation + FVector(0.0f, 0.0f, FMath::RandRange(0.0f, 500.0f));
            Transition.TargetRotation = Transition.OriginalRotation;
            Transition.TargetScale = Transition.OriginalScale;
            Transition.TargetLightIntensity = 1.0f;
            Transition.TargetGravityScale = 1.0f;
            Transition.TargetTimeScale = 1.0f;
            break;

        case EAuracronRealmLayer::Celestial:
            // Celestial layer properties - higher altitude, lighter gravity
            Transition.TargetLocation = Transition.OriginalLocation + FVector(0.0f, 0.0f, FMath::RandRange(1500.0f, 3000.0f));
            Transition.TargetRotation = Transition.OriginalRotation;
            Transition.TargetScale = Transition.OriginalScale * 1.1f; // Slightly larger in celestial
            Transition.TargetLightIntensity = 1.5f;
            Transition.TargetGravityScale = 0.6f; // Reduced gravity
            Transition.TargetTimeScale = 0.9f; // Slightly slower time
            break;

        case EAuracronRealmLayer::Abyssal:
            // Abyssal layer properties - lower altitude, heavier gravity, darker
            Transition.TargetLocation = Transition.OriginalLocation + FVector(0.0f, 0.0f, FMath::RandRange(-2000.0f, -500.0f));
            Transition.TargetRotation = Transition.OriginalRotation + FRotator(0.0f, FMath::RandRange(-15.0f, 15.0f), 0.0f);
            Transition.TargetScale = Transition.OriginalScale * 0.9f; // Slightly smaller in abyssal
            Transition.TargetLightIntensity = 0.3f;
            Transition.TargetGravityScale = 1.4f; // Increased gravity
            Transition.TargetTimeScale = 1.1f; // Slightly faster time
            break;

        default:
            // Default properties
            Transition.TargetLocation = Transition.OriginalLocation;
            Transition.TargetRotation = Transition.OriginalRotation;
            Transition.TargetScale = Transition.OriginalScale;
            Transition.TargetLightIntensity = 1.0f;
            Transition.TargetGravityScale = 1.0f;
            Transition.TargetTimeScale = 1.0f;
            break;
    }

    // Apply random variation for natural feel
    FVector RandomOffset = FVector(
        FMath::RandRange(-100.0f, 100.0f),
        FMath::RandRange(-100.0f, 100.0f),
        FMath::RandRange(-50.0f, 50.0f)
    );
    Transition.TargetLocation += RandomOffset;
}

void UAuracronDynamicRealmSubsystem::ApplyPreTransitionEffects(const FAuracronAdvancedRealmTransition& Transition)
{
    if (!Transition.Actor.IsValid())
    {
        return;
    }

    // Apply pre-transition effects using UE 5.6 GameplayEffect system
    if (UAbilitySystemComponent* ActorASC = Transition.Actor->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = ActorASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);
        EffectContext.AddActors({Transition.Actor.Get()}, false);

        // Apply transition preparation effect
        static const FSoftClassPath PreTransitionPath(TEXT("/Game/GameplayEffects/Realm/GE_PreTransition.GE_PreTransition_C"));
        if (TSubclassOf<UGameplayEffect> PreTransitionEffect = PreTransitionPath.TryLoadClass<UGameplayEffect>())
        {
            FGameplayEffectSpec TransitionSpec(PreTransitionEffect.GetDefaultObject(), EffectContext, 1.0f);
            TransitionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Transition.Duration")), Transition.TransitionDuration);
            TransitionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Transition.Intensity")), 1.0f);
            TransitionSpec.SetDuration(Transition.TransitionDuration, false);

            FActiveGameplayEffectHandle EffectHandle = ActorASC->ApplyGameplayEffectSpecToSelf(TransitionSpec);

            // Store effect handle for cleanup
            TransitionEffectHandles.Add(Transition.TransitionID, EffectHandle);
        }
    }
}

void UAuracronDynamicRealmSubsystem::StartTransitionVisualEffects(const FAuracronAdvancedRealmTransition& Transition)
{
    if (!Transition.Actor.IsValid())
    {
        return;
    }

    // Start transition VFX based on source and target layers using UE 5.6 Niagara
    FString VFXPath;
    FLinearColor TransitionColor;

    // Determine VFX based on transition type
    if (Transition.SourceLayer == EAuracronRealmLayer::Terrestrial && Transition.TargetLayer == EAuracronRealmLayer::Celestial)
    {
        VFXPath = TEXT("/Game/VFX/Transitions/NS_TerrestrialToCelestial.NS_TerrestrialToCelestial");
        TransitionColor = FLinearColor(0.8f, 0.9f, 1.0f, 0.8f); // Light blue ascension
    }
    else if (Transition.SourceLayer == EAuracronRealmLayer::Celestial && Transition.TargetLayer == EAuracronRealmLayer::Terrestrial)
    {
        VFXPath = TEXT("/Game/VFX/Transitions/NS_CelestialToTerrestrial.NS_CelestialToTerrestrial");
        TransitionColor = FLinearColor(1.0f, 0.9f, 0.6f, 0.8f); // Golden descent
    }
    else if (Transition.SourceLayer == EAuracronRealmLayer::Terrestrial && Transition.TargetLayer == EAuracronRealmLayer::Abyssal)
    {
        VFXPath = TEXT("/Game/VFX/Transitions/NS_TerrestrialToAbyssal.NS_TerrestrialToAbyssal");
        TransitionColor = FLinearColor(0.3f, 0.0f, 0.6f, 0.9f); // Dark purple descent
    }
    else if (Transition.SourceLayer == EAuracronRealmLayer::Abyssal && Transition.TargetLayer == EAuracronRealmLayer::Terrestrial)
    {
        VFXPath = TEXT("/Game/VFX/Transitions/NS_AbyssalToTerrestrial.NS_AbyssalToTerrestrial");
        TransitionColor = FLinearColor(0.6f, 0.3f, 0.8f, 0.8f); // Purple ascension
    }
    else if (Transition.SourceLayer == EAuracronRealmLayer::Celestial && Transition.TargetLayer == EAuracronRealmLayer::Abyssal)
    {
        VFXPath = TEXT("/Game/VFX/Transitions/NS_CelestialToAbyssal.NS_CelestialToAbyssal");
        TransitionColor = FLinearColor(0.1f, 0.1f, 0.1f, 1.0f); // Dark void transition
    }
    else if (Transition.SourceLayer == EAuracronRealmLayer::Abyssal && Transition.TargetLayer == EAuracronRealmLayer::Celestial)
    {
        VFXPath = TEXT("/Game/VFX/Transitions/NS_AbyssalToCelestial.NS_AbyssalToCelestial");
        TransitionColor = FLinearColor(0.9f, 0.9f, 1.0f, 0.9f); // Bright ascension
    }
    else
    {
        // Default transition VFX
        VFXPath = TEXT("/Game/VFX/Transitions/NS_DefaultRealmTransition.NS_DefaultRealmTransition");
        TransitionColor = FLinearColor(0.5f, 0.5f, 0.5f, 0.7f);
    }

    // Spawn transition VFX using UE 5.6 Niagara system
    FSoftObjectPath TransitionVFXPath(VFXPath);
    if (UNiagaraSystem* TransitionVFX = Cast<UNiagaraSystem>(TransitionVFXPath.TryLoad()))
    {
        UNiagaraComponent* TransitionComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            TransitionVFX,
            Transition.Actor->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            true // Auto-destroy when complete
        );

        if (TransitionComponent)
        {
            // Set transition parameters
            TransitionComponent->SetVariableFloat(FName("TransitionDuration"), Transition.TransitionDuration);
            TransitionComponent->SetVariableLinearColor(FName("TransitionColor"), TransitionColor);
            TransitionComponent->SetVariableFloat(FName("TransitionIntensity"), 1.0f);
            TransitionComponent->SetVariableVec3(FName("TargetDirection"), (Transition.TargetLocation - Transition.OriginalLocation).GetSafeNormal());

            // Store VFX component for updates
            TransitionVFXComponents.Add(Transition.TransitionID, TransitionComponent);
        }
    }

    // Play transition audio using UE 5.6 MetaSound
    FString AudioPath;
    switch (Transition.TargetLayer)
    {
        case EAuracronRealmLayer::Terrestrial:
            AudioPath = TEXT("/Game/Audio/Transitions/MS_TransitionToTerrestrial.MS_TransitionToTerrestrial");
            break;
        case EAuracronRealmLayer::Celestial:
            AudioPath = TEXT("/Game/Audio/Transitions/MS_TransitionToCelestial.MS_TransitionToCelestial");
            break;
        case EAuracronRealmLayer::Abyssal:
            AudioPath = TEXT("/Game/Audio/Transitions/MS_TransitionToAbyssal.MS_TransitionToAbyssal");
            break;
        default:
            AudioPath = TEXT("/Game/Audio/Transitions/MS_DefaultTransition.MS_DefaultTransition");
            break;
    }

    FSoftObjectPath TransitionAudioPath(AudioPath);
    if (UMetaSoundSource* TransitionAudio = Cast<UMetaSoundSource>(TransitionAudioPath.TryLoad()))
    {
        UAudioComponent* AudioComponent = UGameplayStatics::SpawnSoundAttached(
            TransitionAudio,
            Transition.Actor->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            true // Auto-destroy when complete
        );

        if (AudioComponent)
        {
            // Set audio parameters
            AudioComponent->SetFloatParameter(TEXT("TransitionDuration"), Transition.TransitionDuration);
            AudioComponent->SetFloatParameter(TEXT("TransitionIntensity"), 1.0f);
            AudioComponent->SetFloatParameter(TEXT("LayerDepth"), GetLayerDepthValue(Transition.TargetLayer));

            // Store audio component for cleanup
            TransitionAudioComponents.Add(Transition.TransitionID, AudioComponent);
        }
    }
}

void UAuracronDynamicRealmSubsystem::UpdateAdvancedTransition(const FGuid& TransitionID)
{
    // Find and update the specific transition using UE 5.6 efficient search
    FAuracronAdvancedRealmTransition* FoundTransition = ActiveAdvancedTransitions.FindByPredicate(
        [TransitionID](const FAuracronAdvancedRealmTransition& Transition)
        {
            return Transition.TransitionID == TransitionID;
        }
    );

    if (!FoundTransition || !FoundTransition->Actor.IsValid())
    {
        // Cleanup invalid transition
        CleanupAdvancedTransition(TransitionID);
        return;
    }

    // Update transition progress
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float ElapsedTime = CurrentTime - FoundTransition->StartTime;
    FoundTransition->Progress = FMath::Clamp(ElapsedTime / FoundTransition->TransitionDuration, 0.0f, 1.0f);

    // Apply smooth transition using UE 5.6 interpolation
    float Alpha = FMath::SmoothStep(0.0f, 1.0f, FoundTransition->Progress);

    // Interpolate location
    FVector CurrentLocation = FMath::Lerp(FoundTransition->OriginalLocation, FoundTransition->TargetLocation, Alpha);
    FoundTransition->Actor->SetActorLocation(CurrentLocation);

    // Interpolate rotation
    FRotator CurrentRotation = FMath::Lerp(FoundTransition->OriginalRotation, FoundTransition->TargetRotation, Alpha);
    FoundTransition->Actor->SetActorRotation(CurrentRotation);

    // Interpolate scale
    FVector CurrentScale = FMath::Lerp(FoundTransition->OriginalScale, FoundTransition->TargetScale, Alpha);
    FoundTransition->Actor->SetActorScale3D(CurrentScale);

    // Apply layer-specific effects during transition
    ApplyTransitionLayerEffects(*FoundTransition, Alpha);

    // Update VFX parameters
    if (UNiagaraComponent* VFXComp = TransitionVFXComponents.FindRef(TransitionID))
    {
        if (IsValid(VFXComp))
        {
            VFXComp->SetVariableFloat(FName("TransitionProgress"), FoundTransition->Progress);
            VFXComp->SetVariableFloat(FName("TransitionAlpha"), Alpha);
        }
    }

    // Check if transition is complete
    if (FoundTransition->Progress >= 1.0f)
    {
        CompleteAdvancedTransition(TransitionID);
    }
}

void UAuracronDynamicRealmSubsystem::ApplyTransitionLayerEffects(const FAuracronAdvancedRealmTransition& Transition, float Alpha)
{
    if (!Transition.Actor.IsValid())
    {
        return;
    }

    // Apply layer-specific effects during transition using UE 5.6 systems
    AActor* Actor = Transition.Actor.Get();

    // Apply gravity changes for characters
    if (ACharacter* Character = Cast<ACharacter>(Actor))
    {
        if (UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement())
        {
            float CurrentGravityScale = FMath::Lerp(1.0f, Transition.TargetGravityScale, Alpha);
            MovementComp->GravityScale = CurrentGravityScale;
        }
    }

    // Apply time dilation effects using UE 5.6 time system
    float CurrentTimeScale = FMath::Lerp(1.0f, Transition.TargetTimeScale, Alpha);
    Actor->CustomTimeDilation = CurrentTimeScale;

    // Apply lighting changes to mesh components
    TArray<UMeshComponent*> MeshComponents;
    Actor->GetComponents<UMeshComponent>(MeshComponents);

    for (UMeshComponent* MeshComp : MeshComponents)
    {
        if (MeshComp)
        {
            // Create dynamic material for transition effects
            for (int32 MaterialIndex = 0; MaterialIndex < MeshComp->GetNumMaterials(); MaterialIndex++)
            {
                if (UMaterialInterface* OriginalMaterial = MeshComp->GetMaterial(MaterialIndex))
                {
                    UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(OriginalMaterial, Actor);

                    // Apply layer-specific material effects
                    switch (Transition.TargetLayer)
                    {
                        case EAuracronRealmLayer::Terrestrial:
                            DynamicMaterial->SetScalarParameterValue(TEXT("RadiantGlow"), Alpha * 0.8f);
                            DynamicMaterial->SetVectorParameterValue(TEXT("LayerTint"), FLinearColor(1.0f, 0.9f, 0.6f, Alpha));
                            break;
                        case EAuracronRealmLayer::Celestial:
                            DynamicMaterial->SetScalarParameterValue(TEXT("CelestialGlow"), Alpha * 1.2f);
                            DynamicMaterial->SetVectorParameterValue(TEXT("LayerTint"), FLinearColor(0.7f, 0.9f, 1.0f, Alpha));
                            break;
                        case EAuracronRealmLayer::Abyssal:
                            DynamicMaterial->SetScalarParameterValue(TEXT("UmbralDarkness"), Alpha * 1.5f);
                            DynamicMaterial->SetVectorParameterValue(TEXT("LayerTint"), FLinearColor(0.2f, 0.0f, 0.4f, Alpha));
                            break;
                        default:
                            break;
                    }

                    MeshComp->SetMaterial(MaterialIndex, DynamicMaterial);
                }
            }
        }
    }

    // Apply atmospheric effects based on transition progress
    if (Alpha > 0.5f) // Halfway through transition
    {
        ApplyLayerAtmosphericEffects(Actor, Transition.TargetLayer, (Alpha - 0.5f) * 2.0f);
    }
}

void UAuracronDynamicRealmSubsystem::ApplyLayerAtmosphericEffects(AActor* Actor, EAuracronRealmLayer Layer, float Intensity)
{
    if (!Actor)
    {
        return;
    }

    // Apply atmospheric effects based on layer using UE 5.6 environmental system
    switch (Layer)
    {
        case EAuracronRealmLayer::Terrestrial:
            {
                // Apply radiant atmosphere
                static const FSoftObjectPath RadiantAtmospherePath(TEXT("/Game/VFX/Atmosphere/NS_RadiantAtmosphere.NS_RadiantAtmosphere"));
                if (UNiagaraSystem* AtmosphereVFX = Cast<UNiagaraSystem>(RadiantAtmospherePath.TryLoad()))
                {
                    UNiagaraComponent* AtmosphereComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                        AtmosphereVFX,
                        Actor->GetRootComponent(),
                        NAME_None,
                        FVector(0.0f, 0.0f, 50.0f),
                        FRotator::ZeroRotator,
                        EAttachLocation::KeepRelativeOffset,
                        false
                    );

                    if (AtmosphereComponent)
                    {
                        AtmosphereComponent->SetVariableFloat(FName("AtmosphereIntensity"), Intensity);
                        AtmosphereComponent->SetVariableLinearColor(FName("AtmosphereColor"), FLinearColor(1.0f, 0.9f, 0.6f, Intensity * 0.3f));
                    }
                }
            }
            break;

        case EAuracronRealmLayer::Celestial:
            {
                // Apply celestial atmosphere with wind effects
                static const FSoftObjectPath CelestialAtmospherePath(TEXT("/Game/VFX/Atmosphere/NS_CelestialAtmosphere.NS_CelestialAtmosphere"));
                if (UNiagaraSystem* AtmosphereVFX = Cast<UNiagaraSystem>(CelestialAtmospherePath.TryLoad()))
                {
                    UNiagaraComponent* AtmosphereComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                        AtmosphereVFX,
                        Actor->GetRootComponent(),
                        NAME_None,
                        FVector(0.0f, 0.0f, 100.0f),
                        FRotator::ZeroRotator,
                        EAttachLocation::KeepRelativeOffset,
                        false
                    );

                    if (AtmosphereComponent)
                    {
                        AtmosphereComponent->SetVariableFloat(FName("WindStrength"), Intensity * 1.5f);
                        AtmosphereComponent->SetVariableLinearColor(FName("SkyColor"), FLinearColor(0.6f, 0.8f, 1.0f, Intensity * 0.4f));
                        AtmosphereComponent->SetVariableVec3(FName("WindDirection"), FVector(1.0f, 0.5f, 0.2f).GetSafeNormal());
                    }
                }

                // Apply reduced gravity effect
                if (ACharacter* Character = Cast<ACharacter>(Actor))
                {
                    if (UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement())
                    {
                        float GravityReduction = FMath::Lerp(1.0f, 0.6f, Intensity);
                        MovementComp->GravityScale = GravityReduction;
                    }
                }
            }
            break;

        case EAuracronRealmLayer::Abyssal:
            {
                // Apply abyssal atmosphere with void effects
                static const FSoftObjectPath AbyssalAtmospherePath(TEXT("/Game/VFX/Atmosphere/NS_AbyssalAtmosphere.NS_AbyssalAtmosphere"));
                if (UNiagaraSystem* AtmosphereVFX = Cast<UNiagaraSystem>(AbyssalAtmospherePath.TryLoad()))
                {
                    UNiagaraComponent* AtmosphereComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                        AtmosphereVFX,
                        Actor->GetRootComponent(),
                        NAME_None,
                        FVector(0.0f, 0.0f, -50.0f),
                        FRotator::ZeroRotator,
                        EAttachLocation::KeepRelativeOffset,
                        false
                    );

                    if (AtmosphereComponent)
                    {
                        AtmosphereComponent->SetVariableFloat(FName("VoidIntensity"), Intensity * 1.8f);
                        AtmosphereComponent->SetVariableLinearColor(FName("VoidColor"), FLinearColor(0.1f, 0.0f, 0.3f, Intensity * 0.6f));
                        AtmosphereComponent->SetVariableFloat(FName("CorruptionLevel"), Intensity);
                    }
                }

                // Apply increased gravity and corruption effects
                if (ACharacter* Character = Cast<ACharacter>(Actor))
                {
                    if (UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement())
                    {
                        float GravityIncrease = FMath::Lerp(1.0f, 1.4f, Intensity);
                        MovementComp->GravityScale = GravityIncrease;
                    }
                }

                // Apply corruption visual effect to materials
                TArray<UMeshComponent*> MeshComponents;
                Actor->GetComponents<UMeshComponent>(MeshComponents);

                for (UMeshComponent* MeshComp : MeshComponents)
                {
                    if (MeshComp)
                    {
                        for (int32 MaterialIndex = 0; MaterialIndex < MeshComp->GetNumMaterials(); MaterialIndex++)
                        {
                            if (UMaterialInstanceDynamic* DynamicMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(MaterialIndex)))
                            {
                                DynamicMaterial->SetScalarParameterValue(TEXT("CorruptionLevel"), Intensity * 0.7f);
                                DynamicMaterial->SetVectorParameterValue(TEXT("CorruptionColor"), FLinearColor(0.2f, 0.0f, 0.4f, Intensity));
                            }
                        }
                    }
                }
            }
            break;

        default:
            break;
    }
}

void UAuracronDynamicRealmSubsystem::CompleteAdvancedTransition(const FGuid& TransitionID)
{
    // Find the transition to complete
    FAuracronAdvancedRealmTransition* CompletingTransition = ActiveAdvancedTransitions.FindByPredicate(
        [TransitionID](const FAuracronAdvancedRealmTransition& Transition)
        {
            return Transition.TransitionID == TransitionID;
        }
    );

    if (!CompletingTransition || !CompletingTransition->Actor.IsValid())
    {
        CleanupAdvancedTransition(TransitionID);
        return;
    }

    AActor* Actor = CompletingTransition->Actor.Get();
    EAuracronRealmLayer TargetLayer = CompletingTransition->TargetLayer;

    // Finalize actor position and properties
    Actor->SetActorLocation(CompletingTransition->TargetLocation);
    Actor->SetActorRotation(CompletingTransition->TargetRotation);
    Actor->SetActorScale3D(CompletingTransition->TargetScale);

    // Apply final layer effects using UE 5.6 GameplayEffect system
    if (UAbilitySystemComponent* ActorASC = Actor->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = ActorASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        // Apply layer-specific permanent effects
        FString LayerEffectPath;
        switch (TargetLayer)
        {
            case EAuracronRealmLayer::Terrestrial:
                LayerEffectPath = TEXT("/Game/GameplayEffects/Realm/GE_TerrestrialLayerEffect.GE_TerrestrialLayerEffect_C");
                break;
            case EAuracronRealmLayer::Celestial:
                LayerEffectPath = TEXT("/Game/GameplayEffects/Realm/GE_CelestialLayerEffect.GE_CelestialLayerEffect_C");
                break;
            case EAuracronRealmLayer::Abyssal:
                LayerEffectPath = TEXT("/Game/GameplayEffects/Realm/GE_AbyssalLayerEffect.GE_AbyssalLayerEffect_C");
                break;
            default:
                break;
        }

        if (!LayerEffectPath.IsEmpty())
        {
            FSoftClassPath LayerEffectSoftPath(LayerEffectPath);
            if (TSubclassOf<UGameplayEffect> LayerEffect = LayerEffectSoftPath.TryLoadClass<UGameplayEffect>())
            {
                FGameplayEffectSpec LayerSpec(LayerEffect.GetDefaultObject(), EffectContext, 1.0f);
                LayerSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Layer.Intensity")), 1.0f);
                LayerSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

                FActiveGameplayEffectHandle LayerEffectHandle = ActorASC->ApplyGameplayEffectSpecToSelf(LayerSpec);

                // Store layer effect handle for potential removal
                ActorLayerEffects.Add(Actor, LayerEffectHandle);
            }
        }
    }

    // Update actor's layer registration
    RegisterActorToLayer(Actor, TargetLayer);

    // Broadcast transition completion event
    OnAdvancedRealmTransitionComplete.Broadcast(Actor, CompletingTransition->SourceLayer, TargetLayer, CompletingTransition->TransitionDuration);

    // Cleanup transition
    CleanupAdvancedTransition(TransitionID);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Advanced transition completed - Actor: %s moved to %s"),
        *Actor->GetName(), *UEnum::GetValueAsString(TargetLayer));
}

void UAuracronDynamicRealmSubsystem::CleanupAdvancedTransition(const FGuid& TransitionID)
{
    // Remove transition from active list
    ActiveAdvancedTransitions.RemoveAll([TransitionID](const FAuracronAdvancedRealmTransition& Transition)
    {
        return Transition.TransitionID == TransitionID;
    });

    // Clear timer
    if (FTimerHandle* TimerHandle = ActiveTransitionTimers.Find(TransitionID))
    {
        if (GetWorld())
        {
            GetWorld()->GetTimerManager().ClearTimer(*TimerHandle);
        }
        ActiveTransitionTimers.Remove(TransitionID);
    }

    // Cleanup VFX component
    if (UNiagaraComponent* VFXComp = TransitionVFXComponents.FindRef(TransitionID))
    {
        if (IsValid(VFXComp))
        {
            VFXComp->DestroyComponent();
        }
        TransitionVFXComponents.Remove(TransitionID);
    }

    // Cleanup audio component
    if (UAudioComponent* AudioComp = TransitionAudioComponents.FindRef(TransitionID))
    {
        if (IsValid(AudioComp))
        {
            AudioComp->Stop();
            AudioComp->DestroyComponent();
        }
        TransitionAudioComponents.Remove(TransitionID);
    }

    // Remove transition effect
    if (FActiveGameplayEffectHandle* EffectHandle = TransitionEffectHandles.Find(TransitionID))
    {
        // This would be cleaned up by the GameplayEffect duration, but we can force remove if needed
        TransitionEffectHandles.Remove(TransitionID);
    }
}

float UAuracronDynamicRealmSubsystem::GetLayerDepthValue(EAuracronRealmLayer Layer) const
{
    // Return depth value for audio and VFX parameters
    switch (Layer)
    {
        case EAuracronRealmLayer::Celestial:
            return 1.0f; // Highest layer
        case EAuracronRealmLayer::Terrestrial:
            return 0.0f; // Middle layer
        case EAuracronRealmLayer::Abyssal:
            return -1.0f; // Lowest layer
        default:
            return 0.0f;
    }
}

// === Advanced Dynamic Layer Evolution System ===

void UAuracronDynamicRealmSubsystem::InitializeLayerEvolutionSystem()
{
    if (!GetWorld())
    {
        return;
    }

    // Initialize layer evolution using UE 5.6 advanced systems
    bLayerEvolutionActive = true;
    LayerEvolutionStartTime = GetWorld()->GetTimeSeconds();

    // Initialize evolution data for each layer
    for (int32 LayerInt = 0; LayerInt < static_cast<int32>(EAuracronRealmLayer::MAX); LayerInt++)
    {
        EAuracronRealmLayer Layer = static_cast<EAuracronRealmLayer>(LayerInt);

        FAuracronRealmLayerEvolutionData EvolutionData;
        EvolutionData.Layer = Layer;
        EvolutionData.EvolutionStage = EAuracronLayerEvolutionStage::Dormant;
        EvolutionData.EvolutionProgress = 0.0f;
        EvolutionData.StabilityLevel = 1.0f;
        EvolutionData.EnergyAccumulation = 0.0f;
        EvolutionData.LastEvolutionTime = GetWorld()->GetTimeSeconds();
        EvolutionData.EvolutionRate = CalculateBaseEvolutionRate(Layer);

        LayerEvolutionData.Add(Layer, EvolutionData);
    }

    // Start evolution update timer using UE 5.6 timer system
    GetWorld()->GetTimerManager().SetTimer(
        LayerEvolutionTimer,
        [this]()
        {
            UpdateLayerEvolution();
        },
        1.0f, // Update every second
        true  // Looping
    );

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Layer evolution system initialized"));
}

void UAuracronDynamicRealmSubsystem::UpdateLayerEvolution()
{
    if (!bLayerEvolutionActive || !GetWorld())
    {
        return;
    }

    float CurrentTime = GetWorld()->GetTimeSeconds();
    float DeltaTime = CurrentTime - LastEvolutionUpdateTime;
    LastEvolutionUpdateTime = CurrentTime;

    // Update evolution for each layer
    for (auto& EvolutionPair : LayerEvolutionData)
    {
        EAuracronRealmLayer Layer = EvolutionPair.Key;
        FAuracronRealmLayerEvolutionData& EvolutionData = EvolutionPair.Value;

        // Calculate evolution factors using UE 5.6 analytics
        float ActivityFactor = CalculateLayerActivityFactor(Layer);
        float PlayerPresenceFactor = CalculatePlayerPresenceFactor(Layer);
        float EnergyFactor = CalculateLayerEnergyFactor(Layer);

        // Update energy accumulation
        float EnergyGain = (ActivityFactor + PlayerPresenceFactor + EnergyFactor) * DeltaTime * EvolutionData.EvolutionRate;
        EvolutionData.EnergyAccumulation += EnergyGain;

        // Update evolution progress
        float EvolutionGain = EnergyGain * 0.1f; // 10% of energy becomes evolution progress
        EvolutionData.EvolutionProgress += EvolutionGain;

        // Check for evolution stage advancement
        CheckEvolutionStageAdvancement(Layer, EvolutionData);

        // Apply evolution effects to layer
        ApplyEvolutionEffectsToLayer(Layer, EvolutionData);

        // Update stability based on evolution
        UpdateLayerStability(Layer, EvolutionData);
    }

    // Check for global evolution events
    CheckGlobalEvolutionEvents();
}

float UAuracronDynamicRealmSubsystem::CalculateLayerActivityFactor(EAuracronRealmLayer Layer) const
{
    // Calculate activity factor based on actors and interactions in layer
    TArray<AActor*> LayerActors = GetActorsInLayer(Layer);

    float ActivityFactor = 0.0f;

    // Base activity from actor count
    ActivityFactor += FMath::Min(LayerActors.Num() * 0.1f, 2.0f);

    // Activity from player presence
    for (AActor* Actor : LayerActors)
    {
        if (APawn* Pawn = Cast<APawn>(Actor))
        {
            if (Pawn->IsPlayerControlled())
            {
                ActivityFactor += 1.0f; // Strong boost for player presence

                // Additional boost for player actions
                if (UAbilitySystemComponent* ASC = Pawn->FindComponentByClass<UAbilitySystemComponent>())
                {
                    // Check for active abilities
                    TArray<FGameplayAbilitySpec> ActiveAbilities = ASC->GetActivatableAbilities();
                    for (const FGameplayAbilitySpec& AbilitySpec : ActiveAbilities)
                    {
                        if (AbilitySpec.IsActive())
                        {
                            ActivityFactor += 0.2f; // Boost for active abilities
                        }
                    }
                }
            }
        }
    }

    // Activity from environmental interactions
    ActivityFactor += CalculateEnvironmentalActivity(Layer);

    return FMath::Clamp(ActivityFactor, 0.0f, 5.0f);
}

float UAuracronDynamicRealmSubsystem::CalculatePlayerPresenceFactor(EAuracronRealmLayer Layer) const
{
    // Calculate player presence factor using UE 5.6 player tracking
    float PresenceFactor = 0.0f;

    TArray<AActor*> LayerActors = GetActorsInLayer(Layer);

    for (AActor* Actor : LayerActors)
    {
        if (APawn* Pawn = Cast<APawn>(Actor))
        {
            if (APlayerController* PC = Pawn->GetController<APlayerController>())
            {
                // Base presence
                PresenceFactor += 1.0f;

                // Time-based presence bonus
                float TimeInLayer = GetWorld()->GetTimeSeconds() - GetActorLayerEntryTime(Actor);
                PresenceFactor += FMath::Min(TimeInLayer * 0.01f, 1.0f); // Up to 1.0 bonus for 100 seconds

                // Activity-based presence bonus
                if (PC->GetInputKeyTimeDown(EKeys::W) > 0.0f || PC->GetInputKeyTimeDown(EKeys::A) > 0.0f ||
                    PC->GetInputKeyTimeDown(EKeys::S) > 0.0f || PC->GetInputKeyTimeDown(EKeys::D) > 0.0f)
                {
                    PresenceFactor += 0.5f; // Bonus for active movement
                }
            }
        }
    }

    return FMath::Clamp(PresenceFactor, 0.0f, 10.0f);
}

float UAuracronDynamicRealmSubsystem::CalculateLayerEnergyFactor(EAuracronRealmLayer Layer) const
{
    // Calculate energy factor based on layer-specific sources
    float EnergyFactor = 0.0f;

    // Get layer data
    if (const FAuracronRealmLayerData* LayerData = RealmLayers.Find(Layer))
    {
        // Base energy from layer activity
        EnergyFactor += LayerData->bIsActive ? 1.0f : 0.5f;

        // Energy from layer actor count
        EnergyFactor += LayerData->ActiveActorCount * 0.01f;

        // Time-based energy accumulation
        float TimeSinceLastUpdate = GetWorld()->GetTimeSeconds() - LayerData->LastUpdateTime;
        EnergyFactor += FMath::Min(TimeSinceLastUpdate * 0.02f, 1.0f);
    }

    // Layer-specific energy sources
    switch (Layer)
    {
        case EAuracronRealmLayer::Terrestrial:
            // Energy from radiant crystals and natural sources
            EnergyFactor += CountLayerSpecificObjects(Layer, TEXT("RadiantCrystal")) * 0.1f;
            EnergyFactor += CountLayerSpecificObjects(Layer, TEXT("FloatingIsland")) * 0.05f;
            break;

        case EAuracronRealmLayer::Celestial:
            // Energy from celestial orbs and wind currents
            EnergyFactor += CountLayerSpecificObjects(Layer, TEXT("CelestialOrb")) * 0.08f;
            EnergyFactor += CountLayerSpecificObjects(Layer, TEXT("CelestialPlatform")) * 0.12f;
            break;

        case EAuracronRealmLayer::Abyssal:
            // Energy from void rifts and umbral crystals
            EnergyFactor += CountLayerSpecificObjects(Layer, TEXT("ShadowRift")) * 0.15f;
            EnergyFactor += CountLayerSpecificObjects(Layer, TEXT("UmbralCrystal")) * 0.07f;
            EnergyFactor += CountLayerSpecificObjects(Layer, TEXT("VoidPortal")) * 0.25f;
            break;

        default:
            break;
    }

    return FMath::Clamp(EnergyFactor, 0.0f, 8.0f);
}

float UAuracronDynamicRealmSubsystem::CalculateEnvironmentalActivity(EAuracronRealmLayer Layer) const
{
    // Calculate environmental activity using UE 5.6 environmental tracking
    float EnvironmentalActivity = 0.0f;

    TArray<AActor*> LayerActors = GetActorsInLayer(Layer);

    for (AActor* Actor : LayerActors)
    {
        if (!Actor)
        {
            continue;
        }

        // Activity from moving objects
        if (Actor->GetVelocity().Size() > 10.0f)
        {
            EnvironmentalActivity += 0.1f;
        }

        // Activity from particle systems
        TArray<UNiagaraComponent*> NiagaraComponents;
        Actor->GetComponents<UNiagaraComponent>(NiagaraComponents);
        for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
        {
            if (NiagaraComp && NiagaraComp->IsActive())
            {
                EnvironmentalActivity += 0.05f;
            }
        }

        // Activity from audio sources
        TArray<UAudioComponent*> AudioComponents;
        Actor->GetComponents<UAudioComponent>(AudioComponents);
        for (UAudioComponent* AudioComp : AudioComponents)
        {
            if (AudioComp && AudioComp->IsPlaying())
            {
                EnvironmentalActivity += 0.03f;
            }
        }

        // Activity from light sources
        TArray<ULightComponent*> LightComponents;
        Actor->GetComponents<ULightComponent>(LightComponents);
        for (ULightComponent* LightComp : LightComponents)
        {
            if (LightComp && LightComp->GetLightColor().GetLuminance() > 0.1f)
            {
                EnvironmentalActivity += 0.02f;
            }
        }
    }

    return FMath::Clamp(EnvironmentalActivity, 0.0f, 3.0f);
}

void UAuracronDynamicRealmSubsystem::CheckEvolutionStageAdvancement(EAuracronRealmLayer Layer, FAuracronRealmLayerEvolutionData& EvolutionData)
{
    // Check if layer should advance to next evolution stage using UE 5.6 progression system
    EAuracronLayerEvolutionStage NextStage = this->GetNextEvolutionStage(EvolutionData.EvolutionStage);

    if (NextStage == EvolutionData.EvolutionStage)
    {
        return; // Already at max stage
    }

    float RequiredProgress = this->GetRequiredProgressForStage(NextStage);

    if (EvolutionData.EvolutionProgress >= RequiredProgress)
    {
        // Advance to next stage
        EAuracronLayerEvolutionStage PreviousStage = EvolutionData.EvolutionStage;
        EvolutionData.EvolutionStage = NextStage;
        EvolutionData.LastEvolutionTime = this->GetWorld()->GetTimeSeconds();

        // Apply stage advancement effects
        this->ApplyEvolutionStageEffects(Layer, NextStage);

        // Broadcast evolution event
        OnLayerEvolutionStageChanged.Broadcast(Layer, PreviousStage, NextStage);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Layer %s evolved from %s to %s"),
            *UEnum::GetValueAsString(Layer),
            *UEnum::GetValueAsString(PreviousStage),
            *UEnum::GetValueAsString(NextStage));
    }
}

EAuracronLayerEvolutionStage UAuracronDynamicRealmSubsystem::GetNextEvolutionStage(EAuracronLayerEvolutionStage CurrentStage) const
{
    switch (CurrentStage)
    {
        case EAuracronLayerEvolutionStage::Dormant:
            return EAuracronLayerEvolutionStage::Awakening;
        case EAuracronLayerEvolutionStage::Awakening:
            return EAuracronLayerEvolutionStage::Active;
        case EAuracronLayerEvolutionStage::Active:
            return EAuracronLayerEvolutionStage::Resonant;
        case EAuracronLayerEvolutionStage::Resonant:
            return EAuracronLayerEvolutionStage::Transcendent;
        case EAuracronLayerEvolutionStage::Transcendent:
            return EAuracronLayerEvolutionStage::Transcendent; // Max stage
        default:
            return CurrentStage;
    }
}

float UAuracronDynamicRealmSubsystem::GetRequiredProgressForStage(EAuracronLayerEvolutionStage Stage) const
{
    // Define required progress thresholds for each evolution stage
    switch (Stage)
    {
        case EAuracronLayerEvolutionStage::Awakening:
            return 10.0f;
        case EAuracronLayerEvolutionStage::Active:
            return 25.0f;
        case EAuracronLayerEvolutionStage::Resonant:
            return 50.0f;
        case EAuracronLayerEvolutionStage::Transcendent:
            return 100.0f;
        default:
            return 0.0f;
    }
}

void UAuracronDynamicRealmSubsystem::ApplyEvolutionStageEffects(EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage)
{
    // Apply evolution stage effects using UE 5.6 effect system
    TArray<AActor*> LayerActors = GetActorsInLayer(Layer);

    for (AActor* Actor : LayerActors)
    {
        if (!Actor)
        {
            continue;
        }

        // Apply stage-specific visual effects
        ApplyStageVisualEffects(Actor, Layer, Stage);

        // Apply stage-specific gameplay effects
        ApplyStageGameplayEffects(Actor, Layer, Stage);

        // Apply stage-specific environmental changes
        ApplyStageEnvironmentalChanges(Actor, Layer, Stage);
    }

    // Apply global layer effects
    ApplyGlobalLayerEvolutionEffects(Layer, Stage);
}

void UAuracronDynamicRealmSubsystem::ApplyStageVisualEffects(AActor* Actor, EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage)
{
    if (!Actor)
    {
        return;
    }

    // Apply visual effects based on evolution stage using UE 5.6 Niagara
    FString VFXPath;
    FLinearColor EvolutionColor;
    float EffectIntensity = GetStageIntensityMultiplier(Stage);

    // Determine VFX based on layer and stage
    switch (Layer)
    {
        case EAuracronRealmLayer::Terrestrial:
            VFXPath = FString::Printf(TEXT("/Game/VFX/Evolution/Terrestrial/NS_TerrestrialEvolution_%s.NS_TerrestrialEvolution_%s"),
                *UEnum::GetValueAsString(Stage), *UEnum::GetValueAsString(Stage));
            EvolutionColor = FLinearColor(1.0f, 0.8f, 0.4f, EffectIntensity); // Golden radiance
            break;

        case EAuracronRealmLayer::Celestial:
            VFXPath = FString::Printf(TEXT("/Game/VFX/Evolution/Celestial/NS_CelestialEvolution_%s.NS_CelestialEvolution_%s"),
                *UEnum::GetValueAsString(Stage), *UEnum::GetValueAsString(Stage));
            EvolutionColor = FLinearColor(0.6f, 0.8f, 1.0f, EffectIntensity); // Celestial blue
            break;

        case EAuracronRealmLayer::Abyssal:
            VFXPath = FString::Printf(TEXT("/Game/VFX/Evolution/Abyssal/NS_AbyssalEvolution_%s.NS_AbyssalEvolution_%s"),
                *UEnum::GetValueAsString(Stage), *UEnum::GetValueAsString(Stage));
            EvolutionColor = FLinearColor(0.3f, 0.0f, 0.6f, EffectIntensity); // Dark purple void
            break;

        default:
            return;
    }

    // Spawn evolution VFX
    FSoftObjectPath EvolutionVFXPath(VFXPath);
    if (UNiagaraSystem* EvolutionVFX = Cast<UNiagaraSystem>(EvolutionVFXPath.TryLoad()))
    {
        UNiagaraComponent* EvolutionComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            EvolutionVFX,
            Actor->GetRootComponent(),
            NAME_None,
            FVector(0.0f, 0.0f, 50.0f),
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            false
        );

        if (EvolutionComponent)
        {
            EvolutionComponent->SetVariableFloat(FName("EvolutionIntensity"), EffectIntensity);
            EvolutionComponent->SetVariableLinearColor(FName("EvolutionColor"), EvolutionColor);
            EvolutionComponent->SetVariableFloat(FName("StageLevel"), static_cast<float>(Stage));

            // Store evolution VFX for cleanup - use layer as key instead of GUID
            EvolutionVFXComponents.Add(Layer, EvolutionComponent);
        }
    }
}

void UAuracronDynamicRealmSubsystem::ApplyStageGameplayEffects(AActor* Actor, EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage)
{
    if (!Actor)
    {
        return;
    }

    // Apply gameplay effects based on evolution stage using UE 5.6 GameplayEffect system
    if (UAbilitySystemComponent* ActorASC = Actor->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = ActorASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        // Determine effect path based on layer and stage
        FString EffectPath = FString::Printf(TEXT("/Game/GameplayEffects/Evolution/%s/GE_%sEvolution_%s.GE_%sEvolution_%s_C"),
            *UEnum::GetValueAsString(Layer),
            *UEnum::GetValueAsString(Layer),
            *UEnum::GetValueAsString(Stage),
            *UEnum::GetValueAsString(Layer),
            *UEnum::GetValueAsString(Stage));

        FSoftClassPath EvolutionEffectPath(EffectPath);
        if (TSubclassOf<UGameplayEffect> EvolutionEffectClass = EvolutionEffectPath.TryLoadClass<UGameplayEffect>())
        {
            if (UGameplayEffect* EvolutionEffect = EvolutionEffectClass.GetDefaultObject())
            {
                FGameplayEffectSpec EvolutionSpec(EvolutionEffect, EffectContext, 1.0f);

            // Set stage-specific parameters
            float StageMultiplier = GetStageIntensityMultiplier(Stage);
            EvolutionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Evolution.StageMultiplier")), StageMultiplier);
            EvolutionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Evolution.LayerBonus")), GetLayerBonusMultiplier(Layer));

            // Apply permanent evolution effect
            EvolutionSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

            FActiveGameplayEffectHandle EvolutionEffectHandle = ActorASC->ApplyGameplayEffectSpecToSelf(EvolutionSpec);

            // Store evolution effect for potential removal
            ActorEvolutionEffects.Add(Actor, EvolutionEffectHandle);
            }
        }
    }
}

void UAuracronDynamicRealmSubsystem::ApplyStageEnvironmentalChanges(AActor* Actor, EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage)
{
    if (!Actor)
    {
        return;
    }

    // Apply environmental changes based on evolution stage using UE 5.6 environmental system
    float StageIntensity = GetStageIntensityMultiplier(Stage);

    // Apply material changes to mesh components
    TArray<UMeshComponent*> MeshComponents;
    Actor->GetComponents<UMeshComponent>(MeshComponents);

    for (UMeshComponent* MeshComp : MeshComponents)
    {
        if (!MeshComp)
        {
            continue;
        }

        for (int32 MaterialIndex = 0; MaterialIndex < MeshComp->GetNumMaterials(); MaterialIndex++)
        {
            if (UMaterialInterface* OriginalMaterial = MeshComp->GetMaterial(MaterialIndex))
            {
                // Create or update dynamic material
                UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(OriginalMaterial, Actor);

                // Apply layer and stage-specific material effects
                switch (Layer)
                {
                    case EAuracronRealmLayer::Terrestrial:
                        DynamicMaterial->SetScalarParameterValue(TEXT("RadiantEvolution"), StageIntensity);
                        DynamicMaterial->SetVectorParameterValue(TEXT("EvolutionGlow"), FLinearColor(1.0f, 0.8f, 0.4f, StageIntensity));
                        DynamicMaterial->SetScalarParameterValue(TEXT("CrystallineGrowth"), StageIntensity * 0.8f);
                        break;

                    case EAuracronRealmLayer::Celestial:
                        DynamicMaterial->SetScalarParameterValue(TEXT("CelestialEvolution"), StageIntensity);
                        DynamicMaterial->SetVectorParameterValue(TEXT("EvolutionGlow"), FLinearColor(0.6f, 0.8f, 1.0f, StageIntensity));
                        DynamicMaterial->SetScalarParameterValue(TEXT("EtherealTransparency"), StageIntensity * 0.3f);
                        break;

                    case EAuracronRealmLayer::Abyssal:
                        DynamicMaterial->SetScalarParameterValue(TEXT("UmbralEvolution"), StageIntensity);
                        DynamicMaterial->SetVectorParameterValue(TEXT("EvolutionGlow"), FLinearColor(0.3f, 0.0f, 0.6f, StageIntensity));
                        DynamicMaterial->SetScalarParameterValue(TEXT("VoidCorruption"), StageIntensity * 1.2f);
                        break;

                    default:
                        break;
                }

                MeshComp->SetMaterial(MaterialIndex, DynamicMaterial);
            }
        }
    }

    // Apply lighting changes based on evolution stage
    TArray<ULightComponent*> LightComponents;
    Actor->GetComponents<ULightComponent>(LightComponents);

    for (ULightComponent* LightComp : LightComponents)
    {
        if (LightComp)
        {
            float BaseIntensity = LightComp->Intensity;
            float EvolutionIntensity = BaseIntensity * (1.0f + StageIntensity * 0.5f);
            LightComp->SetIntensity(EvolutionIntensity);

            // Apply layer-specific light colors
            switch (Layer)
            {
                case EAuracronRealmLayer::Terrestrial:
                    LightComp->SetLightColor(FLinearColor(1.0f, 0.9f, 0.6f));
                    break;
                case EAuracronRealmLayer::Celestial:
                    LightComp->SetLightColor(FLinearColor(0.7f, 0.9f, 1.0f));
                    break;
                case EAuracronRealmLayer::Abyssal:
                    LightComp->SetLightColor(FLinearColor(0.4f, 0.2f, 0.8f));
                    break;
                default:
                    break;
            }
        }
    }

    // Apply physics changes based on evolution
    if (UPrimitiveComponent* PrimComp = Actor->FindComponentByClass<UPrimitiveComponent>())
    {
        // Adjust physics properties based on layer evolution
        switch (Layer)
        {
            case EAuracronRealmLayer::Celestial:
                // Reduce mass for celestial evolution
                if (PrimComp->IsSimulatingPhysics())
                {
                    float MassReduction = 1.0f - (StageIntensity * 0.3f);
                    PrimComp->SetMassScale(NAME_None, MassReduction);
                }
                break;

            case EAuracronRealmLayer::Abyssal:
                // Increase mass for abyssal evolution
                if (PrimComp->IsSimulatingPhysics())
                {
                    float MassIncrease = 1.0f + (StageIntensity * 0.4f);
                    PrimComp->SetMassScale(NAME_None, MassIncrease);
                }
                break;

            default:
                break;
        }
    }
}

void UAuracronDynamicRealmSubsystem::ApplyGlobalLayerEvolutionEffects(EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage)
{
    if (!GetWorld())
    {
        return;
    }

    // Apply global effects to the entire layer using UE 5.6 world modification
    float StageIntensity = GetStageIntensityMultiplier(Stage);

    // Find realm manager for the layer
    for (TActorIterator<AAuracronRealmManager> ActorItr(GetWorld()); ActorItr; ++ActorItr)
    {
        AAuracronRealmManager* RealmManager = *ActorItr;
        if (RealmManager && RealmManager->ManagedLayer == Layer)
        {
            // Apply global atmospheric changes
            ApplyGlobalAtmosphericChanges(RealmManager, Stage, StageIntensity);

            // Apply global lighting changes
            ApplyGlobalLightingChanges(RealmManager, Layer, Stage, StageIntensity);

            // Apply global audio changes
            ApplyGlobalAudioChanges(RealmManager, Layer, Stage, StageIntensity);

            break;
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Global evolution effects applied to %s at stage %s"),
        *UEnum::GetValueAsString(Layer), *UEnum::GetValueAsString(Stage));
}

void UAuracronDynamicRealmSubsystem::ApplyGlobalAtmosphericChanges(AAuracronRealmManager* RealmManager, EAuracronLayerEvolutionStage Stage, float Intensity)
{
    if (!RealmManager)
    {
        return;
    }

    // Apply global atmospheric changes using UE 5.6 atmospheric system
    if (UStaticMeshComponent* AtmosphereComp = RealmManager->FindComponentByClass<UStaticMeshComponent>())
    {
        // Create atmospheric material based on evolution stage
        FString AtmosphereMaterialPath = FString::Printf(TEXT("/Game/Materials/Atmosphere/M_Atmosphere_%s.M_Atmosphere_%s"),
            *UEnum::GetValueAsString(Stage), *UEnum::GetValueAsString(Stage));

        FSoftObjectPath MaterialPath(AtmosphereMaterialPath);
        if (UMaterialInterface* AtmosphereMaterial = Cast<UMaterialInterface>(MaterialPath.TryLoad()))
        {
            UMaterialInstanceDynamic* DynamicAtmosphere = UMaterialInstanceDynamic::Create(AtmosphereMaterial, RealmManager);
            DynamicAtmosphere->SetScalarParameterValue(TEXT("EvolutionIntensity"), Intensity);
            DynamicAtmosphere->SetScalarParameterValue(TEXT("AtmosphericDensity"), 0.5f + (Intensity * 0.3f));

            AtmosphereComp->SetMaterial(0, DynamicAtmosphere);
        }
    }

    // Spawn global atmospheric VFX
    FString GlobalVFXPath = FString::Printf(TEXT("/Game/VFX/Atmosphere/Global/NS_GlobalAtmosphere_%s.NS_GlobalAtmosphere_%s"),
        *UEnum::GetValueAsString(Stage), *UEnum::GetValueAsString(Stage));

    FSoftObjectPath GlobalVFXSoftPath(GlobalVFXPath);
    if (UNiagaraSystem* GlobalVFX = Cast<UNiagaraSystem>(GlobalVFXSoftPath.TryLoad()))
    {
        UNiagaraComponent* GlobalVFXComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            GlobalVFX,
            RealmManager->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepWorldPosition,
            false
        );

        if (GlobalVFXComponent)
        {
            GlobalVFXComponent->SetVariableFloat(FName("GlobalIntensity"), Intensity);
            GlobalVFXComponent->SetVariableFloat(FName("AtmosphereScale"), 10000.0f); // Large scale for global effect
        }
    }
}

void UAuracronDynamicRealmSubsystem::ApplyGlobalLightingChanges(AAuracronRealmManager* RealmManager, EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage, float Intensity)
{
    if (!RealmManager || !GetWorld())
    {
        return;
    }

    // Apply global lighting changes using UE 5.6 lighting system

    // Find or create directional light for the layer
    ADirectionalLight* LayerDirectionalLight = nullptr;

    for (TActorIterator<ADirectionalLight> LightItr(GetWorld()); LightItr; ++LightItr)
    {
        ADirectionalLight* DirectionalLight = *LightItr;
        if (DirectionalLight && DirectionalLight->GetName().Contains(UEnum::GetValueAsString(Layer)))
        {
            LayerDirectionalLight = DirectionalLight;
            break;
        }
    }

    // Create directional light if not found
    if (!LayerDirectionalLight)
    {
        FActorSpawnParameters SpawnParams;
        SpawnParams.Name = FName(*FString::Printf(TEXT("DirectionalLight_%s"), *UEnum::GetValueAsString(Layer)));

        LayerDirectionalLight = GetWorld()->SpawnActor<ADirectionalLight>(SpawnParams);
        if (LayerDirectionalLight)
        {
            LayerDirectionalLight->SetActorLocation(RealmManager->GetActorLocation() + FVector(0.0f, 0.0f, 2000.0f));
        }
    }

    if (LayerDirectionalLight)
    {
        UDirectionalLightComponent* LightComp = Cast<UDirectionalLightComponent>(LayerDirectionalLight->GetLightComponent());
        if (LightComp)
        {
            // Apply layer and stage-specific lighting
            switch (Layer)
            {
                case EAuracronRealmLayer::Terrestrial:
                    {
                        FLinearColor RadiantColor = FLinearColor(1.0f, 0.9f, 0.6f) * (0.8f + Intensity * 0.4f);
                        LightComp->SetLightColor(RadiantColor);
                        LightComp->SetIntensity(3.0f + Intensity * 2.0f);

                        // Set sun angle for terrestrial
                        FRotator SunRotation = FRotator(-45.0f + (Intensity * 15.0f), 45.0f, 0.0f);
                        LayerDirectionalLight->SetActorRotation(SunRotation);
                    }
                    break;

                case EAuracronRealmLayer::Celestial:
                    {
                        FLinearColor CelestialColor = FLinearColor(0.7f, 0.9f, 1.0f) * (1.0f + Intensity * 0.5f);
                        LightComp->SetLightColor(CelestialColor);
                        LightComp->SetIntensity(4.0f + Intensity * 3.0f);

                        // Set ethereal angle for celestial
                        FRotator EtherealRotation = FRotator(-30.0f + (Intensity * 20.0f), 90.0f, 0.0f);
                        LayerDirectionalLight->SetActorRotation(EtherealRotation);
                    }
                    break;

                case EAuracronRealmLayer::Abyssal:
                    {
                        FLinearColor UmbralColor = FLinearColor(0.3f, 0.1f, 0.6f) * (0.5f + Intensity * 0.3f);
                        LightComp->SetLightColor(UmbralColor);
                        LightComp->SetIntensity(1.0f + Intensity * 1.5f);

                        // Set ominous angle for abyssal
                        FRotator UmbralRotation = FRotator(-60.0f - (Intensity * 10.0f), 135.0f, 0.0f);
                        LayerDirectionalLight->SetActorRotation(UmbralRotation);
                    }
                    break;

                default:
                    break;
            }

            // Apply evolution stage-specific lighting effects
            switch (Stage)
            {
                case EAuracronLayerEvolutionStage::Awakening:
                    LightComp->SetLightFunctionMaterial(LoadObject<UMaterialInterface>(nullptr, TEXT("/Game/Materials/Lighting/M_AwakeningLight")));
                    break;
                case EAuracronLayerEvolutionStage::Active:
                    LightComp->SetLightFunctionMaterial(LoadObject<UMaterialInterface>(nullptr, TEXT("/Game/Materials/Lighting/M_ActiveLight")));
                    break;
                case EAuracronLayerEvolutionStage::Resonant:
                    LightComp->SetLightFunctionMaterial(LoadObject<UMaterialInterface>(nullptr, TEXT("/Game/Materials/Lighting/M_ResonantLight")));
                    break;
                case EAuracronLayerEvolutionStage::Transcendent:
                    LightComp->SetLightFunctionMaterial(LoadObject<UMaterialInterface>(nullptr, TEXT("/Game/Materials/Lighting/M_TranscendentLight")));
                    break;
                default:
                    break;
            }
        }
    }
}

void UAuracronDynamicRealmSubsystem::ApplyGlobalAudioChanges(AAuracronRealmManager* RealmManager, EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage, float Intensity)
{
    if (!RealmManager)
    {
        return;
    }

    // Apply global audio changes using UE 5.6 MetaSound system
    FString GlobalAudioPath = FString::Printf(TEXT("/Game/Audio/Atmosphere/%s/MS_%sAtmosphere_%s.MS_%sAtmosphere_%s"),
        *UEnum::GetValueAsString(Layer),
        *UEnum::GetValueAsString(Layer),
        *UEnum::GetValueAsString(Stage),
        *UEnum::GetValueAsString(Layer),
        *UEnum::GetValueAsString(Stage));

    FSoftObjectPath GlobalAudioSoftPath(GlobalAudioPath);
    if (UMetaSoundSource* GlobalAudio = Cast<UMetaSoundSource>(GlobalAudioSoftPath.TryLoad()))
    {
        // Find or create audio component for global atmosphere
        UAudioComponent* GlobalAudioComponent = RealmManager->FindComponentByClass<UAudioComponent>();

        if (!GlobalAudioComponent)
        {
            GlobalAudioComponent = NewObject<UAudioComponent>(RealmManager);
            GlobalAudioComponent->SetupAttachment(RealmManager->GetRootComponent());
            GlobalAudioComponent->RegisterComponent();
        }

        // Set global audio properties
        GlobalAudioComponent->SetSound(Cast<USoundBase>(GlobalAudio));
        GlobalAudioComponent->SetVolumeMultiplier(0.6f + (Intensity * 0.4f));
        GlobalAudioComponent->SetPitchMultiplier(1.0f + (Intensity * 0.2f));

        // Set MetaSound parameters based on layer and stage
        GlobalAudioComponent->SetFloatParameter(TEXT("EvolutionIntensity"), Intensity);
        GlobalAudioComponent->SetFloatParameter(TEXT("StageLevel"), static_cast<float>(Stage));
        GlobalAudioComponent->SetFloatParameter(TEXT("LayerDepth"), GetLayerDepthValue(Layer));

        // Layer-specific audio parameters
        switch (Layer)
        {
            case EAuracronRealmLayer::Terrestrial:
                GlobalAudioComponent->SetFloatParameter(TEXT("RadiantHarmony"), Intensity);
                GlobalAudioComponent->SetFloatParameter(TEXT("CrystalResonance"), Intensity * 0.8f);
                break;

            case EAuracronRealmLayer::Celestial:
                GlobalAudioComponent->SetFloatParameter(TEXT("CelestialChoir"), Intensity);
                GlobalAudioComponent->SetFloatParameter(TEXT("WindHarmony"), Intensity * 1.2f);
                break;

            case EAuracronRealmLayer::Abyssal:
                GlobalAudioComponent->SetFloatParameter(TEXT("VoidWhispers"), Intensity);
                GlobalAudioComponent->SetFloatParameter(TEXT("UmbralDrone"), Intensity * 1.5f);
                break;

            default:
                break;
        }

        // Start playing if not already playing
        if (!GlobalAudioComponent->IsPlaying())
        {
            GlobalAudioComponent->Play();
        }
    }
}

float UAuracronDynamicRealmSubsystem::GetStageIntensityMultiplier(EAuracronLayerEvolutionStage Stage) const
{
    // Return intensity multiplier based on evolution stage
    switch (Stage)
    {
        case EAuracronLayerEvolutionStage::Dormant:
            return 0.0f;
        case EAuracronLayerEvolutionStage::Awakening:
            return 0.3f;
        case EAuracronLayerEvolutionStage::Active:
            return 0.6f;
        case EAuracronLayerEvolutionStage::Resonant:
            return 0.9f;
        case EAuracronLayerEvolutionStage::Transcendent:
            return 1.5f; // Transcendent stage is more powerful
        default:
            return 0.0f;
    }
}

float UAuracronDynamicRealmSubsystem::GetLayerBonusMultiplier(EAuracronRealmLayer Layer) const
{
    // Return layer-specific bonus multiplier
    switch (Layer)
    {
        case EAuracronRealmLayer::Terrestrial:
            return 1.0f; // Balanced baseline
        case EAuracronRealmLayer::Celestial:
            return 1.2f; // Celestial bonus
        case EAuracronRealmLayer::Abyssal:
            return 1.4f; // Abyssal power bonus (risk vs reward)
        default:
            return 1.0f;
    }
}

float UAuracronDynamicRealmSubsystem::CalculateBaseEvolutionRate(EAuracronRealmLayer Layer) const
{
    // Calculate base evolution rate for each layer
    switch (Layer)
    {
        case EAuracronRealmLayer::Terrestrial:
            return 1.0f; // Standard evolution rate
        case EAuracronRealmLayer::Celestial:
            return 0.8f; // Slower evolution (more stable)
        case EAuracronRealmLayer::Abyssal:
            return 1.3f; // Faster evolution (more chaotic)
        default:
            return 1.0f;
    }
}

int32 UAuracronDynamicRealmSubsystem::CountLayerSpecificObjects(EAuracronRealmLayer Layer, const FString& ObjectType) const
{
    // Count specific object types in a layer for energy calculation
    TArray<AActor*> LayerActors = GetActorsInLayer(Layer);
    int32 Count = 0;

    for (AActor* Actor : LayerActors)
    {
        if (Actor && Actor->GetName().Contains(ObjectType))
        {
            Count++;
        }
    }

    return Count;
}

float UAuracronDynamicRealmSubsystem::GetActorLayerEntryTime(AActor* Actor) const
{
    // Get the time when actor entered current layer
    if (const float* EntryTime = ActorLayerEntryTimes.Find(Actor))
    {
        return *EntryTime;
    }

    // Default to current time if not tracked
    return GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
}

void UAuracronDynamicRealmSubsystem::CheckGlobalEvolutionEvents()
{
    if (!GetWorld())
    {
        return;
    }

    // Check for global evolution events that affect multiple layers
    bool bAllLayersActive = true;
    bool bAnyLayerTranscendent = false;
    float TotalEvolutionProgress = 0.0f;

    for (const auto& EvolutionPair : LayerEvolutionData)
    {
        const FAuracronRealmLayerEvolutionData& EvolutionData = EvolutionPair.Value;

        if (EvolutionData.EvolutionStage == EAuracronLayerEvolutionStage::Dormant)
        {
            bAllLayersActive = false;
        }

        if (EvolutionData.EvolutionStage == EAuracronLayerEvolutionStage::Transcendent)
        {
            bAnyLayerTranscendent = true;
        }

        TotalEvolutionProgress += EvolutionData.EvolutionProgress;
    }

    // Trigger global events based on evolution state
    if (bAllLayersActive && !bGlobalEvolutionEventTriggered)
    {
        TriggerGlobalEvolutionEvent(EAuracronGlobalEvolutionEvent::AllLayersAwakened);
        bGlobalEvolutionEventTriggered = true;
    }

    if (bAnyLayerTranscendent && !bTranscendentEventTriggered)
    {
        TriggerGlobalEvolutionEvent(EAuracronGlobalEvolutionEvent::TranscendentReached);
        bTranscendentEventTriggered = true;
    }

    if (TotalEvolutionProgress >= 300.0f && !bMaxEvolutionEventTriggered) // All layers at max
    {
        TriggerGlobalEvolutionEvent(EAuracronGlobalEvolutionEvent::MaximumEvolution);
        bMaxEvolutionEventTriggered = true;
    }
}

void UAuracronDynamicRealmSubsystem::TriggerGlobalEvolutionEvent(EAuracronGlobalEvolutionEvent EventType)
{
    // Trigger global evolution events using UE 5.6 event system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Global evolution event triggered: %s"), *UEnum::GetValueAsString(EventType));

    switch (EventType)
    {
        case EAuracronGlobalEvolutionEvent::AllLayersAwakened:
            {
                // Spawn global awakening effects
                SpawnGlobalEvolutionVFX(TEXT("/Game/VFX/Global/NS_GlobalAwakening.NS_GlobalAwakening"));
                PlayGlobalEvolutionAudio(TEXT("/Game/Audio/Global/MS_GlobalAwakening.MS_GlobalAwakening"));

                // Apply global awakening bonuses to all players
                ApplyGlobalEvolutionBonuses(1.2f, TEXT("Global.Awakening"));
            }
            break;

        case EAuracronGlobalEvolutionEvent::TranscendentReached:
            {
                // Spawn transcendent effects
                SpawnGlobalEvolutionVFX(TEXT("/Game/VFX/Global/NS_TranscendentAscension.NS_TranscendentAscension"));
                PlayGlobalEvolutionAudio(TEXT("/Game/Audio/Global/MS_TranscendentAscension.MS_TranscendentAscension"));

                // Apply transcendent bonuses
                ApplyGlobalEvolutionBonuses(1.5f, TEXT("Global.Transcendent"));
            }
            break;

        case EAuracronGlobalEvolutionEvent::MaximumEvolution:
            {
                // Spawn maximum evolution effects
                SpawnGlobalEvolutionVFX(TEXT("/Game/VFX/Global/NS_MaximumEvolution.NS_MaximumEvolution"));
                PlayGlobalEvolutionAudio(TEXT("/Game/Audio/Global/MS_MaximumEvolution.MS_MaximumEvolution"));

                // Apply maximum evolution bonuses
                ApplyGlobalEvolutionBonuses(2.0f, TEXT("Global.Maximum"));

                // Unlock special content
                UnlockTranscendentContent();
            }
            break;

        default:
            break;
    }

    // Broadcast global evolution event
    OnGlobalEvolutionEvent.Broadcast(EventType);
}

void UAuracronDynamicRealmSubsystem::SpawnGlobalEvolutionVFX(const FString& VFXPath)
{
    if (!GetWorld())
    {
        return;
    }

    // Spawn global evolution VFX using UE 5.6 Niagara system
    FSoftObjectPath GlobalVFXPath(VFXPath);
    if (UNiagaraSystem* GlobalVFX = Cast<UNiagaraSystem>(GlobalVFXPath.TryLoad()))
    {
        // Spawn at world center with massive scale
        FVector WorldCenter = FVector::ZeroVector;

        UNiagaraComponent* GlobalVFXComponent = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            GlobalVFX,
            WorldCenter,
            FRotator::ZeroRotator,
            FVector(10.0f, 10.0f, 10.0f), // Large scale for global effect
            true // Auto-destroy when complete
        );

        if (GlobalVFXComponent)
        {
            GlobalVFXComponent->SetVariableFloat(FName("GlobalScale"), 20000.0f);
            GlobalVFXComponent->SetVariableFloat(FName("EvolutionIntensity"), 2.0f);
            GlobalVFXComponent->SetVariableLinearColor(FName("GlobalColor"), FLinearColor(1.0f, 1.0f, 1.0f, 0.8f));
        }
    }
}

void UAuracronDynamicRealmSubsystem::PlayGlobalEvolutionAudio(const FString& AudioPath)
{
    if (!GetWorld())
    {
        return;
    }

    // Play global evolution audio using UE 5.6 MetaSound
    FSoftObjectPath GlobalAudioPath(AudioPath);
    if (UMetaSoundSource* GlobalAudio = Cast<UMetaSoundSource>(GlobalAudioPath.TryLoad()))
    {
        UAudioComponent* GlobalAudioComponent = UGameplayStatics::SpawnSound2D(
            GetWorld(),
            GlobalAudio,
            1.0f, // Volume
            1.0f, // Pitch
            0.0f, // Start time
            nullptr, // Concurrency settings
            false, // Persist across level transitions
            true   // Auto-destroy when complete
        );

        if (GlobalAudioComponent)
        {
            GlobalAudioComponent->SetFloatParameter(TEXT("GlobalIntensity"), 1.5f);
            GlobalAudioComponent->SetFloatParameter(TEXT("EvolutionMagnitude"), 2.0f);
        }
    }
}

void UAuracronDynamicRealmSubsystem::ApplyGlobalEvolutionBonuses(float BonusMultiplier, const FString& BonusTag)
{
    if (!GetWorld())
    {
        return;
    }

    // Apply global evolution bonuses to all players using UE 5.6 GameplayEffect system
    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (PC && PC->GetPawn())
        {
            if (UAbilitySystemComponent* PlayerASC = PC->GetPawn()->FindComponentByClass<UAbilitySystemComponent>())
            {
                FGameplayEffectContextHandle EffectContext = PlayerASC->MakeEffectContext();
                EffectContext.AddSourceObject(this);

                // Apply global evolution bonus effect
                static const FSoftClassPath GlobalBonusPath(TEXT("/Game/GameplayEffects/Global/GE_GlobalEvolutionBonus.GE_GlobalEvolutionBonus_C"));
                if (TSubclassOf<UGameplayEffect> GlobalBonusEffectClass = GlobalBonusPath.TryLoadClass<UGameplayEffect>())
                {
                    if (UGameplayEffect* GlobalBonusEffect = GlobalBonusEffectClass.GetDefaultObject())
                    {
                        FGameplayEffectSpec BonusSpec(GlobalBonusEffect, EffectContext, 1.0f);

                    // Set bonus parameters
                    BonusSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(*BonusTag), BonusMultiplier);
                    BonusSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Global.PowerBonus")), BonusMultiplier * 0.2f);
                    BonusSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Global.SpeedBonus")), BonusMultiplier * 0.15f);
                    BonusSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Global.RegenBonus")), BonusMultiplier * 0.25f);

                    // Apply permanent global bonus
                    BonusSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

                    FActiveGameplayEffectHandle BonusHandle = PlayerASC->ApplyGameplayEffectSpecToSelf(BonusSpec);

                    // Store global bonus handle
                    GlobalEvolutionBonusHandles.Add(PC->GetPawn(), BonusHandle);
                    }
                }
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Global evolution bonuses applied - Multiplier: %.2fx, Tag: %s"),
        BonusMultiplier, *BonusTag);
}

void UAuracronDynamicRealmSubsystem::UnlockTranscendentContent()
{
    if (!GetWorld())
    {
        return;
    }

    // Unlock transcendent content using UE 5.6 content unlocking system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Unlocking transcendent content..."));

    // Spawn transcendent realm portals
    for (int32 i = 0; i < 3; i++)
    {
        FVector PortalLocation = FVector(
            FMath::RandRange(-2000.0f, 2000.0f),
            FMath::RandRange(-2000.0f, 2000.0f),
            FMath::RandRange(3000.0f, 5000.0f) // High altitude for transcendent portals
        );

        FActorSpawnParameters SpawnParams;
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

        static const FSoftClassPath TranscendentPortalPath(TEXT("/Game/Environment/Transcendent/BP_TranscendentPortal.BP_TranscendentPortal_C"));
        if (UClass* PortalClass = TranscendentPortalPath.TryLoadClass<AActor>())
        {
            if (AActor* Portal = GetWorld()->SpawnActor<AActor>(PortalClass, PortalLocation, FRotator::ZeroRotator, SpawnParams))
            {
                // Register portal to special transcendent layer
                RegisterActorToLayer(Portal, EAuracronRealmLayer::Celestial); // Use celestial as base

                // Add transcendent portal VFX
                static const FSoftObjectPath TranscendentVFXPath(TEXT("/Game/VFX/Transcendent/NS_TranscendentPortal.NS_TranscendentPortal"));
                if (UNiagaraSystem* TranscendentVFX = Cast<UNiagaraSystem>(TranscendentVFXPath.TryLoad()))
                {
                    UNiagaraComponent* PortalVFXComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                        TranscendentVFX,
                        Portal->GetRootComponent(),
                        NAME_None,
                        FVector::ZeroVector,
                        FRotator::ZeroRotator,
                        EAttachLocation::KeepRelativeOffset,
                        false
                    );

                    if (PortalVFXComponent)
                    {
                        PortalVFXComponent->SetVariableFloat(FName("TranscendentPower"), 3.0f);
                        PortalVFXComponent->SetVariableLinearColor(FName("TranscendentColor"), FLinearColor(1.0f, 1.0f, 1.0f, 1.0f));
                        PortalVFXComponent->SetVariableFloat(FName("PortalRadius"), 1000.0f);
                    }
                }

                UE_LOG(LogTemp, Log, TEXT("AURACRON: Transcendent portal spawned at %s"), *PortalLocation.ToString());
            }
        }
    }

    // Unlock transcendent abilities for all players
    UnlockTranscendentAbilities();

    // Broadcast transcendent content unlocked event
    OnTranscendentContentUnlocked.Broadcast();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Transcendent content unlocked successfully"));
}

void UAuracronDynamicRealmSubsystem::UnlockTranscendentAbilities()
{
    if (!GetWorld())
    {
        return;
    }

    // Unlock transcendent abilities for all players using UE 5.6 ability system
    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (PC && PC->GetPawn())
        {
            if (UAbilitySystemComponent* PlayerASC = PC->GetPawn()->FindComponentByClass<UAbilitySystemComponent>())
            {
                // Grant transcendent abilities
                TArray<FString> TranscendentAbilityPaths = {
                    TEXT("/Game/GameplayAbilities/Transcendent/GA_TranscendentFlight.GA_TranscendentFlight_C"),
                    TEXT("/Game/GameplayAbilities/Transcendent/GA_RealmWalk.GA_RealmWalk_C"),
                    TEXT("/Game/GameplayAbilities/Transcendent/GA_LayerMastery.GA_LayerMastery_C"),
                    TEXT("/Game/GameplayAbilities/Transcendent/GA_EvolutionControl.GA_EvolutionControl_C")
                };

                for (const FString& AbilityPath : TranscendentAbilityPaths)
                {
                    FSoftClassPath TranscendentAbilityPath(AbilityPath);
                    if (TSubclassOf<UGameplayAbility> TranscendentAbility = TranscendentAbilityPath.TryLoadClass<UGameplayAbility>())
                    {
                        FGameplayAbilitySpec TranscendentSpec(TranscendentAbility, 1, INDEX_NONE, this);
                        TranscendentSpec.GetDynamicSpecSourceTags().AddTag(FGameplayTag::RequestGameplayTag(TEXT("Ability.Transcendent")));

                        FGameplayAbilitySpecHandle SpecHandle = PlayerASC->GiveAbility(TranscendentSpec);

                        // Store transcendent ability handle
                        TranscendentAbilityHandles.Add(PC->GetPawn(), SpecHandle);
                    }
                }

                UE_LOG(LogTemp, Log, TEXT("AURACRON: Transcendent abilities unlocked for player: %s"),
                    *PC->GetPawn()->GetName());
            }
        }
    }
}

// === Additional Evolution System Methods ===

void UAuracronDynamicRealmSubsystem::UpdateLayerStability(EAuracronRealmLayer Layer, FAuracronRealmLayerEvolutionData& EvolutionData)
{
    // Update layer stability based on various factors using UE 5.6 analytics
    float BaseStability = 1.0f;

    // Factor in player activity
    float ActivityFactor = this->CalculateLayerActivityFactor(Layer);
    float ActivityStabilityImpact = FMath::Clamp(ActivityFactor * 0.1f, -0.3f, 0.2f);

    // Factor in evolution stage
    float StageStabilityModifier = 0.0f;
    switch (EvolutionData.EvolutionStage)
    {
        case EAuracronLayerEvolutionStage::Dormant:
            StageStabilityModifier = 0.0f;
            break;
        case EAuracronLayerEvolutionStage::Awakening:
            StageStabilityModifier = -0.1f; // Slightly unstable during awakening
            break;
        case EAuracronLayerEvolutionStage::Active:
            StageStabilityModifier = 0.1f; // More stable when active
            break;
        case EAuracronLayerEvolutionStage::Resonant:
            StageStabilityModifier = 0.2f; // Very stable when resonant
            break;
        case EAuracronLayerEvolutionStage::Transcendent:
            StageStabilityModifier = 0.3f; // Maximum stability
            break;
        default:
            break;
    }

    // Factor in energy levels
    float EnergyStabilityImpact = FMath::Clamp((EvolutionData.EnergyAccumulation - 50.0f) * 0.01f, -0.2f, 0.2f);

    // Calculate new stability
    float NewStability = BaseStability + ActivityStabilityImpact + StageStabilityModifier + EnergyStabilityImpact;
    EvolutionData.StabilityLevel = FMath::Clamp(NewStability, 0.1f, 2.0f);

    // Apply stability effects to layer
    if (EvolutionData.StabilityLevel < 0.5f)
    {
        // Low stability - apply instability effects
        ApplyInstabilityEffects(Layer, 1.0f - EvolutionData.StabilityLevel);
    }
    else if (EvolutionData.StabilityLevel > 1.5f)
    {
        // High stability - apply stability bonuses
        ApplyStabilityBonuses(Layer, EvolutionData.StabilityLevel - 1.0f);
    }
}

void UAuracronDynamicRealmSubsystem::ApplyInstabilityEffects(EAuracronRealmLayer Layer, float InstabilityLevel)
{
    // Apply instability effects to layer using UE 5.6 effect system
    TArray<AActor*> LayerActors = GetActorsInLayer(Layer);

    for (AActor* Actor : LayerActors)
    {
        if (!Actor)
        {
            continue;
        }

        // Apply visual instability effects
        TArray<UNiagaraComponent*> NiagaraComponents;
        Actor->GetComponents<UNiagaraComponent>(NiagaraComponents);

        for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
        {
            if (NiagaraComp)
            {
                // Add instability particle effects
                NiagaraComp->SetVariableFloat(FName("InstabilityLevel"), InstabilityLevel);
                NiagaraComp->SetVariableLinearColor(FName("InstabilityColor"), FLinearColor(1.0f, 0.3f, 0.0f, InstabilityLevel));
            }
        }

        // Apply physics instability
        if (UPrimitiveComponent* PrimComp = Actor->FindComponentByClass<UPrimitiveComponent>())
        {
            if (PrimComp->IsSimulatingPhysics())
            {
                // Add random forces for instability
                FVector RandomForce = FVector(
                    FMath::RandRange(-1000.0f, 1000.0f),
                    FMath::RandRange(-1000.0f, 1000.0f),
                    FMath::RandRange(-500.0f, 500.0f)
                ) * InstabilityLevel;

                PrimComp->AddImpulse(RandomForce);
            }
        }
    }
}

void UAuracronDynamicRealmSubsystem::ApplyStabilityBonuses(EAuracronRealmLayer Layer, float StabilityBonus)
{
    // Apply stability bonuses to layer using UE 5.6 bonus system
    TArray<AActor*> LayerActors = GetActorsInLayer(Layer);

    for (AActor* Actor : LayerActors)
    {
        if (!Actor)
        {
            continue;
        }

        // Apply stability visual effects
        static const FSoftObjectPath StabilityVFXPath(TEXT("/Game/VFX/Environment/NS_StabilityAura.NS_StabilityAura"));
        if (UNiagaraSystem* StabilityVFX = Cast<UNiagaraSystem>(StabilityVFXPath.TryLoad()))
        {
            UNiagaraComponent* StabilityComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                StabilityVFX,
                Actor->GetRootComponent(),
                NAME_None,
                FVector(0.0f, 0.0f, 30.0f),
                FRotator::ZeroRotator,
                EAttachLocation::KeepRelativeOffset,
                false
            );

            if (StabilityComponent)
            {
                StabilityComponent->SetVariableFloat(FName("StabilityLevel"), StabilityBonus);
                StabilityComponent->SetVariableLinearColor(FName("StabilityColor"), FLinearColor(0.0f, 1.0f, 0.3f, StabilityBonus * 0.5f));
            }
        }

        // Apply gameplay stability bonuses
        if (UAbilitySystemComponent* ActorASC = Actor->FindComponentByClass<UAbilitySystemComponent>())
        {
            FGameplayEffectContextHandle EffectContext = ActorASC->MakeEffectContext();
            EffectContext.AddSourceObject(this);

            static const FSoftClassPath StabilityBonusPath(TEXT("/Game/GameplayEffects/Environment/GE_StabilityBonus.GE_StabilityBonus_C"));
            if (TSubclassOf<UGameplayEffect> StabilityBonusEffect = StabilityBonusPath.TryLoadClass<UGameplayEffect>())
            {
                FGameplayEffectSpec StabilitySpec(StabilityBonusEffect.GetDefaultObject(), EffectContext, 1.0f);
                StabilitySpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Environment.Stability.Bonus")), StabilityBonus);
                StabilitySpec.SetDuration(60.0f, false); // 1 minute duration

                ActorASC->ApplyGameplayEffectSpecToSelf(StabilitySpec);
            }
        }
    }
}

// === Public API Methods for Advanced Evolution System ===

FAuracronRealmLayerEvolutionData UAuracronDynamicRealmSubsystem::GetLayerEvolutionData(EAuracronRealmLayer Layer) const
{
    if (const FAuracronRealmLayerEvolutionData* EvolutionData = LayerEvolutionData.Find(Layer))
    {
        return *EvolutionData;
    }

    // Return default data if not found
    FAuracronRealmLayerEvolutionData DefaultData;
    DefaultData.Layer = Layer;
    return DefaultData;
}

void UAuracronDynamicRealmSubsystem::SetLayerEvolutionRate(EAuracronRealmLayer Layer, float EvolutionRate)
{
    if (FAuracronRealmLayerEvolutionData* EvolutionData = LayerEvolutionData.Find(Layer))
    {
        EvolutionData->EvolutionRate = FMath::Clamp(EvolutionRate, 0.1f, 5.0f);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Evolution rate for %s set to %.2f"),
            *UEnum::GetValueAsString(Layer), EvolutionRate);
    }
}

void UAuracronDynamicRealmSubsystem::ForceLayerEvolution(EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage TargetStage)
{
    if (FAuracronRealmLayerEvolutionData* EvolutionData = LayerEvolutionData.Find(Layer))
    {
        EAuracronLayerEvolutionStage OldStage = EvolutionData->EvolutionStage;
        EvolutionData->EvolutionStage = TargetStage;
        EvolutionData->EvolutionProgress = GetRequiredProgressForStage(TargetStage);
        EvolutionData->LastEvolutionTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

        // Apply forced evolution effects
        ApplyEvolutionStageEffects(Layer, TargetStage);

        // Broadcast evolution event
        OnLayerEvolutionStageChanged.Broadcast(Layer, OldStage, TargetStage);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Forced evolution - Layer %s evolved to %s"),
            *UEnum::GetValueAsString(Layer), *UEnum::GetValueAsString(TargetStage));
    }
}

void UAuracronDynamicRealmSubsystem::CancelAdvancedTransition(const FGuid& TransitionID)
{
    // Find and cancel the specific advanced transition
    FAuracronAdvancedRealmTransition* FoundTransition = ActiveAdvancedTransitions.FindByPredicate(
        [TransitionID](const FAuracronAdvancedRealmTransition& Transition)
        {
            return Transition.TransitionID == TransitionID;
        }
    );

    if (FoundTransition && FoundTransition->Actor.IsValid())
    {
        AActor* Actor = FoundTransition->Actor.Get();

        // Restore original properties
        Actor->SetActorLocation(FoundTransition->OriginalLocation);
        Actor->SetActorRotation(FoundTransition->OriginalRotation);
        Actor->SetActorScale3D(FoundTransition->OriginalScale);

        // Remove transition effects
        if (UAbilitySystemComponent* ActorASC = Actor->FindComponentByClass<UAbilitySystemComponent>())
        {
            if (FActiveGameplayEffectHandle* EffectHandle = TransitionEffectHandles.Find(TransitionID))
            {
                ActorASC->RemoveActiveGameplayEffect(*EffectHandle);
            }
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Advanced transition cancelled for actor: %s"), *Actor->GetName());
    }

    // Cleanup transition
    CleanupAdvancedTransition(TransitionID);
}

void UAuracronDynamicRealmSubsystem::ApplyEvolutionEffectsToLayer(EAuracronRealmLayer Layer, const FAuracronRealmLayerEvolutionData& EvolutionData)
{
    // Apply evolution effects to entire layer based on evolution data
    TArray<AActor*> LayerActors = this->GetActorsInLayer(Layer);

    for (AActor* Actor : LayerActors)
    {
        if (!Actor)
        {
            continue;
        }

        // Apply evolution-based material effects
        TArray<UMeshComponent*> MeshComponents;
        Actor->GetComponents<UMeshComponent>(MeshComponents);

        for (UMeshComponent* MeshComp : MeshComponents)
        {
            if (MeshComp)
            {
                for (int32 MaterialIndex = 0; MaterialIndex < MeshComp->GetNumMaterials(); MaterialIndex++)
                {
                    if (UMaterialInterface* OriginalMaterial = MeshComp->GetMaterial(MaterialIndex))
                    {
                        UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(OriginalMaterial, Actor);

                        // Apply evolution parameters
                        DynamicMaterial->SetScalarParameterValue(TEXT("EvolutionProgress"), EvolutionData.EvolutionProgress * 0.01f);
                        DynamicMaterial->SetScalarParameterValue(TEXT("StabilityLevel"), EvolutionData.StabilityLevel);
                        DynamicMaterial->SetScalarParameterValue(TEXT("EnergyLevel"), EvolutionData.EnergyAccumulation * 0.01f);

                        MeshComp->SetMaterial(MaterialIndex, DynamicMaterial);
                    }
                }
            }
        }

        // Apply evolution-based lighting effects
        TArray<ULightComponent*> LightComponents;
        Actor->GetComponents<ULightComponent>(LightComponents);

        for (ULightComponent* LightComp : LightComponents)
        {
            if (LightComp)
            {
                float EvolutionIntensityMultiplier = 1.0f + (EvolutionData.EvolutionProgress * 0.005f);
                float BaseIntensity = LightComp->Intensity;
                LightComp->SetIntensity(BaseIntensity * EvolutionIntensityMultiplier);
            }
        }
    }
}

// === Performance Monitoring and Optimization ===

void UAuracronDynamicRealmSubsystem::MonitorAdvancedPerformance()
{
    if (!GetWorld())
    {
        return;
    }

    // Monitor performance of advanced evolution system using UE 5.6 profiling
    float CurrentTime = GetWorld()->GetTimeSeconds();

    // Count active systems
    int32 ActiveAdvancedTransitionsCount = ActiveAdvancedTransitions.Num();
    int32 ActiveVFXComponents = TransitionVFXComponents.Num();
    int32 ActiveAudioComponents = TransitionAudioComponents.Num();
    int32 ActiveEvolutionVFX = EvolutionVFXComponents.Num();

    // Calculate memory usage
    float EstimatedMemoryUsage =
        (ActiveAdvancedTransitionsCount * 0.5f) +           // 0.5MB per transition
        (ActiveVFXComponents * 2.0f) +         // 2MB per VFX component
        (ActiveAudioComponents * 1.0f) +       // 1MB per audio component
        (ActiveEvolutionVFX * 1.5f);           // 1.5MB per evolution VFX

    // Log performance metrics
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON Evolution Performance:"));
        UE_LOG(LogTemp, Log, TEXT("  Active Transitions: %d"), ActiveTransitions.Num());
        UE_LOG(LogTemp, Log, TEXT("  Active VFX: %d"), ActiveVFXComponents);
        UE_LOG(LogTemp, Log, TEXT("  Active Audio: %d"), ActiveAudioComponents);
        UE_LOG(LogTemp, Log, TEXT("  Evolution VFX: %d"), ActiveEvolutionVFX);
        UE_LOG(LogTemp, Log, TEXT("  Estimated Memory: %.1f MB"), EstimatedMemoryUsage);
    }

    // Apply performance optimizations if needed
    if (EstimatedMemoryUsage > 50.0f) // If using more than 50MB
    {
        OptimizeEvolutionPerformance();
    }
}

void UAuracronDynamicRealmSubsystem::OptimizeEvolutionPerformance()
{
    // Optimize evolution system performance using UE 5.6 optimization techniques
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Optimizing evolution system performance..."));

    // Cleanup completed VFX components
    TArray<EAuracronRealmLayer> CompletedVFXKeys;
    for (auto& VFXPair : EvolutionVFXComponents)
    {
        if (!IsValid(VFXPair.Value) || !VFXPair.Value->IsActive())
        {
            CompletedVFXKeys.Add(VFXPair.Key);
        }
    }

    for (const EAuracronRealmLayer& Key : CompletedVFXKeys)
    {
        if (UNiagaraComponent* VFXComp = EvolutionVFXComponents.FindRef(Key))
        {
            if (IsValid(VFXComp))
            {
                VFXComp->DestroyComponent();
            }
        }
        EvolutionVFXComponents.Remove(Key);
    }

    // Cleanup invalid actor references
    TArray<TObjectPtr<AActor>> InvalidActors;
    for (auto& ActorPair : ActorLayerEntryTimes)
    {
        if (!IsValid(ActorPair.Key))
        {
            InvalidActors.Add(ActorPair.Key);
        }
    }

    for (TObjectPtr<AActor> InvalidActor : InvalidActors)
    {
        ActorLayerEntryTimes.Remove(InvalidActor);
        ActorLayerEffects.Remove(InvalidActor);
        ActorEvolutionEffects.Remove(InvalidActor);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Evolution performance optimization completed"));
}



void UAuracronDynamicRealmSubsystem::RegisterRealmSigilBonuses(UAuracronSigilosBridge* SigilBridge)
{
    if (!SigilBridge)
    {
        return;
    }

    // Register realm-specific bonuses for sigil usage using UE 5.6 bonus system

    // Terrestrial layer bonuses
    FSigilRealmBonus TerrestrialBonus;
    TerrestrialBonus.RealmLayer = EAuracronRealmLayer::Terrestrial;
    TerrestrialBonus.AegisBonusMultiplier = 1.3f;  // Enhanced protection on solid ground
    TerrestrialBonus.RuinBonusMultiplier = 1.1f;   // Standard destruction
    TerrestrialBonus.VesperBonusMultiplier = 1.2f; // Enhanced support on stable ground
    TerrestrialBonus.FusionBonusMultiplier = 1.15f; // Balanced fusion

    // Celestial layer bonuses
    FSigilRealmBonus CelestialBonus;
    CelestialBonus.RealmLayer = EAuracronRealmLayer::Celestial;
    CelestialBonus.AegisBonusMultiplier = 1.1f;   // Reduced protection in air
    CelestialBonus.RuinBonusMultiplier = 1.4f;    // Enhanced destruction from height
    CelestialBonus.VesperBonusMultiplier = 1.5f;  // Maximum support in celestial realm
    CelestialBonus.FusionBonusMultiplier = 1.3f;  // Enhanced fusion in celestial energy

    // Abyssal layer bonuses
    FSigilRealmBonus AbyssalBonus;
    AbyssalBonus.RealmLayer = EAuracronRealmLayer::Abyssal;
    AbyssalBonus.AegisBonusMultiplier = 1.2f;   // Enhanced protection against void
    AbyssalBonus.RuinBonusMultiplier = 1.6f;    // Maximum destruction in void energy
    AbyssalBonus.VesperBonusMultiplier = 0.9f;  // Reduced support in dark realm
    AbyssalBonus.FusionBonusMultiplier = 1.4f;  // Powerful but unstable fusion

    // Convert and register bonuses with sigil system
    // TODO: Make RegisterRealmBonus public in UAuracronSigilosBridge
    /*
    FAuracronRealmBonus TerrestrialRealmBonus;
    TerrestrialRealmBonus.BonusName = TEXT("Terrestrial Layer Bonus");
    TerrestrialRealmBonus.BonusMultiplier = TerrestrialBonus.AegisBonusMultiplier;
    SigilBridge->RegisterRealmBonus(TerrestrialRealmBonus);

    FAuracronRealmBonus CelestialRealmBonus;
    CelestialRealmBonus.BonusName = TEXT("Celestial Layer Bonus");
    CelestialRealmBonus.BonusMultiplier = CelestialBonus.VesperBonusMultiplier;
    SigilBridge->RegisterRealmBonus(CelestialRealmBonus);

    FAuracronRealmBonus AbyssalRealmBonus;
    AbyssalRealmBonus.BonusName = TEXT("Abyssal Layer Bonus");
    AbyssalRealmBonus.BonusMultiplier = AbyssalBonus.RuinBonusMultiplier;
    SigilBridge->RegisterRealmBonus(AbyssalRealmBonus);
    */

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Realm-specific sigil bonuses registered"));
}

void UAuracronDynamicRealmSubsystem::SetupLayerSigilEffects(UAuracronSigilosBridge* SigilBridge)
{
    if (!SigilBridge)
    {
        return;
    }

    // Setup layer-specific visual effects for sigil usage using UE 5.6 VFX system

    // Register layer-specific VFX overrides
    TMap<EAuracronRealmLayer, UNiagaraSystem*> LayerVFXOverrides;

    // Load VFX assets
    UNiagaraSystem* TerrestrialVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Sigils/Terrestrial/NS_TerrestrialLayer.NS_TerrestrialLayer"));
    UNiagaraSystem* CelestialVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Sigils/Celestial/NS_CelestialLayer.NS_CelestialLayer"));
    UNiagaraSystem* AbyssalVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Sigils/Abyssal/NS_AbyssalLayer.NS_AbyssalLayer"));

    if (TerrestrialVFX) LayerVFXOverrides.Add(EAuracronRealmLayer::Terrestrial, TerrestrialVFX);
    if (CelestialVFX) LayerVFXOverrides.Add(EAuracronRealmLayer::Celestial, CelestialVFX);
    if (AbyssalVFX) LayerVFXOverrides.Add(EAuracronRealmLayer::Abyssal, AbyssalVFX);

    for (const auto& VFXPair : LayerVFXOverrides)
    {
        SigilBridge->RegisterLayerVFXOverride(UEnum::GetValueAsString(VFXPair.Key), VFXPair.Value);
    }

    // Register layer-specific audio overrides
    TMap<EAuracronRealmLayer, USoundBase*> LayerAudioOverrides;

    // Load Audio assets
    USoundBase* TerrestrialAudio = LoadObject<USoundBase>(nullptr, TEXT("/Game/Audio/Sigils/Terrestrial/SFX_TerrestrialLayer.SFX_TerrestrialLayer"));
    USoundBase* CelestialAudio = LoadObject<USoundBase>(nullptr, TEXT("/Game/Audio/Sigils/Celestial/SFX_CelestialLayer.SFX_CelestialLayer"));
    USoundBase* AbyssalAudio = LoadObject<USoundBase>(nullptr, TEXT("/Game/Audio/Sigils/Abyssal/SFX_AbyssalLayer.SFX_AbyssalLayer"));

    if (TerrestrialAudio) LayerAudioOverrides.Add(EAuracronRealmLayer::Terrestrial, TerrestrialAudio);
    if (CelestialAudio) LayerAudioOverrides.Add(EAuracronRealmLayer::Celestial, CelestialAudio);
    if (AbyssalAudio) LayerAudioOverrides.Add(EAuracronRealmLayer::Abyssal, AbyssalAudio);

    for (const auto& AudioPair : LayerAudioOverrides)
    {
        SigilBridge->RegisterLayerAudioOverride(UEnum::GetValueAsString(AudioPair.Key), AudioPair.Value);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Layer-specific sigil effects configured"));
}

void UAuracronDynamicRealmSubsystem::CreateSigilRealmSynergies(UAuracronSigilosBridge* SigilBridge)
{
    if (!SigilBridge)
    {
        return;
    }

    // Create synergies between sigil usage and realm evolution using UE 5.6 synergy system

    // Setup evolution acceleration through sigil usage
    SigilBridge->OnSigilActivated.AddDynamic(this, &UAuracronDynamicRealmSubsystem::OnSigilActivatedInRealm);
    SigilBridge->OnFusion20Activated.AddDynamic(this, &UAuracronDynamicRealmSubsystem::OnFusion20ActivatedInRealm);

    // Setup realm bonuses for sigil combinations
    SetupRealmSigilCombinationBonuses(SigilBridge);

    // Setup transcendent sigil unlocks
    SetupTranscendentSigilUnlocks(SigilBridge);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sigil-Realm synergies created"));
}

void UAuracronDynamicRealmSubsystem::OnSigilActivatedInRealm(EAuracronSigiloType SigilType, float PowerLevel)
{
    // Sigil activation affects realm evolution

    // Accelerate layer evolution when sigils are used
    EAuracronRealmLayer UserLayer = EAuracronRealmLayer::Terrestrial; // Default layer

    if (FAuracronRealmLayerEvolutionData* EvolutionData = LayerEvolutionData.Find(UserLayer))
    {
        // Calculate evolution boost based on sigil type and power
        float EvolutionBoost = 0.0f;

        switch (SigilType)
        {
            case EAuracronSigiloType::Aegis:
                EvolutionBoost = PowerLevel * 0.5f; // Moderate evolution boost
                break;
            case EAuracronSigiloType::Ruin:
                EvolutionBoost = PowerLevel * 0.8f; // High evolution boost
                break;
            case EAuracronSigiloType::Vesper:
                EvolutionBoost = PowerLevel * 0.3f; // Lower but stable evolution boost
                break;
            default:
                break;
        }

        // Apply layer-specific multipliers
        switch (UserLayer)
        {
            case EAuracronRealmLayer::Terrestrial:
                EvolutionBoost *= 1.0f; // Standard rate
                break;
            case EAuracronRealmLayer::Celestial:
                EvolutionBoost *= 1.2f; // Enhanced in celestial
                break;
            case EAuracronRealmLayer::Abyssal:
                EvolutionBoost *= 1.5f; // Maximum boost in abyssal
                break;
            default:
                break;
        }

        // Apply evolution boost
        EvolutionData->EvolutionProgress += EvolutionBoost;
        EvolutionData->EnergyAccumulation += EvolutionBoost * 2.0f;

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sigil %s boosted %s layer evolution by %.2f"),
            *UEnum::GetValueAsString(SigilType), *UEnum::GetValueAsString(UserLayer), EvolutionBoost);
    }
}

void UAuracronDynamicRealmSubsystem::OnFusion20ActivatedInRealm(const FAuracronSigilArchetype& SigilArchetype, float PowerLevel)
{
    // Massive evolution boost when Fusion 2.0 is activated using UE 5.6 fusion system
    EAuracronRealmLayer UserLayer = EAuracronRealmLayer::Terrestrial; // Default layer

    if (FAuracronRealmLayerEvolutionData* EvolutionData = LayerEvolutionData.Find(UserLayer))
    {
        // Calculate massive evolution boost for Fusion 2.0
        float FusionEvolutionBoost = PowerLevel * 2.0f; // Base fusion boost

        // Bonus based on archetype complexity
        float SigilCountBonus = 3.0f * 0.5f; // Fusion 2.0 uses 3 sigils
        FusionEvolutionBoost += SigilCountBonus;

        // Layer-specific fusion multipliers
        switch (UserLayer)
        {
            case EAuracronRealmLayer::Terrestrial:
                FusionEvolutionBoost *= 1.2f; // Enhanced on stable ground
                break;
            case EAuracronRealmLayer::Celestial:
                FusionEvolutionBoost *= 1.5f; // Maximum boost in celestial energy
                break;
            case EAuracronRealmLayer::Abyssal:
                FusionEvolutionBoost *= 1.8f; // Extreme boost in void energy
                break;
            default:
                break;
        }

        // Apply fusion evolution boost
        EvolutionData->EvolutionProgress += FusionEvolutionBoost;
        EvolutionData->EnergyAccumulation += FusionEvolutionBoost * 3.0f;

        // Trigger special fusion-realm effects
        TArray<EAuracronSigiloType> DefaultFusedSigils = {EAuracronSigiloType::Aegis, EAuracronSigiloType::Ruin, EAuracronSigiloType::Vesper};
        TriggerFusionRealmEffects(nullptr, UserLayer, DefaultFusedSigils, PowerLevel);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Fusion 2.0 massively boosted %s layer evolution by %.2f"),
            *UEnum::GetValueAsString(UserLayer), FusionEvolutionBoost);
    }
}

void UAuracronDynamicRealmSubsystem::TriggerFusionRealmEffects(AActor* User, EAuracronRealmLayer Layer, const TArray<EAuracronSigiloType>& FusedSigils, float PowerLevel)
{
    if (!User)
    {
        return;
    }

    // Trigger special effects when Fusion 2.0 is used in specific realms using UE 5.6 effect system

    // Spawn fusion-realm VFX
    FString FusionVFXPath = FString::Printf(TEXT("/Game/VFX/Fusion/Realm/NS_Fusion20_%s.NS_Fusion20_%s"),
        *UEnum::GetValueAsString(Layer), *UEnum::GetValueAsString(Layer));

    FSoftObjectPath FusionVFXSoftPath(FusionVFXPath);
    if (UNiagaraSystem* FusionVFX = Cast<UNiagaraSystem>(FusionVFXSoftPath.TryLoad()))
    {
        UNiagaraComponent* FusionVFXComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            FusionVFX,
            User->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            true // Auto-destroy when complete
        );

        if (FusionVFXComponent)
        {
            FusionVFXComponent->SetVariableFloat(FName("FusionPower"), PowerLevel);
            FusionVFXComponent->SetVariableFloat(FName("SigilCount"), static_cast<float>(FusedSigils.Num()));
            FusionVFXComponent->SetVariableFloat(FName("RealmResonance"), GetLayerResonanceValue(Layer));

            // Set layer-specific fusion colors
            FLinearColor FusionColor;
            switch (Layer)
            {
                case EAuracronRealmLayer::Terrestrial:
                    FusionColor = FLinearColor(1.0f, 0.8f, 0.4f, 1.0f); // Golden fusion
                    break;
                case EAuracronRealmLayer::Celestial:
                    FusionColor = FLinearColor(0.7f, 0.9f, 1.0f, 1.0f); // Celestial fusion
                    break;
                case EAuracronRealmLayer::Abyssal:
                    FusionColor = FLinearColor(0.4f, 0.0f, 0.8f, 1.0f); // Void fusion
                    break;
                default:
                    FusionColor = FLinearColor::White;
                    break;
            }

            FusionVFXComponent->SetVariableLinearColor(FName("FusionColor"), FusionColor);
        }
    }

    // Apply fusion-realm gameplay effects
    if (UAbilitySystemComponent* UserASC = User->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = UserASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        FString FusionEffectPath = FString::Printf(TEXT("/Game/GameplayEffects/Fusion/Realm/GE_Fusion20_%s.GE_Fusion20_%s_C"),
            *UEnum::GetValueAsString(Layer), *UEnum::GetValueAsString(Layer));

        FSoftClassPath FusionEffectSoftPath(FusionEffectPath);
        if (TSubclassOf<UGameplayEffect> FusionEffect = FusionEffectSoftPath.TryLoadClass<UGameplayEffect>())
        {
            UGameplayEffect* FusionEffectCDO = FusionEffect.GetDefaultObject();
            FGameplayEffectSpec FusionSpec(FusionEffectCDO, EffectContext, 1.0f);

            // Set fusion parameters
            FusionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Fusion.Power")), PowerLevel);
            FusionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Fusion.SigilCount")), 2.0f); // Default fusion count
            FusionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Fusion.RealmBonus")), GetLayerBonusMultiplier(Layer));

            // Apply fusion-realm effect
            FusionSpec.SetDuration(30.0f, false); // 30 second duration

            UserASC->ApplyGameplayEffectSpecToSelf(FusionSpec);
        }
    }

    // Trigger realm-wide effects for powerful fusion
    if (PowerLevel >= 8.0f) // High power fusion
    {
        TriggerRealmWideFusionEffects(Layer, PowerLevel);
    }
}

void UAuracronDynamicRealmSubsystem::TriggerRealmWideFusionEffects(EAuracronRealmLayer Layer, float PowerLevel)
{
    // Trigger realm-wide effects for powerful fusion activations
    TArray<AActor*> LayerActors = GetActorsInLayer(Layer);

    // Apply realm-wide visual effects
    for (AActor* Actor : LayerActors)
    {
        if (!Actor)
        {
            continue;
        }

        // Spawn realm-wide fusion resonance VFX
        static const FSoftObjectPath RealmResonanceVFXPath(TEXT("/Game/VFX/Fusion/NS_RealmResonance.NS_RealmResonance"));
        if (UNiagaraSystem* ResonanceVFX = Cast<UNiagaraSystem>(RealmResonanceVFXPath.TryLoad()))
        {
            UNiagaraComponent* ResonanceComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                ResonanceVFX,
                Actor->GetRootComponent(),
                NAME_None,
                FVector(0.0f, 0.0f, 100.0f),
                FRotator::ZeroRotator,
                EAttachLocation::KeepRelativeOffset,
                true // Auto-destroy
            );

            if (ResonanceComponent)
            {
                ResonanceComponent->SetVariableFloat(FName("ResonancePower"), PowerLevel * 0.5f);
                ResonanceComponent->SetVariableFloat(FName("RealmDepth"), GetLayerDepthValue(Layer));
            }
        }
    }

    // Accelerate layer evolution significantly
    if (FAuracronRealmLayerEvolutionData* EvolutionData = LayerEvolutionData.Find(Layer))
    {
        float MassiveEvolutionBoost = PowerLevel * 5.0f; // Massive boost for realm-wide effects
        EvolutionData->EvolutionProgress += MassiveEvolutionBoost;
        EvolutionData->EnergyAccumulation += MassiveEvolutionBoost * 4.0f;

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Realm-wide fusion effects triggered - Evolution boost: %.2f"), MassiveEvolutionBoost);
    }
}

float UAuracronDynamicRealmSubsystem::GetLayerResonanceValue(EAuracronRealmLayer Layer) const
{
    // Return resonance value for layer-specific effects
    if (const FAuracronRealmLayerEvolutionData* EvolutionData = LayerEvolutionData.Find(Layer))
    {
        return EvolutionData->EvolutionProgress * EvolutionData->EnvironmentalFactors * 0.01f;
    }

    return 1.0f; // Default resonance
}

// === Advanced Dynamic Rail Management System Implementation ===

void UAuracronDynamicRealmSubsystem::InitializeAdvancedRailSystem()
{
    if (!GetWorld())
    {
        return;
    }

    // Initialize advanced rail management using UE 5.6 rail system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Advanced Dynamic Rail System..."));

    bAdvancedRailSystemActive = true;
    RailSystemStartTime = GetWorld()->GetTimeSeconds();
    LastRailSystemUpdate = RailSystemStartTime;

    // Initialize rail generation parameters
    RailGenerationConfig.MaxRailsPerLayer = 8;
    RailGenerationConfig.MinRailLength = 1000.0f;
    RailGenerationConfig.MaxRailLength = 5000.0f;
    RailGenerationConfig.RailSpacing = 800.0f;
    RailGenerationConfig.bAutoGenerateRails = true;
    RailGenerationConfig.bAdaptiveGeneration = true;

    // Generate initial rail network for each layer
    GenerateLayerRailNetwork(EAuracronRealmLayer::Terrestrial);
    GenerateLayerRailNetwork(EAuracronRealmLayer::Celestial);
    GenerateLayerRailNetwork(EAuracronRealmLayer::Abyssal);

    // Start rail system update timer
    GetWorld()->GetTimerManager().SetTimer(
        RailSystemUpdateTimer,
        [this]()
        {
            UpdateAdvancedRailSystem();
        },
        2.0f, // Update every 2 seconds
        true  // Looping
    );

    // Start rail optimization timer
    GetWorld()->GetTimerManager().SetTimer(
        RailOptimizationTimer,
        [this]()
        {
            OptimizeRailSystem();
        },
        10.0f, // Optimize every 10 seconds
        true   // Looping
    );

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Advanced Dynamic Rail System initialized"));
}

void UAuracronDynamicRealmSubsystem::GenerateLayerRailNetwork(EAuracronRealmLayer Layer)
{
    if (!GetWorld())
    {
        return;
    }

    // Generate rail network for specific layer using UE 5.6 procedural generation
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Generating rail network for %s layer"), *UEnum::GetValueAsString(Layer));

    // Determine rail types for layer
    TArray<EAuracronRailType> LayerRailTypes = GetRailTypesForLayer(Layer);

    // Generate rails for each type
    for (EAuracronRailType RailType : LayerRailTypes)
    {
        int32 RailsToGenerate = CalculateRailCountForType(Layer, RailType);

        for (int32 i = 0; i < RailsToGenerate; i++)
        {
            GenerateSingleRail(Layer, RailType, i);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Rail network generated for %s - Total rails: %d"),
        *UEnum::GetValueAsString(Layer), GetActiveRailsInLayer(Layer).Num());
}

TArray<EAuracronRailType> UAuracronDynamicRealmSubsystem::GetRailTypesForLayer(EAuracronRealmLayer Layer) const
{
    // Determine appropriate rail types for each layer
    TArray<EAuracronRailType> RailTypes;

    switch (Layer)
    {
        case EAuracronRealmLayer::Terrestrial:
            // Terrestrial supports all rail types
            RailTypes.Add(EAuracronRailType::Solar);
            RailTypes.Add(EAuracronRailType::Axis);
            RailTypes.Add(EAuracronRailType::Lunar);
            break;

        case EAuracronRealmLayer::Celestial:
            // Celestial favors Solar and Axis
            RailTypes.Add(EAuracronRailType::Solar);
            RailTypes.Add(EAuracronRailType::Axis);
            break;

        case EAuracronRealmLayer::Abyssal:
            // Abyssal favors Lunar and Axis
            RailTypes.Add(EAuracronRailType::Lunar);
            RailTypes.Add(EAuracronRailType::Axis);
            break;

        default:
            break;
    }

    return RailTypes;
}

int32 UAuracronDynamicRealmSubsystem::CalculateRailCountForType(EAuracronRealmLayer Layer, EAuracronRailType RailType) const
{
    // Calculate number of rails to generate for each type
    int32 BaseCount = 2; // Base number of rails per type

    // Layer-specific modifiers
    switch (Layer)
    {
        case EAuracronRealmLayer::Terrestrial:
            BaseCount = 3; // More rails in main layer
            break;
        case EAuracronRealmLayer::Celestial:
            BaseCount = 2; // Standard count
            break;
        case EAuracronRealmLayer::Abyssal:
            BaseCount = 2; // Standard count
            break;
        default:
            break;
    }

    // Rail type-specific modifiers
    switch (RailType)
    {
        case EAuracronRailType::Solar:
            return BaseCount; // Standard count
        case EAuracronRailType::Axis:
            return BaseCount - 1; // Fewer axis rails (more powerful)
        case EAuracronRailType::Lunar:
            return BaseCount; // Standard count
        default:
            return BaseCount;
    }
}

// === Advanced Dynamic Rail Management System ===
// Implementation moved to avoid duplication

// GenerateLayerRailNetwork implementation moved to avoid duplication

// GetRailTypesForLayer implementation moved to avoid duplication

// CalculateRailCountForType implementation moved to avoid duplication

void UAuracronDynamicRealmSubsystem::GenerateSingleRail(EAuracronRealmLayer Layer, EAuracronRailType RailType, int32 RailIndex)
{
    if (!GetWorld())
    {
        return;
    }

    // Generate single rail using UE 5.6 advanced spawning
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bNoFail = true;
    SpawnParams.Name = FName(*FString::Printf(TEXT("%s_%s_Rail_%d"),
        *UEnum::GetValueAsString(Layer), *UEnum::GetValueAsString(RailType), RailIndex));

    // Calculate rail spawn location based on layer
    FVector RailLocation = CalculateRailSpawnLocation(Layer, RailType, RailIndex);

    // Spawn rail actor
    if (AAuracronDynamicRail* NewRail = GetWorld()->SpawnActor<AAuracronDynamicRail>(RailLocation, FRotator::ZeroRotator, SpawnParams))
    {
        // Configure rail properties using public properties
        NewRail->RailType = RailType;
        NewRail->bAutoActivate = true;

        // Generate rail path based on layer and type
        TArray<FVector> RailPath = GenerateRailPath(Layer, RailType, RailLocation, RailIndex);
        NewRail->SetRailPath(RailPath);

        // Register rail to layer
        RegisterActorToLayer(NewRail, Layer);

        // Add to active rails
        ActiveRails.Add(NewRail);

        // Store rail in layer-specific collection
        if (!LayerRailMap.Contains(Layer))
        {
            LayerRailMap.Add(Layer, TArray<TObjectPtr<AAuracronDynamicRail>>());
        }
        LayerRailMap[Layer].Add(NewRail);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Generated %s rail in %s layer at %s"),
            *UEnum::GetValueAsString(RailType), *UEnum::GetValueAsString(Layer), *RailLocation.ToString());
    }
}

FVector UAuracronDynamicRealmSubsystem::CalculateRailSpawnLocation(EAuracronRealmLayer Layer, EAuracronRailType RailType, int32 RailIndex) const
{
    // Calculate spawn location based on layer characteristics using UE 5.6 positioning system
    FVector BaseLocation = FVector::ZeroVector;

    // Layer-specific base positioning
    switch (Layer)
    {
        case EAuracronRealmLayer::Terrestrial:
            BaseLocation = FVector(0.0f, 0.0f, 100.0f); // Ground level
            break;
        case EAuracronRealmLayer::Celestial:
            BaseLocation = FVector(0.0f, 0.0f, 2500.0f); // High altitude
            break;
        case EAuracronRealmLayer::Abyssal:
            BaseLocation = FVector(0.0f, 0.0f, -800.0f); // Underground
            break;
        default:
            break;
    }

    // Add rail type-specific positioning
    switch (RailType)
    {
        case EAuracronRailType::Solar:
            // Solar rails prefer open areas with good sun exposure
            BaseLocation += FVector(
                FMath::RandRange(-4000.0f, 4000.0f),
                FMath::RandRange(-4000.0f, 4000.0f),
                FMath::RandRange(0.0f, 200.0f)
            );
            break;

        case EAuracronRailType::Axis:
            // Axis rails prefer central, vertical paths
            BaseLocation += FVector(
                FMath::RandRange(-2000.0f, 2000.0f),
                FMath::RandRange(-2000.0f, 2000.0f),
                0.0f // Maintain layer height
            );
            break;

        case EAuracronRailType::Lunar:
            // Lunar rails prefer hidden, ethereal locations
            BaseLocation += FVector(
                FMath::RandRange(-6000.0f, 6000.0f),
                FMath::RandRange(-6000.0f, 6000.0f),
                FMath::RandRange(-100.0f, 300.0f)
            );
            break;

        default:
            break;
    }

    // Add index-based spacing to avoid overlap
    float AngleOffset = (360.0f / 8.0f) * RailIndex; // Distribute around circle
    float RadiusOffset = 1500.0f + (RailIndex * 300.0f);

    FVector IndexOffset = FVector(
        FMath::Cos(FMath::DegreesToRadians(AngleOffset)) * RadiusOffset,
        FMath::Sin(FMath::DegreesToRadians(AngleOffset)) * RadiusOffset,
        0.0f
    );

    return BaseLocation + IndexOffset;
}

TArray<FVector> UAuracronDynamicRealmSubsystem::GenerateRailPath(EAuracronRealmLayer Layer, EAuracronRailType RailType, const FVector& StartLocation, int32 RailIndex) const
{
    // Generate rail path based on type and layer using UE 5.6 path generation
    TArray<FVector> PathPoints;

    // Determine path characteristics
    int32 PathSegments = CalculatePathSegments(RailType);
    float PathLength = CalculatePathLength(Layer, RailType);

    PathPoints.Reserve(PathSegments + 1);
    PathPoints.Add(StartLocation); // Start point

    // Generate path based on rail type
    switch (RailType)
    {
        case EAuracronRailType::Solar:
            GenerateSolarRailPath(PathPoints, StartLocation, PathLength, PathSegments);
            break;
        case EAuracronRailType::Axis:
            GenerateAxisRailPath(PathPoints, StartLocation, PathLength, PathSegments, Layer);
            break;
        case EAuracronRailType::Lunar:
            GenerateLunarRailPath(PathPoints, StartLocation, PathLength, PathSegments);
            break;
        default:
            break;
    }

    return PathPoints;
}

void UAuracronDynamicRealmSubsystem::GenerateSolarRailPath(TArray<FVector>& PathPoints, const FVector& StartLocation, float PathLength, int32 Segments) const
{
    // Generate solar rail path with gentle curves and sun-following behavior
    FVector CurrentLocation = StartLocation;
    FVector Direction = FVector(1.0f, 0.0f, 0.0f); // Start moving east (towards sun)

    float SegmentLength = PathLength / Segments;

    for (int32 i = 1; i <= Segments; i++)
    {
        // Add gentle curves that follow solar patterns
        float CurveAngle = FMath::Sin(i * 0.5f) * 30.0f; // Gentle sine wave
        FRotator CurveRotation = FRotator(0.0f, CurveAngle, 0.0f);
        Direction = CurveRotation.RotateVector(Direction);

        // Add slight elevation changes for natural feel
        float ElevationChange = FMath::Sin(i * 0.3f) * 100.0f;

        CurrentLocation += Direction * SegmentLength;
        CurrentLocation.Z += ElevationChange;

        PathPoints.Add(CurrentLocation);
    }
}

void UAuracronDynamicRealmSubsystem::GenerateAxisRailPath(TArray<FVector>& PathPoints, const FVector& StartLocation, float PathLength, int32 Segments, EAuracronRealmLayer Layer) const
{
    // Generate axis rail path with vertical connections between layers
    FVector CurrentLocation = StartLocation;

    float SegmentLength = PathLength / Segments;
    bool bIsVerticalRail = FMath::RandBool(); // 50% chance for vertical rail

    if (bIsVerticalRail)
    {
        // Create vertical rail connecting layers
        FVector TargetLayerLocation = GetTargetLayerLocation(Layer);
        FVector DirectionToTarget = (TargetLayerLocation - StartLocation).GetSafeNormal();

        for (int32 i = 1; i <= Segments; i++)
        {
            float Progress = static_cast<float>(i) / static_cast<float>(Segments);
            CurrentLocation = FMath::Lerp(StartLocation, TargetLayerLocation, Progress);

            // Add slight horizontal offset for stability
            FVector HorizontalOffset = FVector(
                FMath::Sin(Progress * PI) * 200.0f,
                FMath::Cos(Progress * PI) * 200.0f,
                0.0f
            );

            CurrentLocation += HorizontalOffset;
            PathPoints.Add(CurrentLocation);
        }
    }
    else
    {
        // Create horizontal axis rail
        FVector Direction = FVector(1.0f, 1.0f, 0.0f).GetSafeNormal();

        for (int32 i = 1; i <= Segments; i++)
        {
            CurrentLocation += Direction * SegmentLength;

            // Maintain precise axis alignment
            CurrentLocation.Z = StartLocation.Z; // Keep same height

            PathPoints.Add(CurrentLocation);
        }
    }
}

void UAuracronDynamicRealmSubsystem::GenerateLunarRailPath(TArray<FVector>& PathPoints, const FVector& StartLocation, float PathLength, int32 Segments) const
{
    // Generate lunar rail path with ethereal, flowing curves
    FVector CurrentLocation = StartLocation;
    FVector Direction = FVector(0.0f, 1.0f, 0.0f); // Start moving north

    float SegmentLength = PathLength / Segments;

    for (int32 i = 1; i <= Segments; i++)
    {
        // Add ethereal flowing curves
        float FlowAngle = FMath::Sin(i * 0.8f) * 45.0f; // More dramatic curves
        FRotator FlowRotation = FRotator(0.0f, FlowAngle, 0.0f);
        Direction = FlowRotation.RotateVector(Direction);

        // Add ethereal elevation changes
        float EtherealFloat = FMath::Sin(i * 0.6f) * 200.0f;

        CurrentLocation += Direction * SegmentLength;
        CurrentLocation.Z += EtherealFloat;

        PathPoints.Add(CurrentLocation);
    }
}

void UAuracronDynamicRealmSubsystem::UpdateAdvancedRailSystem()
{
    if (!bAdvancedRailSystemActive || !GetWorld())
    {
        return;
    }

    // Update advanced rail system using UE 5.6 update system
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float DeltaTime = CurrentTime - LastRailSystemUpdate;
    LastRailSystemUpdate = CurrentTime;

    // Update all active rails
    for (AAuracronDynamicRail* Rail : ActiveRails)
    {
        if (Rail && IsValid(Rail))
        {
            UpdateRailSystemMetrics(Rail, DeltaTime);
            UpdateRailAdaptiveBehavior(Rail, DeltaTime);
            UpdateRailPlayerInteractions(Rail);
        }
    }

    // Check for rail generation needs
    if (RailGenerationConfig.bAdaptiveGeneration)
    {
        CheckAdaptiveRailGeneration();
    }

    // Update rail network connectivity
    UpdateRailNetworkConnectivity();

    // Monitor rail system performance
    MonitorRailSystemPerformance();
}

void UAuracronDynamicRealmSubsystem::OptimizeRailSystem()
{
    if (!GetWorld())
    {
        return;
    }

    // Optimize rail system performance using UE 5.6 optimization techniques
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Optimizing rail system performance..."));

    // Remove invalid rails
    TArray<AAuracronDynamicRail*> ValidRails;
    for (AAuracronDynamicRail* Rail : ActiveRails)
    {
        if (IsValid(Rail))
        {
            ValidRails.Add(Rail);
        }
    }
    ActiveRails = ValidRails;

    // Optimize rail metrics
    TArray<AAuracronDynamicRail*> RailsToRemove;
    for (auto& MetricsPair : RailMetricsMap)
    {
        if (!IsValid(MetricsPair.Key))
        {
            RailsToRemove.Add(MetricsPair.Key);
        }
    }

    for (AAuracronDynamicRail* InvalidRail : RailsToRemove)
    {
        RailMetricsMap.Remove(InvalidRail);
    }

    // Clean up player data
    TArray<TObjectPtr<APawn>> InvalidPlayers;
    for (auto& ExperiencePair : PlayerRailExperienceMap)
    {
        if (!IsValid(ExperiencePair.Key))
        {
            InvalidPlayers.Add(ExperiencePair.Key);
        }
    }

    for (TObjectPtr<APawn> InvalidPlayer : InvalidPlayers)
    {
        PlayerRailExperienceMap.Remove(InvalidPlayer);
        PlayerRailBonuses.Remove(InvalidPlayer);
        PlayerRailMasteryEffects.Remove(InvalidPlayer);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Rail system optimization completed"));
}

void UAuracronDynamicRealmSubsystem::CheckAdaptiveRailGeneration()
{
    if (!GetWorld())
    {
        return;
    }

    // Check if adaptive rail generation is needed using UE 5.6 adaptive system
    for (int32 LayerInt = 0; LayerInt < static_cast<int32>(EAuracronRealmLayer::All); LayerInt++)
    {
        EAuracronRealmLayer Layer = static_cast<EAuracronRealmLayer>(LayerInt);

        if (!IsLayerActive(Layer))
        {
            continue;
        }

        TArray<AAuracronDynamicRail*> LayerRails = GetActiveRailsInLayer(Layer);
        int32 ActivePlayerCount = GetActivePlayersInLayer(Layer);

        // Check if more rails are needed based on player density
        float PlayerDensity = static_cast<float>(ActivePlayerCount) / FMath::Max(1, LayerRails.Num());

        if (PlayerDensity > 2.0f && LayerRails.Num() < RailGenerationConfig.MaxRailsPerLayer)
        {
            // Generate additional rail for high player density
            UE_LOG(LogTemp, Log, TEXT("AURACRON: High player density detected in %s - generating additional rail"),
                *UEnum::GetValueAsString(Layer));

            // Determine best rail type for current conditions
            EAuracronRailType OptimalRailType = DetermineOptimalRailType(Layer);
            GenerateSingleRail(Layer, OptimalRailType, LayerRails.Num());
        }
    }
}

EAuracronRailType UAuracronDynamicRealmSubsystem::DetermineOptimalRailType(EAuracronRealmLayer Layer) const
{
    // Determine optimal rail type based on current conditions
    bool bIsNight = IsCurrentlyNightTime();

    switch (Layer)
    {
        case EAuracronRealmLayer::Terrestrial:
            // Choose based on time of day
            if (bIsNight)
            {
                return EAuracronRailType::Lunar; // Lunar rails work best at night
            }
            else
            {
                return EAuracronRailType::Solar; // Solar rails work best during day
            }

        case EAuracronRealmLayer::Celestial:
            return EAuracronRailType::Solar; // Solar rails optimal for celestial

        case EAuracronRealmLayer::Abyssal:
            return EAuracronRailType::Lunar; // Lunar rails optimal for abyssal

        default:
            return EAuracronRailType::Axis; // Axis as fallback
    }
}

void UAuracronDynamicRealmSubsystem::UpdateRailNetworkConnectivity()
{
    // Update rail network connectivity using UE 5.6 network system
    for (int32 LayerInt = 0; LayerInt < static_cast<int32>(EAuracronRealmLayer::All); LayerInt++)
    {
        EAuracronRealmLayer Layer = static_cast<EAuracronRealmLayer>(LayerInt);
        TArray<AAuracronDynamicRail*> LayerRails = GetActiveRailsInLayer(Layer);

        // Check connectivity between rails in the same layer
        for (int32 i = 0; i < LayerRails.Num(); i++)
        {
            for (int32 j = i + 1; j < LayerRails.Num(); j++)
            {
                if (LayerRails[i] && LayerRails[j])
                {
                    CheckRailConnectivity(LayerRails[i], LayerRails[j]);
                }
            }
        }
    }
}

void UAuracronDynamicRealmSubsystem::CheckRailConnectivity(AAuracronDynamicRail* Rail1, AAuracronDynamicRail* Rail2)
{
    if (!Rail1 || !Rail2)
    {
        return;
    }

    // Check if rails should be connected based on proximity
    float Distance = FVector::Dist(Rail1->GetActorLocation(), Rail2->GetActorLocation());

    if (Distance < RailGenerationConfig.RailSpacing * 1.5f)
    {
        // Rails are close enough to potentially connect
        CreateRailConnection(Rail1, Rail2);
    }
}

void UAuracronDynamicRealmSubsystem::CreateRailConnection(AAuracronDynamicRail* Rail1, AAuracronDynamicRail* Rail2)
{
    if (!Rail1 || !Rail2 || !GetWorld())
    {
        return;
    }

    // Create connection between rails using UE 5.6 connection system
    FVector ConnectionPoint1 = Rail1->GetRailEndPosition();
    FVector ConnectionPoint2 = Rail2->GetRailStartPosition();

    // Spawn connection bridge
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    static const FSoftClassPath RailBridgePath(TEXT("/Game/Environment/Rails/BP_RailBridge.BP_RailBridge_C"));
    if (UClass* BridgeClass = RailBridgePath.TryLoadClass<AActor>())
    {
        FVector BridgeLocation = (ConnectionPoint1 + ConnectionPoint2) * 0.5f;

        if (AActor* Bridge = GetWorld()->SpawnActor<AActor>(BridgeClass, BridgeLocation, FRotator::ZeroRotator, SpawnParams))
        {
            // Configure bridge to connect the rails
            if (UStaticMeshComponent* BridgeMesh = Bridge->FindComponentByClass<UStaticMeshComponent>())
            {
                FVector BridgeDirection = (ConnectionPoint2 - ConnectionPoint1).GetSafeNormal();
                float BridgeLength = FVector::Dist(ConnectionPoint1, ConnectionPoint2);

                Bridge->SetActorRotation(BridgeDirection.Rotation());
                BridgeMesh->SetWorldScale3D(FVector(BridgeLength / 100.0f, 1.0f, 1.0f)); // Scale to fit distance
            }

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Rail connection created between %s and %s rails"),
                *UEnum::GetValueAsString(Rail1->GetRailType()), *UEnum::GetValueAsString(Rail2->GetRailType()));
        }
    }
}

void UAuracronDynamicRealmSubsystem::MonitorRailSystemPerformance()
{
    if (!GetWorld())
    {
        return;
    }

    // Monitor rail system performance using UE 5.6 performance monitoring
    int32 TotalActiveRails = ActiveRails.Num();
    int32 TotalPlayersOnRails = 0;
    float TotalMemoryUsage = 0.0f;

    for (AAuracronDynamicRail* Rail : ActiveRails)
    {
        if (Rail && IsValid(Rail))
        {
            TotalPlayersOnRails += Rail->GetPlayersOnRail().Num();

            // Estimate memory usage per rail
            TotalMemoryUsage += 2.5f; // ~2.5MB per active rail
        }
    }

    // Log performance metrics
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON Rail System Performance:"));
        UE_LOG(LogTemp, Log, TEXT("  Active Rails: %d"), TotalActiveRails);
        UE_LOG(LogTemp, Log, TEXT("  Players on Rails: %d"), TotalPlayersOnRails);
        UE_LOG(LogTemp, Log, TEXT("  Estimated Memory: %.1f MB"), TotalMemoryUsage);
        UE_LOG(LogTemp, Log, TEXT("  Rail Metrics Tracked: %d"), RailMetricsMap.Num());
    }

    // Apply performance optimizations if needed
    if (TotalMemoryUsage > 30.0f) // If using more than 30MB
    {
        OptimizeRailSystem();
    }
}

TArray<AAuracronDynamicRail*> UAuracronDynamicRealmSubsystem::GetActiveRailsInLayer(EAuracronRealmLayer Layer) const
{
    TArray<AAuracronDynamicRail*> LayerRails;

    if (const TArray<TObjectPtr<AAuracronDynamicRail>>* RailArray = LayerRailMap.Find(Layer))
    {
        for (TObjectPtr<AAuracronDynamicRail> Rail : *RailArray)
        {
            if (Rail && IsValid(Rail) && Rail->IsRailActive())
            {
                LayerRails.Add(Rail.Get());
            }
        }
    }

    return LayerRails;
}

int32 UAuracronDynamicRealmSubsystem::GetActivePlayersInLayer(EAuracronRealmLayer Layer) const
{
    if (!GetWorld())
    {
        return 0;
    }

    int32 PlayerCount = 0;

    // Count players in the specified layer
    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (PC && PC->GetPawn())
        {
            EAuracronRealmLayer PlayerLayer = GetActorLayer(PC->GetPawn());
            if (PlayerLayer == Layer)
            {
                PlayerCount++;
            }
        }
    }

    return PlayerCount;
}

bool UAuracronDynamicRealmSubsystem::IsCurrentlyNightTime() const
{
    if (!GetWorld())
    {
        return false;
    }

    // Determine if it's currently night time using UE 5.6 time system
    float GameTimeSeconds = GetWorld()->GetTimeSeconds();
    float GameHour = FMath::Fmod(GameTimeSeconds / 60.0f, 24.0f); // 1 minute = 1 hour

    return (GameHour < 6.0f || GameHour > 18.0f);
}

float UAuracronDynamicRealmSubsystem::GetRailTypeBonusMultiplier(EAuracronRailType RailType) const
{
    // Return bonus multiplier for rail type
    switch (RailType)
    {
        case EAuracronRailType::Solar:
            return IsCurrentlyNightTime() ? 0.8f : 1.3f; // Day bonus, night penalty
        case EAuracronRailType::Axis:
            return 1.1f; // Consistent bonus
        case EAuracronRailType::Lunar:
            return IsCurrentlyNightTime() ? 1.4f : 0.6f; // Night bonus, day penalty
        default:
            return 1.0f;
    }
}

void UAuracronDynamicRealmSubsystem::UpdateRailSystemMetrics(AAuracronDynamicRail* Rail, float DeltaTime)
{
    if (!Rail)
    {
        return;
    }

    // Update rail-specific metrics using UE 5.6 metrics system
    FRailSystemMetrics* Metrics = RailMetricsMap.Find(Rail);
    if (!Metrics)
    {
        FRailSystemMetrics NewMetrics;
        NewMetrics.Rail = Rail;
        NewMetrics.TotalUsageTime = 0.0f;
        NewMetrics.PlayerCount = 0;
        NewMetrics.EfficiencyRating = 1.0f;
        NewMetrics.LastUsageTime = 0.0f;

        RailMetricsMap.Add(Rail, NewMetrics);
        Metrics = &RailMetricsMap[Rail];
    }

    // Update metrics
    TArray<APawn*> PlayersOnRail = Rail->GetPlayersOnRail();
    Metrics->PlayerCount = PlayersOnRail.Num();

    if (Metrics->PlayerCount > 0)
    {
        Metrics->TotalUsageTime += DeltaTime;
        Metrics->LastUsageTime = GetWorld()->GetTimeSeconds();
    }

    // Calculate efficiency rating
    float TimeSinceLastUse = GetWorld()->GetTimeSeconds() - Metrics->LastUsageTime;
    float UsageFrequency = Metrics->TotalUsageTime / FMath::Max(1.0f, GetWorld()->GetTimeSeconds() - RailSystemStartTime);

    Metrics->EfficiencyRating = FMath::Clamp(UsageFrequency * 10.0f - (TimeSinceLastUse * 0.01f), 0.1f, 2.0f);
}

void UAuracronDynamicRealmSubsystem::UpdateRailAdaptiveBehavior(AAuracronDynamicRail* Rail, float DeltaTime)
{
    if (!Rail)
    {
        return;
    }

    // Update adaptive behavior based on usage patterns using UE 5.6 adaptive system
    FRailSystemMetrics* Metrics = RailMetricsMap.Find(Rail);
    if (!Metrics)
    {
        return;
    }

    // Adapt rail properties based on usage
    if (Metrics->EfficiencyRating > 1.5f)
    {
        // High usage - enhance rail performance
        EnhanceRailPerformance(Rail, Metrics->EfficiencyRating - 1.0f);
    }
    else if (Metrics->EfficiencyRating < 0.5f)
    {
        // Low usage - consider rail optimization or removal
        float TimeSinceLastUse = GetWorld()->GetTimeSeconds() - Metrics->LastUsageTime;
        if (TimeSinceLastUse > 300.0f) // 5 minutes without use
        {
            OptimizeUnusedRail(Rail);
        }
    }

    // Adapt to time of day for Solar and Lunar rails
    EAuracronRailType RailType = Rail->GetRailType();
    if (RailType == EAuracronRailType::Solar || RailType == EAuracronRailType::Lunar)
    {
        AdaptRailToTimeOfDay(Rail, RailType);
    }
}

void UAuracronDynamicRealmSubsystem::EnhanceRailPerformance(AAuracronDynamicRail* Rail, float EnhancementLevel)
{
    if (!Rail)
    {
        return;
    }

    // Enhance rail performance for high-usage rails using UE 5.6 enhancement system
    FRailMovementData CurrentMovementData = Rail->GetMovementData();

    // Increase movement speed
    float SpeedBonus = 1.0f + (EnhancementLevel * 0.3f);
    CurrentMovementData.MovementSpeed *= SpeedBonus;

    // Improve acceleration
    float AccelBonus = 1.0f + (EnhancementLevel * 0.2f);
    CurrentMovementData.Acceleration *= AccelBonus;

    // Reduce energy cost
    float EfficiencyBonus = 1.0f - (EnhancementLevel * 0.15f);
    CurrentMovementData.EnergyCostPerSecond *= FMath::Max(0.5f, EfficiencyBonus);

    // Apply enhanced movement data
    Rail->SetMovementData(CurrentMovementData);

    // Apply visual enhancement effects
    FRailVisualConfig VisualConfig = Rail->GetVisualConfig();
    VisualConfig.EffectIntensity *= (1.0f + EnhancementLevel * 0.4f);
    Rail->SetVisualConfig(VisualConfig);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Enhanced rail performance - Type: %s, Enhancement: %.2fx"),
        *UEnum::GetValueAsString(Rail->GetRailType()), SpeedBonus);
}

void UAuracronDynamicRealmSubsystem::OptimizeUnusedRail(AAuracronDynamicRail* Rail)
{
    if (!Rail)
    {
        return;
    }

    // Optimize unused rail to save resources using UE 5.6 optimization system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Optimizing unused rail - Type: %s"), *UEnum::GetValueAsString(Rail->GetRailType()));

    // Reduce visual effects for unused rails
    FRailVisualConfig VisualConfig = Rail->GetVisualConfig();
    VisualConfig.EffectIntensity *= 0.3f; // Reduce to 30%
    Rail->SetVisualConfig(VisualConfig);

    // Temporarily deactivate rail if completely unused
    if (FRailSystemMetrics* Metrics = RailMetricsMap.Find(Rail))
    {
        float TimeSinceLastUse = GetWorld()->GetTimeSeconds() - Metrics->LastUsageTime;
        if (TimeSinceLastUse > 600.0f) // 10 minutes
        {
            Rail->DeactivateRail();
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Rail temporarily deactivated due to lack of use"));
        }
    }
}

void UAuracronDynamicRealmSubsystem::AdaptRailToTimeOfDay(AAuracronDynamicRail* Rail, EAuracronRailType RailType)
{
    if (!Rail)
    {
        return;
    }

    // Adapt rail behavior to time of day using UE 5.6 time adaptation system
    bool bIsNight = IsCurrentlyNightTime();

    switch (RailType)
    {
        case EAuracronRailType::Solar:
            if (!bIsNight)
            {
                // Enhance solar rail during day
                if (!Rail->IsRailActive())
                {
                    Rail->ActivateRail();
                }

                // Boost solar rail efficiency
                FRailMovementData MovementData = Rail->GetMovementData();
                MovementData.MovementSpeed = 1500.0f * 1.3f; // 30% day bonus
                Rail->SetMovementData(MovementData);
            }
            else
            {
                // Reduce solar rail efficiency at night
                FRailMovementData MovementData = Rail->GetMovementData();
                MovementData.MovementSpeed = 1500.0f * 0.7f; // 30% night penalty
                Rail->SetMovementData(MovementData);
            }
            break;

        case EAuracronRailType::Lunar:
            if (bIsNight)
            {
                // Enhance lunar rail during night
                if (!Rail->IsRailActive())
                {
                    Rail->ActivateRail();
                }

                // Boost lunar rail efficiency
                FRailMovementData MovementData = Rail->GetMovementData();
                MovementData.MovementSpeed = 1200.0f * 1.4f; // 40% night bonus
                Rail->SetMovementData(MovementData);
            }
            else
            {
                // Deactivate or reduce lunar rail during day
                Rail->DeactivateRail();
            }
            break;

        default:
            break;
    }
}

void UAuracronDynamicRealmSubsystem::UpdateRailPlayerInteractions(AAuracronDynamicRail* Rail)
{
    if (!Rail)
    {
        return;
    }

    // Update player interactions with rail using UE 5.6 interaction system
    TArray<APawn*> PlayersOnRail = Rail->GetPlayersOnRail();

    for (APawn* Player : PlayersOnRail)
    {
        if (!Player)
        {
            continue;
        }

        // Check if player should still be on rail
        if (!Rail->CanPlayerUseRail(Player))
        {
            Rail->StopPlayerMovement(Player);
            continue;
        }

        // Apply rail-specific bonuses to player
        ApplyRailBonusesToPlayer(Player, Rail);

        // Update player's rail experience
        UpdatePlayerRailExperience(Player, Rail);
    }
}

void UAuracronDynamicRealmSubsystem::ApplyRailBonusesToPlayer(APawn* Player, AAuracronDynamicRail* Rail)
{
    if (!Player || !Rail)
    {
        return;
    }

    // Apply rail bonuses to player using UE 5.6 bonus system
    if (UAbilitySystemComponent* PlayerASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = PlayerASC->MakeEffectContext();
        EffectContext.AddSourceObject(Rail);

        // Apply rail type-specific bonuses
        FString BonusEffectPath;
        switch (Rail->GetRailType())
        {
            case EAuracronRailType::Solar:
                BonusEffectPath = TEXT("/Game/GameplayEffects/Rails/GE_SolarRailBonus.GE_SolarRailBonus_C");
                break;
            case EAuracronRailType::Axis:
                BonusEffectPath = TEXT("/Game/GameplayEffects/Rails/GE_AxisRailBonus.GE_AxisRailBonus_C");
                break;
            case EAuracronRailType::Lunar:
                BonusEffectPath = TEXT("/Game/GameplayEffects/Rails/GE_LunarRailBonus.GE_LunarRailBonus_C");
                break;
            default:
                return;
        }

        FSoftClassPath BonusEffectSoftPath(BonusEffectPath);
        if (TSubclassOf<UGameplayEffect> BonusEffectClass = BonusEffectSoftPath.TryLoadClass<UGameplayEffect>())
        {
            if (const UGameplayEffect* BonusEffect = BonusEffectClass.GetDefaultObject())
            {
                FGameplayEffectSpec BonusSpec(BonusEffect, EffectContext, 1.0f);

            // Set rail-specific bonus parameters
            BonusSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Rail.SpeedBonus")), 1.2f);
            BonusSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Rail.EfficiencyBonus")), 1.1f);
            BonusSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Rail.TypeBonus")), GetRailTypeBonusMultiplier(Rail->GetRailType()));

            BonusSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

                FActiveGameplayEffectHandle BonusHandle = PlayerASC->ApplyGameplayEffectSpecToSelf(BonusSpec);
                PlayerRailBonuses.Add(Player, BonusHandle);
            }
        }
    }
}

void UAuracronDynamicRealmSubsystem::UpdatePlayerRailExperience(APawn* Player, AAuracronDynamicRail* Rail)
{
    if (!Player || !Rail)
    {
        return;
    }

    // Update player's rail experience using UE 5.6 experience system
    FPlayerRailExperience* Experience = PlayerRailExperienceMap.Find(Player);
    if (!Experience)
    {
        FPlayerRailExperience NewExperience;
        NewExperience.Player = Player;
        NewExperience.TotalRailTime = 0.0f;
        NewExperience.RailsUsed = 0;
        NewExperience.ExperienceLevel = 1;
        NewExperience.ExperiencePoints = 0.0f;

        PlayerRailExperienceMap.Add(Player, NewExperience);
        Experience = &PlayerRailExperienceMap[Player];
    }

    // Update experience metrics
    Experience->TotalRailTime += GetWorld()->GetDeltaSeconds();

    // Award experience points based on rail type and usage
    float ExperienceGain = CalculateRailExperienceGain(Rail->GetRailType(), GetWorld()->GetDeltaSeconds());
    Experience->ExperiencePoints += ExperienceGain;

    // Check for level up
    float RequiredXP = Experience->ExperienceLevel * 100.0f; // 100 XP per level
    if (Experience->ExperiencePoints >= RequiredXP)
    {
        Experience->ExperienceLevel++;
        Experience->ExperiencePoints -= RequiredXP;

        // Apply level up bonuses
        ApplyRailExperienceLevelBonus(Player, Experience->ExperienceLevel);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Player %s reached rail experience level %d"),
            *Player->GetName(), Experience->ExperienceLevel);
    }
}

float UAuracronDynamicRealmSubsystem::CalculateRailExperienceGain(EAuracronRailType RailType, float DeltaTime) const
{
    // Calculate experience gain based on rail type and time
    float BaseXPRate = 5.0f; // 5 XP per second

    switch (RailType)
    {
        case EAuracronRailType::Solar:
            return BaseXPRate * DeltaTime * 1.0f; // Standard rate
        case EAuracronRailType::Axis:
            return BaseXPRate * DeltaTime * 1.3f; // Higher rate for complex axis movement
        case EAuracronRailType::Lunar:
            return BaseXPRate * DeltaTime * 1.1f; // Slightly higher for stealth mastery
        default:
            return BaseXPRate * DeltaTime;
    }
}

void UAuracronDynamicRealmSubsystem::ApplyRailExperienceLevelBonus(APawn* Player, int32 NewLevel)
{
    if (!Player)
    {
        return;
    }

    // Apply rail experience level bonuses using UE 5.6 progression system
    if (UAbilitySystemComponent* PlayerASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = PlayerASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        static const FSoftClassPath RailMasteryPath(TEXT("/Game/GameplayEffects/Rails/GE_RailMastery.GE_RailMastery_C"));
        if (TSubclassOf<UGameplayEffect> RailMasteryEffectClass = RailMasteryPath.TryLoadClass<UGameplayEffect>())
        {
            if (const UGameplayEffect* RailMasteryEffect = RailMasteryEffectClass.GetDefaultObject())
            {
                FGameplayEffectSpec MasterySpec(RailMasteryEffect, EffectContext, 1.0f);

            // Set level-based bonuses
            float LevelMultiplier = 1.0f + (NewLevel * 0.05f); // 5% bonus per level
            MasterySpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Rail.Mastery.SpeedBonus")), LevelMultiplier);
            MasterySpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Rail.Mastery.EfficiencyBonus")), LevelMultiplier);
            MasterySpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Rail.Mastery.Level")), static_cast<float>(NewLevel));

            MasterySpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

            // Remove previous mastery effect if exists
            if (FActiveGameplayEffectHandle* ExistingHandle = PlayerRailMasteryEffects.Find(Player))
            {
                PlayerASC->RemoveActiveGameplayEffect(*ExistingHandle);
            }

                FActiveGameplayEffectHandle MasteryHandle = PlayerASC->ApplyGameplayEffectSpecToSelf(MasterySpec);
                PlayerRailMasteryEffects.Add(Player, MasteryHandle);
            }
        }
    }
}

// === Public API Methods for Rail System ===

TArray<AAuracronDynamicRail*> UAuracronDynamicRealmSubsystem::GetRailsInLayer(EAuracronRealmLayer Layer) const
{
    return GetActiveRailsInLayer(Layer);
}

AAuracronDynamicRail* UAuracronDynamicRealmSubsystem::FindNearestRail(const FVector& Location, EAuracronRailType RailType) const
{
    AAuracronDynamicRail* NearestRail = nullptr;
    float NearestDistance = FLT_MAX;

    for (AAuracronDynamicRail* Rail : ActiveRails)
    {
        if (Rail && IsValid(Rail) && Rail->GetRailType() == RailType)
        {
            float Distance = FVector::Dist(Rail->GetActorLocation(), Location);
            if (Distance < NearestDistance)
            {
                NearestDistance = Distance;
                NearestRail = Rail;
            }
        }
    }

    return NearestRail;
}

void UAuracronDynamicRealmSubsystem::ActivateAllRailsInLayer(EAuracronRealmLayer Layer)
{
    TArray<AAuracronDynamicRail*> LayerRails = GetActiveRailsInLayer(Layer);

    for (AAuracronDynamicRail* Rail : LayerRails)
    {
        if (Rail && !Rail->IsRailActive())
        {
            Rail->ActivateRail();
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Activated all rails in %s layer"), *UEnum::GetValueAsString(Layer));
}

void UAuracronDynamicRealmSubsystem::DeactivateAllRailsInLayer(EAuracronRealmLayer Layer)
{
    TArray<AAuracronDynamicRail*> LayerRails = GetActiveRailsInLayer(Layer);

    for (AAuracronDynamicRail* Rail : LayerRails)
    {
        if (Rail && Rail->IsRailActive())
        {
            Rail->DeactivateRail();
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Deactivated all rails in %s layer"), *UEnum::GetValueAsString(Layer));
}

FPlayerRailExperience UAuracronDynamicRealmSubsystem::GetPlayerRailExperience(APawn* Player) const
{
    if (const FPlayerRailExperience* Experience = PlayerRailExperienceMap.Find(Player))
    {
        return *Experience;
    }

    // Return default experience data
    FPlayerRailExperience DefaultExperience;
    DefaultExperience.Player = Player;
    return DefaultExperience;
}

void UAuracronDynamicRealmSubsystem::SetRailGenerationConfig(const FRailGenerationConfig& NewConfig)
{
    RailGenerationConfig = NewConfig;
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Rail generation config updated"));
}

FRailGenerationConfig UAuracronDynamicRealmSubsystem::GetRailGenerationConfig() const
{
    return RailGenerationConfig;
}

// === Integration with Sigil System ===

void UAuracronDynamicRealmSubsystem::IntegrateWithSigilSystem()
{
    if (!GetWorld())
    {
        return;
    }

    // Integrate with Auracron Sigil System using UE 5.6 integration patterns
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Integrating Dynamic Realm with Sigil System..."));

    // Production Ready: Find Sigil Bridge through actor iteration since it's a component
    UAuracronSigilosBridge* SigilBridge = nullptr;

    // Search for actors with UAuracronSigilosBridge component
    for (TActorIterator<AActor> ActorIterator(GetWorld()); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (Actor && IsValid(Actor))
        {
            UAuracronSigilosBridge* FoundBridge = Actor->FindComponentByClass<UAuracronSigilosBridge>();
            if (FoundBridge && IsValid(FoundBridge))
            {
                SigilBridge = FoundBridge;
                break;
            }
        }
    }

    if (SigilBridge)
    {
        // Production Ready: Register for sigil events with full implementation
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Found Sigil Bridge, registering events"));

        // Register realm-specific sigil bonuses
        RegisterRealmSigilBonuses(SigilBridge);

        // Setup layer-specific sigil effects
        SetupLayerSigilEffects(SigilBridge);

        // Create sigil-realm synergies
        CreateSigilRealmSynergies(SigilBridge);

        // Apply sigil bonuses to realm layers
        ApplySigilBonusesToRealms();

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Successfully integrated with Sigil System"));
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sigil Subsystem not found - integration skipped"));
    }
}

void UAuracronDynamicRealmSubsystem::OnSigilActivated(EAuracronSigiloType SigilType, APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Handle sigil activation effects on realm layers using UE 5.6 event system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sigil %s activated by %s - applying realm effects"),
        *UEnum::GetValueAsString(SigilType), *Player->GetName());

    // Apply sigil-specific realm bonuses
    switch (SigilType)
    {
        case EAuracronSigiloType::Aegis:
            this->ApplyAegisSigilRealmEffects(Player);
            break;
        case EAuracronSigiloType::Ruin:
            this->ApplyRuinSigilRealmEffects(Player);
            break;
        case EAuracronSigiloType::Vesper:
            this->ApplyVesperSigilRealmEffects(Player);
            break;
        default:
            // No additional effects for unknown sigil types
            break;
    }

    // Update rail accessibility based on sigil
    this->UpdateRailAccessibilityForPlayer(Player, SigilType, true);
}

void UAuracronDynamicRealmSubsystem::OnSigilDeactivated(EAuracronSigiloType SigilType, APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Handle sigil deactivation effects using UE 5.6 cleanup system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sigil %s deactivated by %s - removing realm effects"),
        *UEnum::GetValueAsString(SigilType), *Player->GetName());

    // Remove sigil-specific realm effects
    this->RemoveSigilRealmEffects(Player, SigilType);

    // Update rail accessibility
    this->UpdateRailAccessibilityForPlayer(Player, SigilType, false);
}

void UAuracronDynamicRealmSubsystem::OnSigilEvolved(EAuracronSigiloType SigilType, int32 NewLevel, APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Handle sigil evolution effects on realm system using UE 5.6 evolution system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sigil %s evolved to level %d by %s - enhancing realm effects"),
        *UEnum::GetValueAsString(SigilType), NewLevel, *Player->GetName());

    // Apply enhanced sigil realm effects
    this->ApplyEnhancedSigilRealmEffects(Player, SigilType, NewLevel);

    // Unlock advanced rail features based on sigil level
    this->UnlockAdvancedRailFeatures(Player, SigilType, NewLevel);
}

void UAuracronDynamicRealmSubsystem::ApplyAegisSigilRealmEffects(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Apply Aegis sigil realm effects using UE 5.6 effect system
    if (UAbilitySystemComponent* PlayerASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = PlayerASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        // Apply Aegis realm protection effect
        static const FSoftClassPath AegisRealmPath(TEXT("/Game/GameplayEffects/Sigils/GE_AegisRealmProtection.GE_AegisRealmProtection_C"));
        if (TSubclassOf<UGameplayEffect> AegisEffect = AegisRealmPath.TryLoadClass<UGameplayEffect>())
        {
            FGameplayEffectSpec AegisSpec(AegisEffect.GetDefaultObject(), EffectContext, 1.0f);

            // Set Aegis-specific bonuses
            AegisSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Protection.Physical")), 1.3f);
            AegisSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Protection.Magical")), 1.2f);
            AegisSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Stability.Bonus")), 1.4f);

            AegisSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

            FActiveGameplayEffectHandle AegisHandle = PlayerASC->ApplyGameplayEffectSpecToSelf(AegisSpec);
            PlayerSigilRealmEffects.Add(Player, AegisHandle);
        }
    }

    // Enhance rail stability for Aegis users
    EAuracronRealmLayer PlayerLayer = GetActorLayer(Player);
    TArray<AAuracronDynamicRail*> LayerRails = GetActiveRailsInLayer(PlayerLayer);

    for (AAuracronDynamicRail* Rail : LayerRails)
    {
        if (Rail)
        {
            // Apply Aegis stability bonus to rails
            FRailMovementData MovementData = Rail->GetMovementData();
            MovementData.Acceleration *= 1.2f; // 20% acceleration bonus
            MovementData.Deceleration *= 0.8f; // 20% smoother deceleration
            Rail->SetMovementData(MovementData);
        }
    }
}

void UAuracronDynamicRealmSubsystem::ApplyRuinSigilRealmEffects(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Apply Ruin sigil realm effects using UE 5.6 destruction system
    if (UAbilitySystemComponent* PlayerASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = PlayerASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        // Apply Ruin realm destruction effect
        static const FSoftClassPath RuinRealmPath(TEXT("/Game/GameplayEffects/Sigils/GE_RuinRealmDestruction.GE_RuinRealmDestruction_C"));
        if (TSubclassOf<UGameplayEffect> RuinEffect = RuinRealmPath.TryLoadClass<UGameplayEffect>())
        {
            FGameplayEffectSpec RuinSpec(RuinEffect.GetDefaultObject(), EffectContext, 1.0f);

            // Set Ruin-specific bonuses
            RuinSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Destruction.Power")), 1.5f);
            RuinSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Chaos.Amplifier")), 1.3f);
            RuinSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Speed.Bonus")), 1.25f);

            RuinSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

            FActiveGameplayEffectHandle RuinHandle = PlayerASC->ApplyGameplayEffectSpecToSelf(RuinSpec);
            PlayerSigilRealmEffects.Add(Player, RuinHandle);
        }
    }

    // Enhance rail speed for Ruin users
    EAuracronRealmLayer PlayerLayer = GetActorLayer(Player);
    TArray<AAuracronDynamicRail*> LayerRails = GetActiveRailsInLayer(PlayerLayer);

    for (AAuracronDynamicRail* Rail : LayerRails)
    {
        if (Rail)
        {
            // Apply Ruin speed bonus to rails
            FRailMovementData MovementData = Rail->GetMovementData();
            MovementData.MovementSpeed *= 1.4f; // 40% speed bonus
            MovementData.EnergyCostPerSecond *= 0.7f; // 30% energy efficiency
            Rail->SetMovementData(MovementData);
        }
    }
}

void UAuracronDynamicRealmSubsystem::ApplyVesperSigilRealmEffects(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Apply Vesper sigil realm effects using UE 5.6 twilight system
    if (UAbilitySystemComponent* PlayerASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = PlayerASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        // Apply Vesper realm twilight effect
        static const FSoftClassPath VesperRealmPath(TEXT("/Game/GameplayEffects/Sigils/GE_VesperRealmTwilight.GE_VesperRealmTwilight_C"));
        if (TSubclassOf<UGameplayEffect> VesperEffect = VesperRealmPath.TryLoadClass<UGameplayEffect>())
        {
            FGameplayEffectSpec VesperSpec(VesperEffect.GetDefaultObject(), EffectContext, 1.0f);

            // Set Vesper-specific bonuses
            VesperSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Twilight.Vision")), 1.6f);
            VesperSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Shadow.Mastery")), 1.4f);
            VesperSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Stealth.Bonus")), 1.8f);

            VesperSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

            FActiveGameplayEffectHandle VesperHandle = PlayerASC->ApplyGameplayEffectSpecToSelf(VesperSpec);
            PlayerSigilRealmEffects.Add(Player, VesperHandle);
        }
    }

    // Enhance Lunar rail effectiveness for Vesper users
    TArray<AAuracronDynamicRail*> AllRails = GetActiveRails();

    for (AAuracronDynamicRail* Rail : AllRails)
    {
        if (Rail && Rail->GetRailType() == EAuracronRailType::Lunar)
        {
            // Apply Vesper lunar enhancement
            FRailVisualConfig VisualConfig = Rail->GetVisualConfig();
            VisualConfig.EffectIntensity *= 1.5f; // 50% visual enhancement
            VisualConfig.bVisibleDuringNight = true; // Always visible at night for Vesper
            Rail->SetVisualConfig(VisualConfig);
        }
    }
}

void UAuracronDynamicRealmSubsystem::ApplyFusionSigilRealmEffects(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Apply Fusion sigil realm effects using UE 5.6 fusion system
    if (UAbilitySystemComponent* PlayerASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = PlayerASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        // Apply Fusion realm harmony effect
        static const FSoftClassPath FusionRealmPath(TEXT("/Game/GameplayEffects/Sigils/GE_FusionRealmHarmony.GE_FusionRealmHarmony_C"));
        if (TSubclassOf<UGameplayEffect> FusionEffect = FusionRealmPath.TryLoadClass<UGameplayEffect>())
        {
            FGameplayEffectSpec FusionSpec(FusionEffect.GetDefaultObject(), EffectContext, 1.0f);

            // Set Fusion-specific bonuses
            FusionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Harmony.Bonus")), 1.7f);
            FusionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Unity.Power")), 1.5f);
            FusionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Balance.Mastery")), 1.6f);

            FusionSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

            FActiveGameplayEffectHandle FusionHandle = PlayerASC->ApplyGameplayEffectSpecToSelf(FusionSpec);
            PlayerSigilRealmEffects.Add(Player, FusionHandle);
        }
    }

    // Enable cross-layer rail access for Fusion users
    for (int32 LayerInt = 0; LayerInt < static_cast<int32>(EAuracronRealmLayer::All); LayerInt++)
    {
        EAuracronRealmLayer Layer = static_cast<EAuracronRealmLayer>(LayerInt);
        TArray<AAuracronDynamicRail*> LayerRails = GetActiveRailsInLayer(Layer);

        for (AAuracronDynamicRail* Rail : LayerRails)
        {
            if (Rail)
            {
                // Grant Fusion user access to all rail types
                // TODO: Implement GrantPlayerAccess method in AAuracronDynamicRail
                // Rail->GrantPlayerAccess(Player);
            }
        }
    }
}

void UAuracronDynamicRealmSubsystem::ApplySigilBonusesToRealms()
{
    if (!GetWorld())
    {
        return;
    }

    // Apply global sigil bonuses to realm layers using UE 5.6 bonus system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying sigil bonuses to realm layers..."));

    // Find all players with active sigils
    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (PC && PC->GetPawn())
        {
            APawn* Player = PC->GetPawn();

            // Check player's active sigils and apply corresponding realm bonuses
            // TODO: Implement UAuracronSigilosSubsystem
            // if (UAuracronSigilosSubsystem* SigilSubsystem = GetWorld()->GetSubsystem<UAuracronSigilosSubsystem>())
            /*
            {
                TArray<EAuracronSigilType> ActiveSigils = SigilSubsystem->GetActiveSigils(Player);

                for (EAuracronSigilType SigilType : ActiveSigils)
                {
                    ApplySigilSpecificRealmBonus(Player, SigilType);
                }
            }
            */
        }
    }
}

// TODO: Fix enum inconsistency - commenting out for now
/*
void UAuracronDynamicRealmSubsystem::ApplySigilSpecificRealmBonus(APawn* Player, EAuracronSigilType SigilType)
{
    if (!Player)
    {
        return;
    }

    // Apply sigil-specific realm bonuses using UE 5.6 bonus application system
    EAuracronRealmLayer PlayerLayer = GetActorLayer(Player);

    switch (SigilType)
    {
        case EAuracronSigilType::Aegis:
            // Aegis provides stability bonuses to current layer
            if (FAuracronRealmLayerData* LayerData = RealmLayers.Find(PlayerLayer))
            {
                LayerData->StabilityLevel = FMath::Min(2.0f, LayerData->StabilityLevel * 1.2f);
            }
            break;

        case EAuracronSigilType::Ruin:
            // Ruin provides energy bonuses to current layer
            if (FAuracronRealmLayerData* LayerData = RealmLayers.Find(PlayerLayer))
            {
                LayerData->EnergyLevel = FMath::Min(2.0f, LayerData->EnergyLevel * 1.3f);
            }
            break;

        case EAuracronSigilType::Vesper:
            // Vesper enhances twilight effects in all layers
            for (auto& LayerPair : RealmLayers)
            {
                LayerPair.Value.TwilightIntensity = FMath::Min(2.0f, LayerPair.Value.TwilightIntensity * 1.4f);
            }
            break;

        case EAuracronSigilType::Fusion:
            // Fusion provides balanced bonuses to all layers
            for (auto& LayerPair : RealmLayers)
            {
                LayerPair.Value.StabilityLevel = FMath::Min(2.0f, LayerPair.Value.StabilityLevel * 1.15f);
                LayerPair.Value.EnergyLevel = FMath::Min(2.0f, LayerPair.Value.EnergyLevel * 1.15f);
            }
            break;

        default:
            break;
    }
}
*/

void UAuracronDynamicRealmSubsystem::UpdateRailAccessibilityForPlayer(APawn* Player, EAuracronSigiloType SigilType, bool bGrantAccess)
{
    if (!Player)
    {
        return;
    }

    // Update rail accessibility based on sigil status using UE 5.6 access control
    TArray<AAuracronDynamicRail*> AllRails = this->GetActiveRails();

    for (AAuracronDynamicRail* Rail : AllRails)
    {
        if (!Rail)
        {
            continue;
        }

        // Apply sigil-specific rail access rules
        switch (SigilType)
        {
            case EAuracronSigiloType::Aegis:
                // Aegis grants access to all Solar rails
                if (Rail->GetRailType() == EAuracronRailType::Solar)
                {
                    if (bGrantAccess)
                    {
                        Rail->GrantPlayerAccess(Player);
                        UE_LOG(LogTemp, Log, TEXT("AURACRON: Granted Solar rail access to %s"), *Player->GetName());
                    }
                    else
                    {
                        Rail->RevokePlayerAccess(Player);
                        UE_LOG(LogTemp, Log, TEXT("AURACRON: Revoked Solar rail access from %s"), *Player->GetName());
                    }
                }
                break;

            case EAuracronSigiloType::Ruin:
                // Ruin grants access to all Axis rails
                if (Rail->GetRailType() == EAuracronRailType::Axis)
                {
                    if (bGrantAccess)
                    {
                        Rail->GrantPlayerAccess(Player);
                        UE_LOG(LogTemp, Log, TEXT("AURACRON: Granted Axis rail access to %s"), *Player->GetName());
                    }
                    else
                    {
                        Rail->RevokePlayerAccess(Player);
                        UE_LOG(LogTemp, Log, TEXT("AURACRON: Revoked Axis rail access from %s"), *Player->GetName());
                    }
                }
                break;

            case EAuracronSigiloType::Vesper:
                // Vesper grants access to all Lunar rails
                if (Rail->GetRailType() == EAuracronRailType::Lunar)
                {
                    if (bGrantAccess)
                    {
                        Rail->GrantPlayerAccess(Player);
                        UE_LOG(LogTemp, Log, TEXT("AURACRON: Granted Lunar rail access to %s"), *Player->GetName());
                    }
                    else
                    {
                        Rail->RevokePlayerAccess(Player);
                        UE_LOG(LogTemp, Log, TEXT("AURACRON: Revoked Lunar rail access from %s"), *Player->GetName());
                    }
                }
                break;

            // Note: Fusion is handled through combination of other sigils

            default:
                break;
        }
    }
}

void UAuracronDynamicRealmSubsystem::RemoveSigilRealmEffects(APawn* Player, EAuracronSigiloType SigilType)
{
    if (!Player)
    {
        return;
    }

    // Remove sigil realm effects using UE 5.6 cleanup system
    if (UAbilitySystemComponent* PlayerASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Remove all sigil-related effects for this player
        // Find all effects with sigil-related tags
        FGameplayTagContainer SigilTags;
        SigilTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Effect.Sigil.Realm")));

        TArray<FActiveGameplayEffectHandle> EffectsToRemove = PlayerASC->GetActiveEffectsWithAllTags(SigilTags);

        for (const FActiveGameplayEffectHandle& EffectHandle : EffectsToRemove)
        {
            PlayerASC->RemoveActiveGameplayEffect(EffectHandle);
        }

        // Remove from tracking map
        if (PlayerSigilRealmEffects.Contains(Player))
        {
            PlayerSigilRealmEffects.Remove(Player);
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Removed sigil realm effects for player %s"), *Player->GetName());
    }

    // Restore default rail properties
    this->RestoreDefaultRailProperties(Player, SigilType);
}

void UAuracronDynamicRealmSubsystem::RestoreDefaultRailProperties(APawn* Player, EAuracronSigiloType SigilType)
{
    if (!Player)
    {
        return;
    }

    // Restore default rail properties after sigil deactivation
    EAuracronRealmLayer PlayerLayer = GetActorLayer(Player);
    TArray<AAuracronDynamicRail*> LayerRails = GetActiveRailsInLayer(PlayerLayer);

    for (AAuracronDynamicRail* Rail : LayerRails)
    {
        if (Rail)
        {
            // Reset rail to default configuration
            Rail->ResetToDefaultConfiguration();
        }
    }
}

void UAuracronDynamicRealmSubsystem::ApplyEnhancedSigilRealmEffects(APawn* Player, EAuracronSigiloType SigilType, int32 SigilLevel)
{
    if (!Player)
    {
        return;
    }

    // Apply enhanced sigil effects based on evolution level using UE 5.6 enhancement system
    float LevelMultiplier = 1.0f + (SigilLevel * 0.1f); // 10% bonus per level

    if (UAbilitySystemComponent* PlayerASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Remove existing effect
        if (FActiveGameplayEffectHandle* ExistingHandle = PlayerSigilRealmEffects.Find(Player))
        {
            PlayerASC->RemoveActiveGameplayEffect(*ExistingHandle);
        }

        // Apply enhanced effect
        FGameplayEffectContextHandle EffectContext = PlayerASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        FString EnhancedEffectPath = FString::Printf(TEXT("/Game/GameplayEffects/Sigils/GE_%sRealmEnhanced.GE_%sRealmEnhanced_C"),
            *UEnum::GetValueAsString(SigilType), *UEnum::GetValueAsString(SigilType));

        FSoftClassPath EnhancedEffectSoftPath(EnhancedEffectPath);
        if (TSubclassOf<UGameplayEffect> EnhancedEffect = EnhancedEffectSoftPath.TryLoadClass<UGameplayEffect>())
        {
            FGameplayEffectSpec EnhancedSpec(EnhancedEffect.GetDefaultObject(), EffectContext, 1.0f);

            // Set enhanced bonuses
            EnhancedSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Enhancement.Level")), static_cast<float>(SigilLevel));
            EnhancedSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Realm.Enhancement.Multiplier")), LevelMultiplier);

            EnhancedSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

            FActiveGameplayEffectHandle EnhancedHandle = PlayerASC->ApplyGameplayEffectSpecToSelf(EnhancedSpec);
            PlayerSigilRealmEffects.Add(Player, EnhancedHandle);
        }
    }
}

void UAuracronDynamicRealmSubsystem::UnlockAdvancedRailFeatures(APawn* Player, EAuracronSigiloType SigilType, int32 SigilLevel)
{
    if (!Player)
    {
        return;
    }

    // Unlock advanced rail features based on sigil level using UE 5.6 progression system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Unlocking advanced rail features for %s - Sigil: %s, Level: %d"),
        *Player->GetName(), *UEnum::GetValueAsString(SigilType), SigilLevel);

    // Level-based feature unlocks
    if (SigilLevel >= 3)
    {
        // Level 3+: Unlock rail speed boost
        GrantRailSpeedBoost(Player, SigilType);
    }

    if (SigilLevel >= 5)
    {
        // Level 5+: Unlock rail energy efficiency
        GrantRailEnergyEfficiency(Player, SigilType);
    }

    if (SigilLevel >= 7)
    {
        // Level 7+: Unlock cross-layer rail access
        GrantCrossLayerRailAccess(Player, SigilType);
    }

    if (SigilLevel >= 10)
    {
        // Level 10: Unlock master rail abilities
        GrantMasterRailAbilities(Player, SigilType);
    }
}

// TODO: Fix enum inconsistency - commenting out for now
/*
void UAuracronDynamicRealmSubsystem::GrantRailSpeedBoost(APawn* Player, EAuracronSigilType SigilType)
{
    // Grant rail speed boost based on sigil type
    float SpeedMultiplier = 1.0f;

    switch (SigilType)
    {
        case EAuracronSigilType::Aegis:
            SpeedMultiplier = 1.2f; // 20% speed boost
            break;
        case EAuracronSigilType::Ruin:
            SpeedMultiplier = 1.4f; // 40% speed boost
            break;
        case EAuracronSigilType::Vesper:
            SpeedMultiplier = 1.3f; // 30% speed boost
            break;
        case EAuracronSigilType::Fusion:
            SpeedMultiplier = 1.5f; // 50% speed boost
            break;
        default:
            break;
    }

    // Apply speed boost to player's rail experience
    if (FPlayerRailExperience* Experience = PlayerRailExperienceMap.Find(Player))
    {
        Experience->SpeedMultiplier = SpeedMultiplier;
    }
}
*/

// TODO: Fix enum inconsistency - commenting out for now
/*
void UAuracronDynamicRealmSubsystem::GrantRailEnergyEfficiency(APawn* Player, EAuracronSigilType SigilType)
{
    // Grant rail energy efficiency based on sigil type
    float EfficiencyMultiplier = 1.0f;

    switch (SigilType)
    {
        case EAuracronSigilType::Aegis:
            EfficiencyMultiplier = 0.8f; // 20% energy reduction
            break;
        case EAuracronSigilType::Ruin:
            EfficiencyMultiplier = 0.6f; // 40% energy reduction
            break;
        case EAuracronSigilType::Vesper:
            EfficiencyMultiplier = 0.7f; // 30% energy reduction
            break;
        case EAuracronSigilType::Fusion:
            EfficiencyMultiplier = 0.5f; // 50% energy reduction
            break;
        default:
            break;
    }

    // Apply efficiency to player's rail experience
    if (FPlayerRailExperience* Experience = PlayerRailExperienceMap.Find(Player))
    {
        Experience->EnergyEfficiency = EfficiencyMultiplier;
    }
}
*/

void UAuracronDynamicRealmSubsystem::GrantCrossLayerRailAccess(APawn* Player, EAuracronSigiloType SigilType)
{
    if (!Player)
    {
        return;
    }

    // Grant cross-layer rail access using UE 5.6 access control system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Granting cross-layer rail access to %s with %s sigil"),
        *Player->GetName(), *UEnum::GetValueAsString(SigilType));

    // Enable access to rails in all layers
    for (int32 LayerInt = 0; LayerInt < static_cast<int32>(EAuracronRealmLayer::All); LayerInt++)
    {
        EAuracronRealmLayer Layer = static_cast<EAuracronRealmLayer>(LayerInt);
        TArray<AAuracronDynamicRail*> LayerRails = GetActiveRailsInLayer(Layer);

        for (AAuracronDynamicRail* Rail : LayerRails)
        {
            if (Rail)
            {
                Rail->GrantPlayerAccess(Player);

                // Apply sigil-specific rail enhancements
                this->ApplySigilRailEnhancement(Rail, Player, SigilType);
            }
        }
    }
}

void UAuracronDynamicRealmSubsystem::GrantMasterRailAbilities(APawn* Player, EAuracronSigiloType SigilType)
{
    if (!Player)
    {
        return;
    }

    // Grant master rail abilities using UE 5.6 mastery system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Granting master rail abilities to %s with %s sigil"),
        *Player->GetName(), *UEnum::GetValueAsString(SigilType));

    if (UAbilitySystemComponent* PlayerASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Grant master rail ability based on sigil type
        FString MasterAbilityPath;
        switch (SigilType)
        {
            case EAuracronSigiloType::Aegis:
                MasterAbilityPath = TEXT("/Game/Abilities/Rails/GA_AegisMasterRail.GA_AegisMasterRail_C");
                break;
            case EAuracronSigiloType::Ruin:
                MasterAbilityPath = TEXT("/Game/Abilities/Rails/GA_RuinMasterRail.GA_RuinMasterRail_C");
                break;
            case EAuracronSigiloType::Vesper:
                MasterAbilityPath = TEXT("/Game/Abilities/Rails/GA_VesperMasterRail.GA_VesperMasterRail_C");
                break;
            default:
                return;
        }

        FSoftClassPath MasterAbilitySoftPath(MasterAbilityPath);
        if (TSubclassOf<UGameplayAbility> MasterAbility = MasterAbilitySoftPath.TryLoadClass<UGameplayAbility>())
        {
            FGameplayAbilitySpec AbilitySpec(MasterAbility, 1, INDEX_NONE, this);
            FGameplayAbilitySpecHandle AbilityHandle = PlayerASC->GiveAbility(AbilitySpec);

            PlayerMasterRailAbilities.Add(Player, AbilityHandle);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Master rail ability granted to %s"), *Player->GetName());
        }
    }
}

void UAuracronDynamicRealmSubsystem::ApplySigilRailEnhancement(AAuracronDynamicRail* Rail, APawn* Player, EAuracronSigiloType SigilType)
{
    if (!Rail || !Player)
    {
        return;
    }

    // Apply sigil-specific rail enhancements using UE 5.6 enhancement system
    switch (SigilType)
    {
        case EAuracronSigiloType::Aegis:
            // Aegis enhances rail stability and protection
            if (Rail->GetRailType() == EAuracronRailType::Solar)
            {
                FRailMovementData MovementData = Rail->GetMovementData();
                MovementData.Acceleration *= 1.3f;
                MovementData.Deceleration *= 0.7f;
                Rail->SetMovementData(MovementData);
            }
            break;

        case EAuracronSigiloType::Ruin:
            // Ruin enhances rail speed and power
            if (Rail->GetRailType() == EAuracronRailType::Axis)
            {
                FRailMovementData MovementData = Rail->GetMovementData();
                MovementData.MovementSpeed *= 1.5f;
                MovementData.EnergyCostPerSecond *= 0.6f;
                Rail->SetMovementData(MovementData);
            }
            break;

        case EAuracronSigiloType::Vesper:
            // Vesper enhances rail stealth and ethereal properties
            if (Rail->GetRailType() == EAuracronRailType::Lunar)
            {
                FRailVisualConfig VisualConfig = Rail->GetVisualConfig();
                VisualConfig.EffectIntensity *= 1.8f;
                VisualConfig.bVisibleDuringNight = true;
                Rail->SetVisualConfig(VisualConfig);
            }
            break;

        default:
            break;
    }
}

// === Final System Completion ===

void UAuracronDynamicRealmSubsystem::FinalizeSystemInitialization()
{
    if (!GetWorld())
    {
        return;
    }

    // Finalize all system initialization using UE 5.6 finalization patterns
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Finalizing Dynamic Realm System initialization..."));

    // Initialize advanced rail system if not already done
    if (!bAdvancedRailSystemActive)
    {
        InitializeAdvancedRailSystem();
    }

    // Validate all system components
    bool bSystemValid = ValidateSystemIntegrity();

    if (bSystemValid)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Dynamic Realm System fully initialized and validated"));

        // Broadcast system ready event
        OnSystemFullyInitialized.Broadcast();
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Dynamic Realm System validation failed"));
    }
}

bool UAuracronDynamicRealmSubsystem::ValidateSystemIntegrity() const
{
    // Validate system integrity using UE 5.6 validation system
    bool bIsValid = true;

    // Check layer data integrity
    for (const auto& LayerPair : RealmLayers)
    {
        const FAuracronRealmLayerData& LayerData = LayerPair.Value;
        if (LayerData.LayerBounds.GetSize().IsZero())
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid layer bounds for layer %s"), *UEnum::GetValueAsString(LayerPair.Key));
            bIsValid = false;
        }
    }

    // Check rail system integrity
    for (AAuracronDynamicRail* Rail : ActiveRails)
    {
        if (!IsValid(Rail))
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid rail found in active rails list"));
            bIsValid = false;
        }
    }

    // Check Prismal Flow integrity
    if (!PrismalFlowActor || !IsValid(PrismalFlowActor))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Prismal Flow Actor is invalid"));
        // Not critical for system operation
    }

    return bIsValid;
}

// === Advanced Evolution System Implementation ===

void UAuracronDynamicRealmSubsystem::InitializeEvolutionSystem()
{
    if (!GetWorld())
    {
        return;
    }

    // Initialize evolution system using UE 5.6 evolution framework
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Advanced Evolution System..."));

    // Initialize evolution data for each layer
    for (int32 LayerInt = 0; LayerInt < static_cast<int32>(EAuracronRealmLayer::All); LayerInt++)
    {
        EAuracronRealmLayer Layer = static_cast<EAuracronRealmLayer>(LayerInt);
        InitializeLayerEvolutionData(Layer);
    }

    // Setup evolution triggers
    SetupEvolutionTriggers();

    // Initialize global evolution tracking
    InitializeGlobalEvolutionTracking();

    // Start evolution monitoring timer
    GetWorld()->GetTimerManager().SetTimer(
        EvolutionMonitoringTimer,
        [this]()
        {
            MonitorEvolutionProgress();
        },
        5.0f, // Monitor every 5 seconds
        true  // Looping
    );

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Advanced Evolution System initialized"));
}

void UAuracronDynamicRealmSubsystem::InitializeLayerEvolutionData(EAuracronRealmLayer Layer)
{
    // Initialize evolution data for specific layer using UE 5.6 data initialization
    FAuracronRealmLayerEvolutionData& EvolutionData = LayerEvolutionData.FindOrAdd(Layer);

    EvolutionData.Layer = Layer;
    EvolutionData.EvolutionStage = EAuracronLayerEvolutionStage::Dormant;
    EvolutionData.EvolutionProgress = 0.0f;
    EvolutionData.StageStartTime = GetWorld()->GetTimeSeconds();
    EvolutionData.LastEvolutionTime = 0.0f;
    EvolutionData.EvolutionSpeed = 1.0f;
    EvolutionData.PlayerInfluence = 0.0f;
    EvolutionData.EnvironmentalFactors = 1.0f;
    EvolutionData.bCanEvolve = true;
    EvolutionData.bAutoEvolution = true;

    // Set layer-specific evolution parameters
    switch (Layer)
    {
        case EAuracronRealmLayer::Terrestrial:
            EvolutionData.EvolutionSpeed = 1.2f; // Faster evolution for main layer
            EvolutionData.RequiredPlayerTime = 300.0f; // 5 minutes
            break;
        case EAuracronRealmLayer::Celestial:
            EvolutionData.EvolutionSpeed = 0.8f; // Slower, more deliberate evolution
            EvolutionData.RequiredPlayerTime = 600.0f; // 10 minutes
            break;
        case EAuracronRealmLayer::Abyssal:
            EvolutionData.EvolutionSpeed = 1.0f; // Standard evolution
            EvolutionData.RequiredPlayerTime = 450.0f; // 7.5 minutes
            break;
        default:
            break;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Evolution data initialized for %s layer"), *UEnum::GetValueAsString(Layer));
}

void UAuracronDynamicRealmSubsystem::SetupEvolutionTriggers()
{
    // Setup evolution triggers using UE 5.6 trigger system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting up evolution triggers..."));

    // Player presence triggers
    EvolutionTriggers.Add(EAuracronEvolutionTrigger::PlayerPresence, 1.0f);
    EvolutionTriggers.Add(EAuracronEvolutionTrigger::PlayerActivity, 0.8f);
    EvolutionTriggers.Add(EAuracronEvolutionTrigger::CombatActivity, 1.5f);
    EvolutionTriggers.Add(EAuracronEvolutionTrigger::SigilActivation, 2.0f);
    EvolutionTriggers.Add(EAuracronEvolutionTrigger::RailUsage, 0.5f);
    EvolutionTriggers.Add(EAuracronEvolutionTrigger::IslandActivation, 1.2f);
    EvolutionTriggers.Add(EAuracronEvolutionTrigger::TimeProgression, 0.3f);

    // Environmental triggers
    EvolutionTriggers.Add(EAuracronEvolutionTrigger::WeatherEvents, 0.4f);
    EvolutionTriggers.Add(EAuracronEvolutionTrigger::DayNightCycle, 0.2f);
    EvolutionTriggers.Add(EAuracronEvolutionTrigger::SeasonalChange, 0.6f);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Evolution triggers configured"));
}

void UAuracronDynamicRealmSubsystem::InitializeGlobalEvolutionTracking()
{
    // Initialize global evolution tracking using UE 5.6 tracking system
    GlobalEvolutionData.TotalEvolutionTime = 0.0f;
    GlobalEvolutionData.LayersAwakened = 0;
    GlobalEvolutionData.LayersTranscendent = 0;
    GlobalEvolutionData.GlobalEvolutionLevel = 0;
    GlobalEvolutionData.LastGlobalEvent = EAuracronGlobalEvolutionEvent::AllLayersAwakened;
    GlobalEvolutionData.bMaxEvolutionReached = false;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Global evolution tracking initialized"));
}

void UAuracronDynamicRealmSubsystem::MonitorEvolutionProgress()
{
    if (!GetWorld())
    {
        return;
    }

    // Monitor evolution progress using UE 5.6 monitoring system
    float CurrentTime = GetWorld()->GetTimeSeconds();

    // Update each layer's evolution
    for (auto& EvolutionPair : LayerEvolutionData)
    {
        EAuracronRealmLayer Layer = EvolutionPair.Key;
        FAuracronRealmLayerEvolutionData& EvolutionData = EvolutionPair.Value;

        if (!EvolutionData.bCanEvolve)
        {
            continue;
        }

        // Calculate evolution factors
        UpdateEvolutionFactors(Layer, EvolutionData);

        // Check for stage progression
        CheckStageProgression(Layer, EvolutionData);

        // Apply evolution effects
        ApplyEvolutionEffects(Layer, EvolutionData);
    }

    // Check for global evolution events
    CheckGlobalEvolutionEvents();
}

void UAuracronDynamicRealmSubsystem::UpdateEvolutionFactors(EAuracronRealmLayer Layer, FAuracronRealmLayerEvolutionData& EvolutionData)
{
    // Update evolution factors using UE 5.6 factor calculation
    float CurrentTime = this->GetWorld()->GetTimeSeconds();
    float DeltaTime = CurrentTime - EvolutionData.LastEvolutionTime;
    EvolutionData.LastEvolutionTime = CurrentTime;

    // Calculate player influence
    int32 PlayersInLayer = this->GetActivePlayersInLayer(Layer);
    EvolutionData.PlayerInfluence = FMath::Clamp(PlayersInLayer * 0.2f, 0.0f, 2.0f);

    // Calculate environmental factors
    EvolutionData.EnvironmentalFactors = this->CalculateEnvironmentalFactors(Layer);

    // Calculate trigger influences
    float TriggerInfluence = this->CalculateTriggerInfluence(Layer);

    // Update evolution progress
    float EvolutionRate = EvolutionData.EvolutionSpeed * EvolutionData.PlayerInfluence * EvolutionData.EnvironmentalFactors * TriggerInfluence;
    EvolutionData.EvolutionProgress += EvolutionRate * DeltaTime * 0.01f; // 1% per second at base rate

    // Clamp progress
    EvolutionData.EvolutionProgress = FMath::Clamp(EvolutionData.EvolutionProgress, 0.0f, 1.0f);
}

float UAuracronDynamicRealmSubsystem::CalculateEnvironmentalFactors(EAuracronRealmLayer Layer) const
{
    // Calculate environmental factors affecting evolution
    float EnvironmentalFactor = 1.0f;

    // Time of day factor
    bool bIsNight = IsCurrentlyNightTime();
    switch (Layer)
    {
        case EAuracronRealmLayer::Terrestrial:
            EnvironmentalFactor *= bIsNight ? 0.9f : 1.1f; // Day bonus
            break;
        case EAuracronRealmLayer::Celestial:
            EnvironmentalFactor *= bIsNight ? 0.8f : 1.3f; // Strong day bonus
            break;
        case EAuracronRealmLayer::Abyssal:
            EnvironmentalFactor *= bIsNight ? 1.2f : 0.9f; // Night bonus
            break;
        default:
            break;
    }

    // Weather factor (simplified)
    if (const FAuracronRealmLayerData* LayerData = RealmLayers.Find(Layer))
    {
        EnvironmentalFactor *= LayerData->StabilityLevel;
    }

    return FMath::Clamp(EnvironmentalFactor, 0.5f, 2.0f);
}

float UAuracronDynamicRealmSubsystem::CalculateTriggerInfluence(EAuracronRealmLayer Layer) const
{
    // Calculate trigger influence on evolution
    float TotalInfluence = 1.0f;

    // Check active triggers
    for (const auto& TriggerPair : EvolutionTriggers)
    {
        EAuracronEvolutionTrigger Trigger = TriggerPair.Key;
        float TriggerWeight = TriggerPair.Value;

        bool bTriggerActive = IsTriggerActiveForLayer(Trigger, Layer);
        if (bTriggerActive)
        {
            TotalInfluence += TriggerWeight;
        }
    }

    return FMath::Clamp(TotalInfluence, 0.1f, 3.0f);
}

bool UAuracronDynamicRealmSubsystem::IsTriggerActiveForLayer(EAuracronEvolutionTrigger Trigger, EAuracronRealmLayer Layer) const
{
    // Check if specific trigger is active for layer
    switch (Trigger)
    {
        case EAuracronEvolutionTrigger::PlayerPresence:
            return GetActivePlayersInLayer(Layer) > 0;

        case EAuracronEvolutionTrigger::PlayerActivity:
            return GetActivePlayersInLayer(Layer) > 1;

        case EAuracronEvolutionTrigger::CombatActivity:
            return CheckCombatActivityInLayer(Layer);

        case EAuracronEvolutionTrigger::SigilActivation:
            return CheckSigilActivityInLayer(Layer);

        case EAuracronEvolutionTrigger::RailUsage:
            return CheckRailUsageInLayer(Layer);

        case EAuracronEvolutionTrigger::IslandActivation:
            return CheckIslandActivityInLayer(Layer);

        case EAuracronEvolutionTrigger::TimeProgression:
            return true; // Always active

        case EAuracronEvolutionTrigger::WeatherEvents:
            return CheckWeatherEventsInLayer(Layer);

        case EAuracronEvolutionTrigger::DayNightCycle:
            return true; // Always active

        case EAuracronEvolutionTrigger::SeasonalChange:
            return CheckSeasonalChangeInLayer(Layer);

        default:
            return false;
    }
}

void UAuracronDynamicRealmSubsystem::CheckStageProgression(EAuracronRealmLayer Layer, FAuracronRealmLayerEvolutionData& EvolutionData)
{
    // Check if layer should progress to next evolution stage
    if (EvolutionData.EvolutionProgress >= 1.0f)
    {
        EAuracronLayerEvolutionStage NextStage = this->GetNextEvolutionStage(EvolutionData.EvolutionStage);

        if (NextStage != EvolutionData.EvolutionStage)
        {
            ProgressToNextStage(Layer, EvolutionData, NextStage);
        }
    }
}

// GetNextEvolutionStage implementation already exists above

void UAuracronDynamicRealmSubsystem::ProgressToNextStage(EAuracronRealmLayer Layer, FAuracronRealmLayerEvolutionData& EvolutionData, EAuracronLayerEvolutionStage NextStage)
{
    // Progress layer to next evolution stage using UE 5.6 progression system
    EAuracronLayerEvolutionStage PreviousStage = EvolutionData.EvolutionStage;
    EvolutionData.EvolutionStage = NextStage;
    EvolutionData.EvolutionProgress = 0.0f; // Reset progress for next stage
    EvolutionData.StageStartTime = GetWorld()->GetTimeSeconds();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Layer %s evolved from %s to %s"),
        *UEnum::GetValueAsString(Layer),
        *UEnum::GetValueAsString(PreviousStage),
        *UEnum::GetValueAsString(NextStage));

    // Apply stage evolution effects
    ApplyEvolutionStageEffects(Layer, NextStage);

    // Play evolution effects
    PlayLayerEvolutionEffects(Layer, NextStage);

    // Update global evolution tracking
    UpdateGlobalEvolutionTracking(Layer, NextStage);

    // Broadcast evolution event
    OnLayerEvolutionStageChanged.Broadcast(Layer, PreviousStage, NextStage);
}

void UAuracronDynamicRealmSubsystem::ApplyEvolutionEffects(EAuracronRealmLayer Layer, const FAuracronRealmLayerEvolutionData& EvolutionData)
{
    // Apply ongoing evolution effects based on current stage
    TArray<AActor*> LayerActors = this->GetActorsInLayer(Layer);

    for (AActor* Actor : LayerActors)
    {
        if (Actor)
        {
            this->ApplyStageEffectsToActor(Actor, Layer, EvolutionData.EvolutionStage, EvolutionData.EvolutionProgress);
        }
    }
}

void UAuracronDynamicRealmSubsystem::PlayLayerEvolutionEffects(EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage)
{
    if (!GetWorld())
    {
        return;
    }

    // Play layer evolution effects using UE 5.6 effect system
    FVector LayerCenter = GetLayerCenter(Layer);

    // Play evolution VFX
    FString VFXPath = FString::Printf(TEXT("/Game/VFX/Evolution/%s/NS_%sEvolution_%s.NS_%sEvolution_%s"),
        *UEnum::GetValueAsString(Layer),
        *UEnum::GetValueAsString(Layer),
        *UEnum::GetValueAsString(Stage),
        *UEnum::GetValueAsString(Layer),
        *UEnum::GetValueAsString(Stage));

    FSoftObjectPath EvolutionVFXPath(VFXPath);
    if (UNiagaraSystem* EvolutionVFX = Cast<UNiagaraSystem>(EvolutionVFXPath.TryLoad()))
    {
        UNiagaraComponent* EvolutionComponent = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            EvolutionVFX,
            LayerCenter,
            FRotator::ZeroRotator,
            FVector::OneVector,
            true, // Auto-destroy
            true, // Auto-activate
            ENCPoolMethod::None
        );

        if (EvolutionComponent)
        {
            EvolutionComponent->SetVariableLinearColor(FName("EvolutionColor"), GetLayerEvolutionColor(Layer, Stage));
            EvolutionComponent->SetVariableFloat(FName("EvolutionIntensity"), GetStageIntensityMultiplier(Stage));

            // Store for cleanup
            EvolutionVFXComponents.Add(Layer, EvolutionComponent);
        }
    }

    // Play evolution audio
    FString AudioPath = FString::Printf(TEXT("/Game/Audio/Evolution/%s/MS_%sEvolution_%s.MS_%sEvolution_%s"),
        *UEnum::GetValueAsString(Layer),
        *UEnum::GetValueAsString(Layer),
        *UEnum::GetValueAsString(Stage),
        *UEnum::GetValueAsString(Layer),
        *UEnum::GetValueAsString(Stage));

    FSoftObjectPath EvolutionAudioPath(AudioPath);
    if (UMetaSoundSource* EvolutionAudio = Cast<UMetaSoundSource>(EvolutionAudioPath.TryLoad()))
    {
        UGameplayStatics::PlaySoundAtLocation(
            GetWorld(),
            EvolutionAudio,
            LayerCenter,
            1.0f, // Volume
            1.0f  // Pitch
        );
    }
}

// === Evolution Utility Methods ===

// GetActivePlayersInLayer implementation already exists above - removing duplicate

bool UAuracronDynamicRealmSubsystem::CheckCombatActivityInLayer(EAuracronRealmLayer Layer) const
{
    // Check for combat activity in layer (simplified implementation)
    TArray<AActor*> LayerActors = GetActorsInLayer(Layer);

    for (AActor* Actor : LayerActors)
    {
        if (APawn* Pawn = Cast<APawn>(Actor))
        {
            if (UAbilitySystemComponent* ASC = Pawn->FindComponentByClass<UAbilitySystemComponent>())
            {
                // Check for combat tags
                if (ASC->HasMatchingGameplayTag(FGameplayTag::RequestGameplayTag(TEXT("State.Combat"))))
                {
                    return true;
                }
            }
        }
    }

    return false;
}

bool UAuracronDynamicRealmSubsystem::CheckSigilActivityInLayer(EAuracronRealmLayer Layer) const
{
    // Check for sigil activity in layer
    // This would integrate with the sigil system when implemented
    return false; // Placeholder
}

bool UAuracronDynamicRealmSubsystem::CheckRailUsageInLayer(EAuracronRealmLayer Layer) const
{
    // Check for rail usage in layer
    TArray<AAuracronDynamicRail*> LayerRails = GetActiveRailsInLayer(Layer);

    for (AAuracronDynamicRail* Rail : LayerRails)
    {
        if (Rail && Rail->IsRailActive() && Rail->GetActivePlayerCount() > 0)
        {
            return true;
        }
    }

    return false;
}

bool UAuracronDynamicRealmSubsystem::CheckIslandActivityInLayer(EAuracronRealmLayer Layer) const
{
    // Check for island activity in layer
    if (PrismalFlowActor)
    {
        for (int32 IslandTypeInt = 0; IslandTypeInt < 4; IslandTypeInt++)
        {
            EPrismalIslandType IslandType = static_cast<EPrismalIslandType>(IslandTypeInt);
            TArray<AAuracronPrismalIsland*> Islands = PrismalFlowActor->GetIslandsByType(IslandType);

            for (AAuracronPrismalIsland* Island : Islands)
            {
                if (Island && Island->IsIslandActive())
                {
                    // Check if island is in the specified layer
                    EAuracronRealmLayer IslandLayer = GetActorLayer(Island);
                    if (IslandLayer == Layer)
                    {
                        return true;
                    }
                }
            }
        }
    }

    return false;
}

bool UAuracronDynamicRealmSubsystem::CheckWeatherEventsInLayer(EAuracronRealmLayer Layer) const
{
    // Check for weather events in layer (simplified)
    return FMath::RandBool(); // Random weather events for now
}

bool UAuracronDynamicRealmSubsystem::CheckSeasonalChangeInLayer(EAuracronRealmLayer Layer) const
{
    // Check for seasonal changes in layer (simplified)
    return false; // Placeholder for seasonal system
}

// IsCurrentlyNightTime implementation already exists above - removing duplicate

FVector UAuracronDynamicRealmSubsystem::GetLayerCenter(EAuracronRealmLayer Layer) const
{
    // Get center point of layer
    if (const FAuracronRealmLayerData* LayerData = RealmLayers.Find(Layer))
    {
        return LayerData->LayerBounds.GetCenter();
    }

    // Fallback to layer height
    switch (Layer)
    {
        case EAuracronRealmLayer::Terrestrial:
            return FVector(0.0f, 0.0f, AuracronDynamicRealmConstants::TERRESTRIAL_LAYER_HEIGHT);
        case EAuracronRealmLayer::Celestial:
            return FVector(0.0f, 0.0f, AuracronDynamicRealmConstants::CELESTIAL_LAYER_HEIGHT);
        case EAuracronRealmLayer::Abyssal:
            return FVector(0.0f, 0.0f, AuracronDynamicRealmConstants::ABYSSAL_LAYER_HEIGHT);
        default:
            return FVector::ZeroVector;
    }
}

FLinearColor UAuracronDynamicRealmSubsystem::GetLayerEvolutionColor(EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage) const
{
    // Get color for layer evolution stage
    FLinearColor BaseColor;

    switch (Layer)
    {
        case EAuracronRealmLayer::Terrestrial:
            BaseColor = FLinearColor(0.9f, 0.7f, 0.3f, 1.0f); // Golden
            break;
        case EAuracronRealmLayer::Celestial:
            BaseColor = FLinearColor(0.6f, 0.8f, 1.0f, 1.0f); // Sky blue
            break;
        case EAuracronRealmLayer::Abyssal:
            BaseColor = FLinearColor(0.4f, 0.2f, 0.8f, 1.0f); // Deep purple
            break;
        default:
            BaseColor = FLinearColor::White;
            break;
    }

    // Modify intensity based on stage
    float Intensity = GetStageIntensityMultiplier(Stage);
    return BaseColor * Intensity;
}

// GetStageIntensityMultiplier implementation already exists above - removing duplicate

void UAuracronDynamicRealmSubsystem::UpdateGlobalEvolutionTracking(EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage)
{
    // Update global evolution tracking when layer evolves
    GlobalEvolutionData.TotalEvolutionTime = GetWorld()->GetTimeSeconds();

    // Count awakened layers
    int32 AwakenedLayers = 0;
    int32 TranscendentLayers = 0;

    for (const auto& EvolutionPair : LayerEvolutionData)
    {
        EAuracronLayerEvolutionStage LayerStage = EvolutionPair.Value.EvolutionStage;

        if (LayerStage >= EAuracronLayerEvolutionStage::Awakening)
        {
            AwakenedLayers++;
        }

        if (LayerStage == EAuracronLayerEvolutionStage::Transcendent)
        {
            TranscendentLayers++;
        }
    }

    GlobalEvolutionData.LayersAwakened = AwakenedLayers;
    GlobalEvolutionData.LayersTranscendent = TranscendentLayers;
    GlobalEvolutionData.GlobalEvolutionLevel = (AwakenedLayers * 20) + (TranscendentLayers * 30);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Global evolution updated - Awakened: %d, Transcendent: %d, Level: %d"),
        AwakenedLayers, TranscendentLayers, GlobalEvolutionData.GlobalEvolutionLevel);
}

// CheckGlobalEvolutionEvents implementation already exists above - removing duplicate

void UAuracronDynamicRealmSubsystem::ApplyStageEffectsToActor(AActor* Actor, EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage Stage, float Progress)
{
    if (!Actor)
    {
        return;
    }

    // Apply stage effects to actor using UE 5.6 effect application
    if (UAbilitySystemComponent* ASC = Actor->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Remove previous stage effects
        RemoveActorEvolutionEffects(Actor);

        // Apply new stage effects
        FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        FString EffectPath = FString::Printf(TEXT("/Game/GameplayEffects/Evolution/GE_%s_%s.GE_%s_%s_C"),
            *UEnum::GetValueAsString(Layer),
            *UEnum::GetValueAsString(Stage),
            *UEnum::GetValueAsString(Layer),
            *UEnum::GetValueAsString(Stage));

        FSoftClassPath EvolutionEffectPath(EffectPath);
        if (TSubclassOf<UGameplayEffect> EvolutionEffect = EvolutionEffectPath.TryLoadClass<UGameplayEffect>())
        {
            FGameplayEffectSpec EvolutionSpec(EvolutionEffect.GetDefaultObject(), EffectContext, 1.0f);

            // Set stage parameters
            EvolutionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Evolution.Stage.Intensity")), GetStageIntensityMultiplier(Stage));
            EvolutionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Evolution.Progress")), Progress);
            EvolutionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Evolution.Layer.Bonus")), static_cast<float>(Layer) + 1.0f);

            EvolutionSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

            FActiveGameplayEffectHandle EvolutionHandle = ASC->ApplyGameplayEffectSpecToSelf(EvolutionSpec);
            ActorEvolutionEffects.Add(Actor, EvolutionHandle);
        }
    }
}

void UAuracronDynamicRealmSubsystem::RemoveActorEvolutionEffects(AActor* Actor)
{
    if (!Actor)
    {
        return;
    }

    // Remove evolution effects from actor
    if (UAbilitySystemComponent* ASC = Actor->FindComponentByClass<UAbilitySystemComponent>())
    {
        if (FActiveGameplayEffectHandle* EvolutionHandle = ActorEvolutionEffects.Find(Actor))
        {
            ASC->RemoveActiveGameplayEffect(*EvolutionHandle);
            ActorEvolutionEffects.Remove(Actor);
        }
    }
}

// === Public Evolution API Implementation ===

// GetLayerEvolutionData implementation already exists above with correct return type - removing duplicate

void UAuracronDynamicRealmSubsystem::SetLayerEvolutionSpeed(EAuracronRealmLayer Layer, float Speed)
{
    // Set evolution speed for specific layer
    FAuracronRealmLayerEvolutionData* EvolutionData = LayerEvolutionData.Find(Layer);
    if (EvolutionData)
    {
        EvolutionData->EvolutionRate = FMath::Clamp(Speed, 0.1f, 5.0f);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Evolution speed for %s layer set to %.2f"),
            *UEnum::GetValueAsString(Layer), Speed);
    }
}

void UAuracronDynamicRealmSubsystem::EnableLayerAutoEvolution(EAuracronRealmLayer Layer, bool bEnable)
{
    // Enable/disable auto evolution for layer
    FAuracronRealmLayerEvolutionData* EvolutionData = LayerEvolutionData.Find(Layer);
    if (EvolutionData)
    {
        EvolutionData->bAutoEvolution = bEnable;

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Auto evolution for %s layer %s"),
            *UEnum::GetValueAsString(Layer), bEnable ? TEXT("enabled") : TEXT("disabled"));
    }
}

FAuracronGlobalEvolutionData UAuracronDynamicRealmSubsystem::GetGlobalEvolutionData() const
{
    return GlobalEvolutionData;
}

EAuracronRealmLayer UAuracronDynamicRealmSubsystem::GetLocationLayer(const FVector& Location) const
{
    // Determine layer based on Z coordinate and realm configuration
    if (Location.Z > 5000.0f)
    {
        return EAuracronRealmLayer::Celestial;
    }
    else if (Location.Z < -2000.0f)
    {
        return EAuracronRealmLayer::Abyssal;
    }
    else
    {
        return EAuracronRealmLayer::Terrestrial;
    }
}

// GetLayerCenter implementation already exists above - removing duplicate

void UAuracronDynamicRealmSubsystem::SetCreatureSpawnRateMultiplier(float Multiplier)
{
    if (!bIsInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot set creature spawn rate - subsystem not initialized"));
        return;
    }

    // Clamp multiplier to reasonable range
    float ClampedMultiplier = FMath::Clamp(Multiplier, 0.1f, 5.0f);

    // Store the multiplier for use by AI systems
    for (auto& LayerPair : LayerEvolutionData)
    {
        LayerPair.Value.CreatureSpawnRateMultiplier = ClampedMultiplier;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Set creature spawn rate multiplier to %.2f"), ClampedMultiplier);

    // Notify AI systems of the change
    OnCreatureSpawnRateChanged.Broadcast(ClampedMultiplier);
}

void UAuracronDynamicRealmSubsystem::SetEnvironmentalHazardIntensity(float Intensity)
{
    if (!bIsInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot set hazard intensity - subsystem not initialized"));
        return;
    }

    // Clamp intensity to reasonable range
    float ClampedIntensity = FMath::Clamp(Intensity, 0.0f, 2.0f);

    // Store the intensity for use by environmental systems
    for (auto& LayerPair : LayerEvolutionData)
    {
        LayerPair.Value.EnvironmentalHazardIntensity = ClampedIntensity;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Set environmental hazard intensity to %.2f"), ClampedIntensity);

    // Notify environmental systems of the change
    OnEnvironmentalHazardIntensityChanged.Broadcast(ClampedIntensity);
}

void UAuracronDynamicRealmSubsystem::SetResourceDensityMultiplier(float Multiplier)
{
    if (!bIsInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot set resource density - subsystem not initialized"));
        return;
    }

    // Clamp multiplier to reasonable range
    float ClampedMultiplier = FMath::Clamp(Multiplier, 0.1f, 3.0f);

    // Store the multiplier for use by resource systems
    for (auto& LayerPair : LayerEvolutionData)
    {
        LayerPair.Value.ResourceDensityMultiplier = ClampedMultiplier;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Set resource density multiplier to %.2f"), ClampedMultiplier);

    // Notify resource systems of the change
    OnResourceDensityChanged.Broadcast(ClampedMultiplier);
}

float UAuracronDynamicRealmSubsystem::CalculateGlobalEvolutionProgress() const
{
    if (LayerEvolutionData.Num() == 0)
    {
        return 0.0f;
    }

    float TotalProgress = 0.0f;
    int32 ValidLayers = 0;

    for (const auto& LayerPair : LayerEvolutionData)
    {
        if (LayerPair.Key != EAuracronRealmLayer::None)
        {
            TotalProgress += LayerPair.Value.EvolutionProgress;
            ValidLayers++;
        }
    }

    return ValidLayers > 0 ? TotalProgress / ValidLayers : 0.0f;
}
