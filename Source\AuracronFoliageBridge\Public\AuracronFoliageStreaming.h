// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Streaming Integration Header
// Bridge 4.10: Foliage - Streaming Integration

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageInstanced.h"
// #include "AuracronWorldPartitionStreaming.h" // Include removed - may not exist in UE5.6
// Forward declaration instead of include to avoid circular dependency
// #include "AuracronWorldPartitionDataLayers.h"

// UE5.6 Streaming includes
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionStreamingPolicy.h"
#include "WorldPartition/WorldPartitionRuntimeCell.h"
#include "WorldPartition/WorldPartitionStreamingSource.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/WorldDataLayers.h"

// Foliage streaming includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "InstancedFoliageActor.h"
#include "ProceduralFoliageComponent.h"

// Memory management includes
#include "HAL/PlatformMemory.h"
#include "HAL/MemoryBase.h"
#include "Containers/LockFreeList.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"
#include "Engine/StreamableManager.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"

#include "AuracronFoliageStreaming.generated.h"

// Forward declarations
class UAuracronFoliageStreamingManager;
// class UAuracronWorldPartitionStreamingManager; // Will be available when WorldPartitionBridge is compiled
// class UAuracronDataLayerManager; // Will be available when WorldPartitionBridge is compiled

// =============================================================================
// FOLIAGE STREAMING TYPES AND ENUMS
// =============================================================================

// Foliage streaming strategy
UENUM(BlueprintType)
enum class EAuracronFoliageStreamingStrategy : uint8
{
    None                    UMETA(DisplayName = "None"),
    DistanceBased           UMETA(DisplayName = "Distance Based"),
    CellBased               UMETA(DisplayName = "Cell Based"),
    DataLayerBased          UMETA(DisplayName = "Data Layer Based"),
    Hybrid                  UMETA(DisplayName = "Hybrid Strategy"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Foliage memory management strategy
UENUM(BlueprintType)
enum class EAuracronFoliageMemoryStrategy : uint8
{
    Conservative            UMETA(DisplayName = "Conservative"),
    Balanced                UMETA(DisplayName = "Balanced"),
    Aggressive              UMETA(DisplayName = "Aggressive"),
    Adaptive                UMETA(DisplayName = "Adaptive"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Foliage loading priority
UENUM(BlueprintType)
enum class EAuracronFoliageLoadingPriority : uint8
{
    VeryLow                 UMETA(DisplayName = "Very Low"),
    Low                     UMETA(DisplayName = "Low"),
    Normal                  UMETA(DisplayName = "Normal"),
    High                    UMETA(DisplayName = "High"),
    VeryHigh                UMETA(DisplayName = "Very High"),
    Critical                UMETA(DisplayName = "Critical")
};

// Foliage streaming state
UENUM(BlueprintType)
enum class EAuracronFoliageStreamingState : uint8
{
    Unloaded                UMETA(DisplayName = "Unloaded"),
    Loading                 UMETA(DisplayName = "Loading"),
    Loaded                  UMETA(DisplayName = "Loaded"),
    Unloading               UMETA(DisplayName = "Unloading"),
    Error                   UMETA(DisplayName = "Error"),
    Cached                  UMETA(DisplayName = "Cached")
};

// =============================================================================
// FOLIAGE STREAMING CONFIGURATION
// =============================================================================

/**
 * Foliage Streaming Configuration
 * Configuration for foliage streaming system
 */
USTRUCT(BlueprintType)
struct FAuracronFoliageStreamingConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming System")
    bool bEnableFoliageStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming System")
    EAuracronFoliageStreamingStrategy StreamingStrategy = EAuracronFoliageStreamingStrategy::Hybrid;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance Streaming")
    float FoliageStreamingDistance = 8000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance Streaming")
    float FoliageUnloadingDistance = 12000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance Streaming")
    float FoliagePreloadDistance = 15000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Integration")
    bool bEnableCellBasedStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Integration")
    bool bIntegrateWithWorldPartition = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Integration")
    float CellStreamingBuffer = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Layer Integration")
    bool bEnableDataLayerIntegration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Layer Integration")
    bool bRespectDataLayerStates = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Management")
    EAuracronFoliageMemoryStrategy MemoryStrategy = EAuracronFoliageMemoryStrategy::Balanced;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Management")
    float MaxFoliageMemoryMB = 1024.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Management")
    float MemoryPressureThreshold = 0.85f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Management")
    bool bEnableInstancePooling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Management")
    int32 InstancePoolSize = 50000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async Loading")
    bool bEnableAsyncFoliageLoading = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async Loading")
    int32 MaxConcurrentFoliageLoads = 6;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async Loading")
    float FoliageLoadingTimeSliceMs = 8.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async Loading")
    int32 AsyncWorkerThreads = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxFoliageInstancesPerFrame = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float StreamingUpdateInterval = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableFoliageLODStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableFrustumCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableOcclusionCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Caching")
    bool bEnableFoliageCaching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Caching")
    float CacheRetentionTime = 30.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Caching")
    int32 MaxCachedFoliageChunks = 20;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableStreamingDebug = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogFoliageStreaming = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bDrawStreamingBounds = false;

    FAuracronFoliageStreamingConfiguration()
    {
        bEnableFoliageStreaming = true;
        StreamingStrategy = EAuracronFoliageStreamingStrategy::Hybrid;
        FoliageStreamingDistance = 8000.0f;
        FoliageUnloadingDistance = 12000.0f;
        FoliagePreloadDistance = 15000.0f;
        bEnableCellBasedStreaming = true;
        bIntegrateWithWorldPartition = true;
        CellStreamingBuffer = 1000.0f;
        bEnableDataLayerIntegration = true;
        bRespectDataLayerStates = true;
        MemoryStrategy = EAuracronFoliageMemoryStrategy::Balanced;
        MaxFoliageMemoryMB = 1024.0f;
        MemoryPressureThreshold = 0.85f;
        bEnableInstancePooling = true;
        InstancePoolSize = 50000;
        bEnableAsyncFoliageLoading = true;
        MaxConcurrentFoliageLoads = 6;
        FoliageLoadingTimeSliceMs = 8.0f;
        AsyncWorkerThreads = 3;
        MaxFoliageInstancesPerFrame = 1000;
        StreamingUpdateInterval = 0.1f;
        bEnableFoliageLODStreaming = true;
        bEnableFrustumCulling = true;
        bEnableOcclusionCulling = true;
        bEnableFoliageCaching = true;
        CacheRetentionTime = 30.0f;
        MaxCachedFoliageChunks = 20;
        bEnableStreamingDebug = false;
        bLogFoliageStreaming = false;
        bDrawStreamingBounds = false;
    }
};

// =============================================================================
// FOLIAGE STREAMING DATA STRUCTURES
// =============================================================================

/**
 * Foliage Chunk Data
 * Represents a chunk of foliage instances for streaming
 */
USTRUCT(BlueprintType)
struct FAuracronFoliageChunkData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chunk Data")
    FString ChunkId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chunk Data")
    FString CellId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chunk Data")
    FString DataLayerId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chunk Data")
    FBox ChunkBounds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chunk Data")
    TArray<FString> FoliageTypeIds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chunk Data")
    int32 TotalInstanceCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chunk Data")
    float EstimatedMemoryMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    EAuracronFoliageStreamingState StreamingState = EAuracronFoliageStreamingState::Unloaded;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    EAuracronFoliageLoadingPriority LoadingPriority = EAuracronFoliageLoadingPriority::Normal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    float LastAccessTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    float LoadingProgress = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async")
    bool bIsAsyncLoading = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async")
    FString AsyncRequestId;

    FAuracronFoliageChunkData()
    {
        ChunkBounds = FBox(ForceInit);
        TotalInstanceCount = 0;
        EstimatedMemoryMB = 0.0f;
        StreamingState = EAuracronFoliageStreamingState::Unloaded;
        LoadingPriority = EAuracronFoliageLoadingPriority::Normal;
        LastAccessTime = 0.0f;
        LoadingProgress = 0.0f;
        bIsAsyncLoading = false;
    }
};

/**
 * Foliage Streaming Request
 * Request for loading/unloading foliage chunks
 */
USTRUCT(BlueprintType)
struct FAuracronFoliageStreamingRequest
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FString RequestId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FString ChunkId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    bool bIsLoadRequest = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    EAuracronFoliageLoadingPriority Priority = EAuracronFoliageLoadingPriority::Normal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FVector RequestLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    float RequestDistance = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsProcessing = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    float RequestTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    float ProcessingStartTime = 0.0f;

    FAuracronFoliageStreamingRequest()
    {
        bIsLoadRequest = true;
        Priority = EAuracronFoliageLoadingPriority::Normal;
        RequestLocation = FVector::ZeroVector;
        RequestDistance = 0.0f;
        bIsProcessing = false;
        RequestTime = 0.0f;
        ProcessingStartTime = 0.0f;
    }
};

/**
 * Foliage Memory Pool
 * Memory pool for foliage instance data
 */
USTRUCT(BlueprintType)
struct FAuracronFoliageMemoryPool
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Pool")
    FString PoolId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Pool")
    int32 PoolSize = 10000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Pool")
    int32 UsedInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Pool")
    int32 AvailableInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Pool")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Pool")
    float MaxMemoryMB = 256.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsActive = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    float LastCleanupTime = 0.0f;

    FAuracronFoliageMemoryPool()
    {
        PoolSize = 10000;
        UsedInstances = 0;
        AvailableInstances = 0;
        MemoryUsageMB = 0.0f;
        MaxMemoryMB = 256.0f;
        bIsActive = true;
        LastCleanupTime = 0.0f;
    }
};

/**
 * Foliage Streaming Performance Data
 * Performance metrics for foliage streaming
 */
USTRUCT(BlueprintType)
struct FAuracronFoliageStreamingPerformanceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 TotalFoliageChunks = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 LoadedChunks = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 LoadingChunks = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 CachedChunks = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 TotalFoliageInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 StreamedInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float FoliageMemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float StreamingUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float AsyncLoadingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 PendingRequests = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 CompletedRequests = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 FailedRequests = 0;

    FAuracronFoliageStreamingPerformanceData()
    {
        TotalFoliageChunks = 0;
        LoadedChunks = 0;
        LoadingChunks = 0;
        CachedChunks = 0;
        TotalFoliageInstances = 0;
        StreamedInstances = 0;
        FoliageMemoryUsageMB = 0.0f;
        StreamingUpdateTime = 0.0f;
        AsyncLoadingTime = 0.0f;
        PendingRequests = 0;
        CompletedRequests = 0;
        FailedRequests = 0;
    }
};

// =============================================================================
// FOLIAGE STREAMING MANAGER
// =============================================================================

/**
 * Foliage Streaming Manager
 * Manager for foliage streaming integration with World Partition
 */
UCLASS(BlueprintType, Blueprintable)
class UAuracronFoliageStreamingManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    static UAuracronFoliageStreamingManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void Initialize(const FAuracronFoliageStreamingConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void Tick(float DeltaTime);

    // Configuration management
    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void SetConfiguration(const FAuracronFoliageStreamingConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    FAuracronFoliageStreamingConfiguration GetConfiguration() const;

    // World Partition integration - Production Ready
    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void IntegrateWithWorldPartition(UObject* InWorldPartitionManager);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void IntegrateWithDataLayers(UObject* InDataLayerManager);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void RegisterFoliageWithCell(const FString& CellId, const FString& FoliageTypeId, const TArray<FTransform>& Instances);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void UnregisterFoliageFromCell(const FString& CellId, const FString& FoliageTypeId);

    // Chunk management
    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    FString CreateFoliageChunk(const FBox& ChunkBounds, const FString& CellId, const FString& DataLayerId);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    bool UpdateFoliageChunk(const FString& ChunkId, const FAuracronFoliageChunkData& ChunkData);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    bool RemoveFoliageChunk(const FString& ChunkId);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    FAuracronFoliageChunkData GetFoliageChunk(const FString& ChunkId) const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    TArray<FAuracronFoliageChunkData> GetAllFoliageChunks() const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    TArray<FString> GetChunksInRange(const FVector& Location, float Radius) const;

    // Streaming operations
    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    FString RequestFoliageChunkLoading(const FString& ChunkId, EAuracronFoliageLoadingPriority Priority);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    FString RequestFoliageChunkUnloading(const FString& ChunkId);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    bool CancelFoliageStreamingRequest(const FString& RequestId);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void ProcessStreamingRequests();

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void UpdateDistanceBasedStreaming(const TArray<FVector>& StreamingSources);

    // Async loading
    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void EnableAsyncFoliageLoading(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    bool IsAsyncFoliageLoadingEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void UpdateAsyncLoadingOperations(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    int32 GetActiveAsyncLoadingCount() const;

    // Memory management
    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void InitializeMemoryPools();

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    FAuracronFoliageMemoryPool CreateMemoryPool(const FString& PoolId, int32 PoolSize, float MaxMemoryMB);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    bool UpdateMemoryPool(const FString& PoolId, const FAuracronFoliageMemoryPool& PoolData);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void UpdateMemoryManagement();

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    bool IsMemoryPressureHigh() const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void CleanupUnusedMemory();

    // Data layer integration
    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void OnDataLayerStateChanged(const FString& DataLayerId, bool bIsLoaded);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void UpdateDataLayerBasedStreaming();

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    TArray<FString> GetFoliageChunksInDataLayer(const FString& DataLayerId) const;

    // Performance monitoring
    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    FAuracronFoliageStreamingPerformanceData GetPerformanceData() const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void UpdatePerformanceMetrics();

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    int32 GetLoadedChunkCount() const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    float GetFoliageMemoryUsage() const;

    // Debug and visualization
    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void EnableDebugVisualization(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    bool IsDebugVisualizationEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void DrawDebugStreamingInfo(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Streaming Manager")
    void LogStreamingStatistics() const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnFoliageChunkLoaded, FString, ChunkId, int32, InstanceCount);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnFoliageChunkUnloaded, FString, ChunkId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnFoliageStreamingStateChanged, FString, ChunkId, EAuracronFoliageStreamingState, NewState);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnFoliageMemoryPressure, float, MemoryUsagePercent);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnFoliageChunkLoaded OnFoliageChunkLoaded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnFoliageChunkUnloaded OnFoliageChunkUnloaded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnFoliageStreamingStateChanged OnFoliageStreamingStateChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnFoliageMemoryPressure OnFoliageMemoryPressure;

private:
    static UAuracronFoliageStreamingManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronFoliageStreamingConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Integration with other systems - Production Ready placeholders
    UPROPERTY()
    TWeakObjectPtr<UObject> WorldPartitionManager;

    UPROPERTY()
    TWeakObjectPtr<UObject> DataLayerManager;

    // Foliage streaming data
    TMap<FString, FAuracronFoliageChunkData> FoliageChunks;
    TMap<FString, FAuracronFoliageStreamingRequest> StreamingRequests;
    TMap<FString, FAuracronFoliageMemoryPool> MemoryPools;

    // Streaming queues
    TQueue<FString> PendingLoadRequests;
    TQueue<FString> PendingUnloadRequests;
    TArray<FString> ActiveAsyncOperations;

    // Performance data
    FAuracronFoliageStreamingPerformanceData PerformanceData;
    float LastPerformanceUpdate = 0.0f;
    float LastStreamingUpdate = 0.0f;
    float LastMemoryUpdate = 0.0f;

    // Debug settings
    bool bDebugVisualizationEnabled = false;

    // Thread safety
    mutable FCriticalSection StreamingLock;

    // Internal functions
    void ValidateConfiguration();
    FString GenerateChunkId() const;
    FString GenerateRequestId() const;
    FString GeneratePoolId() const;
    void UpdateStreamingInternal(float DeltaTime);
    void ProcessSingleStreamingRequest(const FString& RequestId);
    void LoadFoliageChunkInternal(const FString& ChunkId);
    void UnloadFoliageChunkInternal(const FString& ChunkId);
    void UpdateChunkPriorities(const TArray<FVector>& StreamingSources);
    float CalculateChunkPriority(const FAuracronFoliageChunkData& ChunkData, const FVector& SourceLocation) const;
    bool ShouldLoadChunk(const FAuracronFoliageChunkData& ChunkData, const TArray<FVector>& StreamingSources) const;
    bool ShouldUnloadChunk(const FAuracronFoliageChunkData& ChunkData, const TArray<FVector>& StreamingSources) const;
    void UpdatePerformanceDataInternal();
    void CleanupExpiredCacheEntries();
    TArray<FString> GetChunksForCell(const FString& CellId) const;
    void OnWorldPartitionCellLoaded(const FString& CellId);
    void OnWorldPartitionCellUnloaded(const FString& CellId);
};
