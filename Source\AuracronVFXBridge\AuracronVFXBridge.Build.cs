﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de PartÃ­culas e Efeitos Visuais Bridge Build Configuration
using UnrealBuildTool;
public class AuracronVFXBridge : ModuleRules
{
    public AuracronVFXBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "RenderCore",
                "RHI","NiagaraCore",
                "NiagaraShader",
                "NiagaraAnimNotifies","Landscape",
                "Foliage",
                "GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "ModularGameplay",
                "NetCore",
                "ReplicationGraph",
                "ChaosCore","PhysicsCore","EnhancedInput",
                "InputCore",
                "UMG",
                "Slate",
                "SlateCore","DeveloperSettings",
                "EngineSettings"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "RenderCore",
                "RHI",
                "Json",
                "AudioMixer",
                "SignalProcessing",
                "MovieScene",
                "MovieSceneTracks",
                "CinematicCamera",
                "MediaAssets",
                "MediaUtils",
                "ImageWrapper",
                "ImageCore"
            }
        );

        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "ToolMenus",
                    "EditorStyle",
                    "EditorWidgets",
                    "UnrealEd",
                    "PropertyEditor",
                    "KismetCompiler",
                    "BlueprintGraph",
                    "Kismet",
                    "NiagaraEditorWidgets",
                    "MaterialEditor",
                    "LevelEditor",
                    "SceneOutliner",
                    "DetailCustomizations",
                    "ComponentVisualizers",
                    "GraphEditor",
                    "KismetWidgets",
                    "SequencerWidgets"
                }
            );
        }
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {}
        );
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_NIAGARA=1");
        PublicDefinitions.Add("WITH_CASCADE=1");
        PublicDefinitions.Add("WITH_CHAOS_PHYSICS=1");
        PublicDefinitions.Add("WITH_FIELD_SYSTEM=1");
        PublicDefinitions.Add("WITH_GEOMETRY_COLLECTION=1");
        PublicDefinitions.Add("WITH_PROCEDURAL_GENERATION=1");
        PublicDefinitions.Add("WITH_DYNAMIC_MATERIALS=1");
        PublicDefinitions.Add("WITH_SHADER_COMPILATION=1");
        // VFX features
        PublicDefinitions.Add("AURACRON_NIAGARA_VFX=1");
        PublicDefinitions.Add("AURACRON_DYNAMIC_PARTICLES=1");
        PublicDefinitions.Add("AURACRON_REALM_VFX=1");
        PublicDefinitions.Add("AURACRON_ABILITY_VFX=1");
        PublicDefinitions.Add("AURACRON_CHAMPION_VFX=1");
        PublicDefinitions.Add("AURACRON_COMBAT_VFX=1");
        PublicDefinitions.Add("AURACRON_ENVIRONMENTAL_VFX=1");
        PublicDefinitions.Add("AURACRON_WEATHER_VFX=1");
        PublicDefinitions.Add("AURACRON_DESTRUCTION_VFX=1");
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_VFX=1");
            PublicDefinitions.Add("AURACRON_OPTIMIZED_PARTICLES=1");
            PublicDefinitions.Add("AURACRON_REDUCED_COMPLEXITY=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_VFX=0");
            PublicDefinitions.Add("AURACRON_OPTIMIZED_PARTICLES=0");
            PublicDefinitions.Add("AURACRON_REDUCED_COMPLEXITY=0");
        }
        // Development vs Shipping
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_VFX_DEBUG=1");
            PublicDefinitions.Add("AURACRON_VFX_PROFILING=1");
            PublicDefinitions.Add("AURACRON_PARTICLE_DEBUG=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_VFX_DEBUG=0");
            PublicDefinitions.Add("AURACRON_VFX_PROFILING=0");
            PublicDefinitions.Add("AURACRON_PARTICLE_DEBUG=0");
        }
        // Performance optimizations
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("AURACRON_OPTIMIZE_VFX=1");
            PublicDefinitions.Add("AURACRON_VFX_CULLING=1");
            PublicDefinitions.Add("AURACRON_PARTICLE_POOLING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_OPTIMIZE_VFX=0");
            PublicDefinitions.Add("AURACRON_VFX_CULLING=0");
            PublicDefinitions.Add("AURACRON_PARTICLE_POOLING=0");
        }
        // Quality settings
        PublicDefinitions.Add("AURACRON_VFX_SCALABILITY=1");
        PublicDefinitions.Add("AURACRON_ADAPTIVE_QUALITY=1");
        PublicDefinitions.Add("AURACRON_LOD_PARTICLES=1");
        PublicDefinitions.Add("AURACRON_DISTANCE_CULLING=1");
    }
}

