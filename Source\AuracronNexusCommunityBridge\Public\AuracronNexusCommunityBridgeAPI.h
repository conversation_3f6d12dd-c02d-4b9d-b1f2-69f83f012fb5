#pragma once

#include "CoreMinimal.h"

#ifndef AURACRONEXUSCOMMUNITYBRIDGE_API
    #ifdef AURACRONEXUSCOMMUNITYBRIDGE_EXPORTS
        #define AURACRONEXUSCOMMUNITYBRIDGE_API __declspec(dllexport)
    #else
        #define AURACRONEXUSCOMMUNITYBRIDGE_API __declspec(dllimport)
    #endif
#endif

// Alias for consistency
#ifndef AURACRONEXUSCOMMUNITYBRIDGE_API
    #define AURACRONEXUSCOMMUNITYBRIDGE_API AURACRONEXUSCOMMUNITYBRIDGE_API
#endif
