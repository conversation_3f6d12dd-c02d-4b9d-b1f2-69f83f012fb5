// AURACRON - Implementação do Bridge C++ para Sistema de Transições Verticais
// Integração com Unreal Engine 5.6 APIs
// Autor: Augment Agent
// Data: 2025-08-03
// Versão: 1.0.0

#include "AuracronVerticalTransitionsBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "NavigationSystem.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Particles/ParticleSystemComponent.h"
#include "Components/AudioComponent.h"
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"
#include "TimerManager.h"
#include "DrawDebugHelpers.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"

UAuracronVerticalTransitionsBridge::UAuracronVerticalTransitionsBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // 10 FPS para otimização

    // Configurações padrão
    bUse3DNavigation = true;
    TransitionDetectionRadius = 200.0f;
    MaxChannelingTime = 10.0f;
    bUseVisualEffects = true;
    bUseAudioEffects = true;
    GenerationSeed = 98765;
    
    // Inicializar configurações padrão de transições
    InitializeDefaultTransitionProperties();
}

void UAuracronVerticalTransitionsBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Transições Verticais"));

    // Validar configuração
    if (!ValidateSystemConfiguration())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Configuração do sistema inválida"));
        return;
    }

    // Inicializar sistema
    bSystemInitialized = InitializeTransitionSystem();
    
    if (bSystemInitialized)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Transições Verticais inicializado com sucesso"));
        
        // Construir rede de transições
        BuildTransitionNetwork();
        
        // Configurar timer de atualização
        if (GetWorld())
        {
            GetWorld()->GetTimerManager().SetTimer(UpdateTimer, this, 
                &UAuracronVerticalTransitionsBridge::UpdateTransitionSystem, 1.0f, true);
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Transições Verticais"));
    }
}

void UAuracronVerticalTransitionsBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timer
    if (GetWorld() && UpdateTimer.IsValid())
    {
        GetWorld()->GetTimerManager().ClearTimer(UpdateTimer);
    }
    
    // Cancelar todos os channelings
    for (auto& ChannelingPair : PlayersChanneling)
    {
        CancelTransitionChanneling(ChannelingPair.Key);
    }
    
    // Limpar rede de transições
    ClearTransitionNetwork();
    
    Super::EndPlay(EndPlayReason);
}

void UAuracronVerticalTransitionsBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    if (!bSystemInitialized)
    {
        return;
    }
    
    // Atualizar cooldowns
    UpdateTransitionCooldowns(DeltaTime);
    
    // Processar channelings ativos
    ProcessActiveChannelings(DeltaTime);
    
    // Detectar jogadores próximos a transições
    if (GetWorld() && GetWorld()->GetTimeSeconds() - LastPlayerDetectionTime > 0.5f)
    {
        DetectPlayersNearTransitions();
        LastPlayerDetectionTime = GetWorld()->GetTimeSeconds();
    }
}

// === Core Transition Management ===

bool UAuracronVerticalTransitionsBridge::CreateTransitionPoint(const FTransitionPointData& TransitionData)
{
    if (TransitionData.TransitionID.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: ID de transição não pode estar vazio"));
        return false;
    }
    
    FScopeLock Lock(&TransitionMutex);
    
    // Verificar se já existe
    for (const FTransitionPointData& ExistingTransition : TransitionNetwork.TransitionPoints)
    {
        if (ExistingTransition.TransitionID == TransitionData.TransitionID)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Transição %s já existe"), *TransitionData.TransitionID);
            return false;
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Criando ponto de transição %s"), *TransitionData.TransitionID);
    
    // Adicionar à rede
    TransitionNetwork.TransitionPoints.Add(TransitionData);
    TransitionNetwork.TotalTransitions++;
    
    // Criar componente visual se especificado
    if (TransitionData.TransitionMesh.IsValid())
    {
        CreateTransitionVisualComponent(TransitionData);
    }
    
    // Configurar efeitos
    if (bUseVisualEffects)
    {
        SetupVisualEffects(TransitionData.TransitionID);
    }
    
    if (bUseAudioEffects)
    {
        SetupAudioEffects(TransitionData.TransitionID);
    }
    
    // Atualizar navegação se necessário
    if (bUse3DNavigation)
    {
        UpdateNavigationForTransition(TransitionData);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Ponto de transição %s criado com sucesso"), *TransitionData.TransitionID);
    return true;
}

bool UAuracronVerticalTransitionsBridge::RemoveTransitionPoint(const FString& TransitionID)
{
    if (TransitionID.IsEmpty())
    {
        return false;
    }
    
    FScopeLock Lock(&TransitionMutex);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Removendo ponto de transição %s"), *TransitionID);
    
    // Encontrar e remover da rede
    int32 RemovedCount = TransitionNetwork.TransitionPoints.RemoveAll([&TransitionID](const FTransitionPointData& Transition)
    {
        return Transition.TransitionID == TransitionID;
    });
    
    if (RemovedCount > 0)
    {
        TransitionNetwork.TotalTransitions -= RemovedCount;
        
        // Remover componentes visuais
        if (UStaticMeshComponent* Component = TransitionComponents.FindRef(TransitionID))
        {
            Component->DestroyComponent();
            TransitionComponents.Remove(TransitionID);
        }
        
        // Remover componentes de efeitos
        if (UParticleSystemComponent* EffectComponent = EffectComponents.FindRef(TransitionID))
        {
            EffectComponent->DestroyComponent();
            EffectComponents.Remove(TransitionID);
        }
        
        // Remover componentes de áudio
        if (UAudioComponent* AudioComponent = AudioComponents.FindRef(TransitionID))
        {
            AudioComponent->Stop();
            AudioComponent->DestroyComponent();
            AudioComponents.Remove(TransitionID);
        }
        
        // Remover cooldown
        TransitionCooldowns.Remove(TransitionID);
        
        // Cancelar channeling se ativo
        for (auto It = PlayersChanneling.CreateIterator(); It; ++It)
        {
            if (It.Value() == TransitionID)
            {
                CancelTransitionChanneling(It.Key());
                It.RemoveCurrent();
            }
        }
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Ponto de transição %s removido"), *TransitionID);
        return true;
    }
    
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Ponto de transição %s não encontrado"), *TransitionID);
    return false;
}

bool UAuracronVerticalTransitionsBridge::ActivateTransition(const FString& TransitionID, APawn* Player)
{
    if (!Player || TransitionID.IsEmpty())
    {
        return false;
    }
    
    // Encontrar dados da transição
    FTransitionPointData* TransitionData = FindTransitionData(TransitionID);
    if (!TransitionData)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Transição %s não encontrada"), *TransitionID);
        return false;
    }
    
    // Verificar se está ativa
    if (!TransitionData->bIsActive)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Transição %s está desativada"), *TransitionID);
        return false;
    }
    
    // Verificar cooldown
    if (IsTransitionOnCooldown(TransitionID))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Transição %s está em cooldown"), *TransitionID);
        return false;
    }
    
    // Verificar se jogador pode usar
    if (!CanPlayerUseTransition(TransitionID, Player))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Jogador não pode usar transição %s"), *TransitionID);
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Ativando transição %s para jogador"), *TransitionID);
    
    // Verificar se requer channeling
    if (TransitionData->Properties.bRequiresChannel)
    {
        return StartTransitionChanneling(TransitionID, Player);
    }
    else
    {
        return ExecuteTransition(TransitionID, Player);
    }
}

bool UAuracronVerticalTransitionsBridge::ExecuteTransition(const FString& TransitionID, APawn* Player)
{
    if (!Player || TransitionID.IsEmpty())
    {
        return false;
    }
    
    FTransitionPointData* TransitionData = FindTransitionData(TransitionID);
    if (!TransitionData)
    {
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Executando transição %s"), *TransitionID);
    
    // Reproduzir efeito de ativação
    PlayTransitionEffect(TransitionID, true);
    
    // Calcular posição de destino
    FVector DestinationLocation = TransitionData->DestinationLocation;
    FRotator DestinationRotation = TransitionData->DestinationRotation;
    
    // Ajustar posição baseado no tipo de transição
    AdjustDestinationForTransitionType(TransitionData->TransitionType, DestinationLocation, DestinationRotation);
    
    // Executar transição baseado no tipo
    bool bSuccess = false;
    switch (TransitionData->TransitionType)
    {
        case EAuracronVerticalTransitionType::Portal:
            bSuccess = ExecuteTeleportTransition(Player, DestinationLocation, DestinationRotation);
            break;

        case EAuracronVerticalTransitionType::Elevator:
        case EAuracronVerticalTransitionType::FloatingPlatform:
            bSuccess = ExecuteElevatorTransition(Player, DestinationLocation, TransitionData->Properties.TransitionSpeed);
            break;
            
        case EAuracronVerticalTransitionType::JumpPad:
            bSuccess = ExecuteJumpPadTransition(Player, DestinationLocation, TransitionData->Properties.TransitionSpeed);
            break;

        case EAuracronVerticalTransitionType::WindCurrent:
            bSuccess = ExecuteWindCurrentTransition(Player, DestinationLocation, TransitionData->Properties.TransitionSpeed);
            break;

        case EAuracronVerticalTransitionType::Stairs:
        case EAuracronVerticalTransitionType::CaveEntrance:
            bSuccess = ExecuteWalkTransition(Player, DestinationLocation);
            break;
            
        default:
            bSuccess = ExecuteTeleportTransition(Player, DestinationLocation, DestinationRotation);
            break;
    }
    
    if (bSuccess)
    {
        // Aplicar cooldown
        TransitionCooldowns.Add(TransitionID, TransitionData->Properties.CooldownTime);
        
        // Reproduzir efeito de chegada
        PlayTransitionEffect(TransitionID, false);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Transição %s executada com sucesso"), *TransitionID);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao executar transição %s"), *TransitionID);
    }
    
    return bSuccess;
}

// === Internal Helper Functions ===

void UAuracronVerticalTransitionsBridge::UpdateTransitionCooldowns(float DeltaTime)
{
    FScopeLock Lock(&TransitionMutex);
    
    // Atualizar cooldowns
    for (auto& CooldownPair : TransitionCooldowns)
    {
        if (CooldownPair.Value > 0.0f)
        {
            CooldownPair.Value -= DeltaTime;
            if (CooldownPair.Value <= 0.0f)
            {
                CooldownPair.Value = 0.0f;
                UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Cooldown da transição %s finalizado"), *CooldownPair.Key);
            }
        }
    }
}

void UAuracronVerticalTransitionsBridge::ProcessActiveChannelings(float DeltaTime)
{
    FScopeLock Lock(&TransitionMutex);
    
    TArray<APawn*> PlayersToRemove;
    
    // Processar channelings ativos
    for (auto& ChannelingPair : PlayersChanneling)
    {
        APawn* Player = ChannelingPair.Key;
        const FString& TransitionID = ChannelingPair.Value;
        
        if (!IsValid(Player))
        {
            PlayersToRemove.Add(Player);
            continue;
        }
        
        // Verificar se o jogador ainda está na área de transição
        // Implementação básica - pode ser expandida
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Processando channeling do jogador para transição %s"), *TransitionID);
    }
    
    // Remover jogadores inválidos
    for (APawn* Player : PlayersToRemove)
    {
        PlayersChanneling.Remove(Player);
    }
}

void UAuracronVerticalTransitionsBridge::DetectPlayersNearTransitions()
{
    if (!GetWorld())
    {
        return;
    }
    
    FScopeLock Lock(&TransitionMutex);
    
    // Obter todos os jogadores no mundo
    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (!IsValid(PC) || !IsValid(PC->GetPawn()))
        {
            continue;
        }
        
        APawn* Player = PC->GetPawn();
        FVector PlayerLocation = Player->GetActorLocation();
        
        // Verificar proximidade com pontos de transição
        for (const auto& TransitionPair : TransitionPoints)
        {
            const FTransitionPointData& TransitionData = TransitionPair.Value;
            float Distance = FVector::Dist(PlayerLocation, TransitionData.Location);
            
            if (Distance <= TransitionData.TriggerRadius)
            {
                UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Jogador próximo à transição %s (distância: %.2f)"), *TransitionPair.Key, Distance);
                // Aqui pode ser adicionada lógica adicional para notificar o jogador
            }
        }
    }
}

void UAuracronVerticalTransitionsBridge::InitializeDefaultTransitionProperties()
{
    // Initialize default transition properties using UE 5.6 initialization system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing default transition properties"));

    // Setup default transition types
    DefaultTransitionProperties.Add(EVerticalTransitionType::Teleport, FTransitionProperties());
    DefaultTransitionProperties.Add(EVerticalTransitionType::Elevator, FTransitionProperties());
    DefaultTransitionProperties.Add(EVerticalTransitionType::JumpPad, FTransitionProperties());
    DefaultTransitionProperties.Add(EVerticalTransitionType::WindCurrent, FTransitionProperties());
    DefaultTransitionProperties.Add(EVerticalTransitionType::Walk, FTransitionProperties());

    // Configure teleport properties
    FTransitionProperties& TeleportProps = DefaultTransitionProperties[EVerticalTransitionType::Teleport];
    TeleportProps.TransitionSpeed = 0.0f; // Instant
    TeleportProps.EnergyConsumption = 50.0f;
    TeleportProps.CooldownTime = 5.0f;
    TeleportProps.RequiredLevel = 1;

    // Configure elevator properties
    FTransitionProperties& ElevatorProps = DefaultTransitionProperties[EVerticalTransitionType::Elevator];
    ElevatorProps.TransitionSpeed = 300.0f;
    ElevatorProps.EnergyConsumption = 10.0f;
    ElevatorProps.CooldownTime = 2.0f;
    ElevatorProps.RequiredLevel = 1;

    // Configure jump pad properties
    FTransitionProperties& JumpPadProps = DefaultTransitionProperties[EVerticalTransitionType::JumpPad];
    JumpPadProps.TransitionSpeed = 800.0f;
    JumpPadProps.EnergyConsumption = 20.0f;
    JumpPadProps.CooldownTime = 3.0f;
    JumpPadProps.RequiredLevel = 2;

    // Configure wind current properties
    FTransitionProperties& WindProps = DefaultTransitionProperties[EVerticalTransitionType::WindCurrent];
    WindProps.TransitionSpeed = 400.0f;
    WindProps.EnergyConsumption = 15.0f;
    WindProps.CooldownTime = 1.0f;
    WindProps.RequiredLevel = 3;

    // Configure walk properties
    FTransitionProperties& WalkProps = DefaultTransitionProperties[EVerticalTransitionType::Walk];
    WalkProps.TransitionSpeed = 150.0f;
    WalkProps.EnergyConsumption = 5.0f;
    WalkProps.CooldownTime = 0.0f;
    WalkProps.RequiredLevel = 1;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Default transition properties initialized"));
}

bool UAuracronVerticalTransitionsBridge::ValidateSystemConfiguration()
{
    // Validate system configuration using UE 5.6 validation system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Validating system configuration"));

    // Check if world is valid
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: World is not valid"));
        return false;
    }

    // Check if owner is valid
    if (!GetOwner())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Owner is not valid"));
        return false;
    }

    // Validate configuration parameters
    if (TransitionDetectionRadius <= 0.0f)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid transition detection radius: %.2f"), TransitionDetectionRadius);
        return false;
    }

    if (MaxChannelingTime <= 0.0f)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid max channeling time: %.2f"), MaxChannelingTime);
        return false;
    }

    // Check if default transition properties are initialized
    if (DefaultTransitionProperties.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Default transition properties not initialized"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: System configuration validated successfully"));
    return true;
}

bool UAuracronVerticalTransitionsBridge::InitializeTransitionSystem()
{
    // Initialize transition system using UE 5.6 initialization system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing transition system"));

    try
    {
        // Initialize transition points map
        TransitionPoints.Empty();

        // Initialize active transitions map
        ActiveTransitions.Empty();

        // Initialize transition cooldowns
        TransitionCooldowns.Empty();

        // Initialize transition statistics
        TransitionStatistics.Empty();

        // Setup transition network
        if (!SetupTransitionNetwork())
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to setup transition network"));
            return false;
        }

        // Initialize visual and audio components if enabled
        if (bUseVisualEffects)
        {
            InitializeVisualEffects();
        }

        if (bUseAudioEffects)
        {
            InitializeAudioEffects();
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Transition system initialized successfully"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception during transition system initialization"));
        return false;
    }
}

bool UAuracronVerticalTransitionsBridge::SetupTransitionNetwork()
{
    // Setup transition network using UE 5.6 network system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting up transition network"));

    // Initialize network connections
    TransitionNetworkConnections.Empty();

    // Create default network topology
    // This would typically be loaded from configuration or generated procedurally

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Transition network setup complete"));
    return true;
}

void UAuracronVerticalTransitionsBridge::InitializeVisualEffects()
{
    // Initialize visual effects using UE 5.6 VFX system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing visual effects"));

    // Clear existing effect components
    for (auto& EffectPair : EffectComponents)
    {
        if (EffectPair.Value)
        {
            EffectPair.Value->DestroyComponent();
        }
    }
    EffectComponents.Empty();

    // Initialize default visual effects for each transition type
    // This would typically load particle systems and materials

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Visual effects initialized"));
}

void UAuracronVerticalTransitionsBridge::InitializeAudioEffects()
{
    // Initialize audio effects using UE 5.6 audio system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing audio effects"));

    // Clear existing audio components
    for (auto& AudioPair : AudioComponents)
    {
        if (AudioPair.Value)
        {
            AudioPair.Value->DestroyComponent();
        }
    }
    AudioComponents.Empty();

    // Initialize default audio effects for each transition type
    // This would typically load sound cues and audio components

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Audio effects initialized"));
}

void UAuracronVerticalTransitionsBridge::UpdateTransitionSystem()
{
    // Update transition system using UE 5.6 update system
    if (!bSystemInitialized)
    {
        return;
    }

    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    float DeltaTime = CurrentTime - LastPlayerDetectionTime;
    LastPlayerDetectionTime = CurrentTime;

    // Update transition cooldowns
    UpdateTransitionCooldowns(DeltaTime);

    // Process active channelings
    ProcessActiveChannelings(DeltaTime);

    // Detect players near transitions
    DetectPlayersNearTransitions();

    // Update transition statistics
    UpdateTransitionStatistics();
}

void UAuracronVerticalTransitionsBridge::CreateTransitionVisualComponent(const FTransitionPointData& TransitionData)
{
    // Create visual component for transition using UE 5.6 component system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Creating visual component for transition %s"), *TransitionData.TransitionID);

    if (!GetOwner())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Cannot create visual component - no owner"));
        return;
    }

    // Create static mesh component
    UStaticMeshComponent* MeshComponent = NewObject<UStaticMeshComponent>(GetOwner());
    if (MeshComponent)
    {
        MeshComponent->AttachToComponent(GetOwner()->GetRootComponent(),
            FAttachmentTransformRules::KeepWorldTransform);

        // Set mesh if available
        if (TransitionData.TransitionMesh.IsValid())
        {
            MeshComponent->SetStaticMesh(TransitionData.TransitionMesh.Get());
        }

        // Set location
        MeshComponent->SetWorldLocation(TransitionData.Location);

        // Store component reference
        TransitionComponents.Add(TransitionData.TransitionID, MeshComponent);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Visual component created for transition %s"), *TransitionData.TransitionID);
    }
}

void UAuracronVerticalTransitionsBridge::UpdateNavigationForTransition(const FTransitionPointData& TransitionData)
{
    // Update navigation for transition using UE 5.6 navigation system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Updating navigation for transition %s"), *TransitionData.TransitionID);

    // This would typically update the navigation mesh or add navigation links
    // For now, we'll just log the operation

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Navigation updated for transition %s"), *TransitionData.TransitionID);
}

void UAuracronVerticalTransitionsBridge::UpdateTransitionStatistics()
{
    // Update transition statistics using UE 5.6 statistics system
    // This would typically track usage patterns, performance metrics, etc.

    // Update total usage count
    int32 TotalUsage = 0;
    for (const auto& StatPair : TransitionStatistics)
    {
        TotalUsage += StatPair.Value;
    }

    // Log statistics periodically
    static float LastStatsLog = 0.0f;
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    if (CurrentTime - LastStatsLog > 60.0f) // Log every minute
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Transition Statistics - Total Points: %d, Total Usage: %d"),
            TransitionPoints.Num(), TotalUsage);
        LastStatsLog = CurrentTime;
    }
}
