// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Data Layers Implementation
// Bridge 3.4: World Partition - Data Layers

#include "AuracronWorldPartitionDataLayers.h"

// Define log category
DEFINE_LOG_CATEGORY_STATIC(LogAuracronDataLayers, Log, All);
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionBridge.h"

// Data Layer includes
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerManager.h"
#include "WorldPartition/DataLayer/DataLayerAsset.h"
#include "WorldPartition/DataLayer/DataLayerType.h"
#include "Engine/Engine.h"
#include "WorldPartition/DataLayer/DataLayerInstance.h"
#include "WorldPartition/DataLayer/WorldDataLayers.h"

// Engine includes
#include "Engine/World.h"
#include "HAL/PlatformMemory.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"

// =============================================================================
// DATA LAYER CONDITION IMPLEMENTATION
// =============================================================================

bool FAuracronDataLayerCondition::EvaluateCondition(const FAuracronDataLayerContext& Context) const
{
    if (!bIsActive || ConditionExpression.IsEmpty())
    {
        return false;
    }
    
    // Real condition evaluation using UE5.6 expression parser
    return EvaluateConditionExpression(ConditionExpression, Context);
}

// =============================================================================
// DATA LAYER STATISTICS IMPLEMENTATION
// =============================================================================

void FAuracronDataLayerStatistics::UpdateCalculatedFields()
{
    if (TotalLayers > 0)
    {
        LayerEfficiency = static_cast<float>(LoadedLayers) / static_cast<float>(TotalLayers);
    }
    else
    {
        LayerEfficiency = 0.0f;
    }
    
    LastUpdateTime = FDateTime::Now();
}

// =============================================================================
// DATA LAYER MANAGER IMPLEMENTATION
// =============================================================================

UAuracronDataLayerManager* UAuracronDataLayerManager::Instance = nullptr;

UAuracronDataLayerManager* UAuracronDataLayerManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronDataLayerManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronDataLayerManager::Initialize(const FAuracronDataLayerConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Data Layer Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize statistics
    Statistics = FAuracronDataLayerStatistics();
    
    // Clear collections
    DataLayers.Empty();
    LayerConditions.Empty();
    LoadedLayers.Empty();
    VisibleLayers.Empty();
    
    // Initialize timing
    LastConditionEvaluationTime = 0.0f;
    
    bIsInitialized = true;
    
    AURACRON_WP_LOG_INFO(TEXT("Data Layer Manager initialized with max concurrent operations: %d"), Configuration.MaxConcurrentLayerOperations);
}

void UAuracronDataLayerManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Unload all layers
    TArray<FString> LayersToUnload;
    LayersToUnload = LoadedLayers.Array();
    
    for (const FString& LayerId : LayersToUnload)
    {
        UnloadDataLayer(LayerId);
    }
    
    // Clear all data
    DataLayers.Empty();
    LayerConditions.Empty();
    LoadedLayers.Empty();
    VisibleLayers.Empty();
    
    // Reset references
    ManagedWorld.Reset();
    DataLayerSubsystem.Reset();
    
    bIsInitialized = false;
    
    AURACRON_WP_LOG_INFO(TEXT("Data Layer Manager shutdown completed"));
}

bool UAuracronDataLayerManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronDataLayerManager::Tick(float DeltaTime)
{
    if (!bIsInitialized)
    {
        return;
    }

    // Evaluate conditions periodically
    if (Configuration.bEnableConditionalLoading)
    {
        LastConditionEvaluationTime += DeltaTime;
        if (LastConditionEvaluationTime >= 1.0f) // Evaluate every second
        {
            EvaluateAllConditions();
            LastConditionEvaluationTime = 0.0f;
        }
    }
    
    // Update statistics
    UpdateStatistics();
}

FString UAuracronDataLayerManager::CreateDataLayer(const FString& LayerName, EAuracronDataLayerType LayerType)
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot create data layer: Manager not initialized"));
        return FString();
    }

    if (LayerName.IsEmpty())
    {
        AURACRON_WP_LOG_ERROR(TEXT("Cannot create data layer: Layer name is empty"));
        return FString();
    }

    FScopeLock Lock(&LayerLock);
    
    FString LayerId = GenerateLayerId(LayerName);
    
    // Check if layer already exists
    if (DataLayers.Contains(LayerId))
    {
        AURACRON_WP_LOG_WARNING(TEXT("Data layer already exists: %s"), *LayerId);
        return LayerId;
    }
    
    // Get Data Layer Subsystem for UE5.6 integration
    UDataLayerSubsystem* DataLayerSys = GetDataLayerSubsystem();
    if (!DataLayerSys)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Failed to get Data Layer Subsystem"));
        return FString();
    }
    
    // Create UE5 Data Layer Asset
    UDataLayerAsset* DataLayerAsset = NewObject<UDataLayerAsset>();
    if (!DataLayerAsset)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Failed to create Data Layer Asset"));
        return FString();
    }
    
    // Configure the data layer asset
    // Configure DataLayerAsset for UE 5.6
    // Note: SetType and SetDebugColor are WITH_EDITOR only in UE 5.6
    // These properties are set during asset creation in editor
#if WITH_EDITOR
    DataLayerAsset->SetType(EDataLayerType::Runtime);
    DataLayerAsset->SetDebugColor(FColor::MakeRandomColor());
#endif
    
    // Create layer info
    FAuracronDataLayerInfo LayerInfo;
    LayerInfo.LayerId = LayerId;
    LayerInfo.LayerName = LayerName;
    LayerInfo.LayerLabel = LayerName;
    LayerInfo.LayerType = LayerType;
    LayerInfo.State = EAuracronDataLayerState::Unloaded;
    LayerInfo.Visibility = EAuracronDataLayerVisibility::Hidden;
    LayerInfo.Priority = EAuracronDataLayerPriority::Normal;
    LayerInfo.CreationTime = FDateTime::Now();
    LayerInfo.LastModifiedTime = LayerInfo.CreationTime;
    LayerInfo.DataLayerAsset = DataLayerAsset;
    
    // Add to collection
    DataLayers.Add(LayerId, LayerInfo);
    
    AURACRON_WP_LOG_INFO(TEXT("Data layer created: %s (%s)"), *LayerName, *LayerId);
    
    return LayerId;
}

bool UAuracronDataLayerManager::RemoveDataLayer(const FString& LayerId)
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Data Layer Manager not initialized"));
        return false;
    }

    if (!ValidateLayerId(LayerId))
    {
        AURACRON_WP_LOG_ERROR(TEXT("Invalid layer ID: %s"), *LayerId);
        return false;
    }

    FScopeLock Lock(&LayerLock);
    
    if (!DataLayers.Contains(LayerId))
    {
        AURACRON_WP_LOG_WARNING(TEXT("Layer ID not found: %s"), *LayerId);
        return false;
    }
    
    // Get layer info before removal
    FAuracronDataLayerInfo LayerInfo = DataLayers[LayerId];
    
    // Get Data Layer Subsystem
    UDataLayerSubsystem* DataLayerSys = GetDataLayerSubsystem();
    if (!DataLayerSys)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Failed to get Data Layer Subsystem"));
        return false;
    }
    
    // Unload layer if loaded
    if (LoadedLayers.Contains(LayerId))
    {
        if (!UnloadDataLayer(LayerId))
        {
            AURACRON_WP_LOG_WARNING(TEXT("Failed to unload layer before removal: %s"), *LayerId);
        }
    }
    
    // Remove dependencies
    UnloadLayerDependents(LayerId);
    
    // Clean up UE5 Data Layer Asset if exists
    if (LayerInfo.DataLayerAsset)
    {
        LayerInfo.DataLayerAsset->MarkAsGarbage();
    }
    
    // Remove from collections
    DataLayers.Remove(LayerId);
    LayerConditions.Remove(LayerId);
    LoadedLayers.Remove(LayerId);
    VisibleLayers.Remove(LayerId);
    
    // Update statistics
    UpdateStatistics();
    
    AURACRON_WP_LOG_INFO(TEXT("Data layer removed: %s (%s)"), *LayerInfo.LayerName, *LayerId);
    
    return true;
}

FAuracronDataLayerInfo UAuracronDataLayerManager::GetDataLayerInfo(const FString& LayerId) const
{
    FScopeLock Lock(&LayerLock);
    
    const FAuracronDataLayerInfo* LayerInfo = DataLayers.Find(LayerId);
    if (LayerInfo)
    {
        return *LayerInfo;
    }
    
    return FAuracronDataLayerInfo();
}

TArray<FAuracronDataLayerInfo> UAuracronDataLayerManager::GetAllDataLayers() const
{
    FScopeLock Lock(&LayerLock);
    
    TArray<FAuracronDataLayerInfo> AllLayers;
    DataLayers.GenerateValueArray(AllLayers);
    
    return AllLayers;
}

TArray<FString> UAuracronDataLayerManager::GetDataLayerNames() const
{
    FScopeLock Lock(&LayerLock);
    
    TArray<FString> LayerNames;
    
    for (const auto& LayerPair : DataLayers)
    {
        LayerNames.Add(LayerPair.Value.LayerName);
    }
    
    return LayerNames;
}

bool UAuracronDataLayerManager::DoesDataLayerExist(const FString& LayerId) const
{
    FScopeLock Lock(&LayerLock);
    return DataLayers.Contains(LayerId);
}

bool UAuracronDataLayerManager::SetDataLayerState(const FString& LayerId, EAuracronDataLayerState NewState)
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Data Layer Manager not initialized"));
        return false;
    }

    if (!ValidateLayerId(LayerId))
    {
        AURACRON_WP_LOG_ERROR(TEXT("Invalid layer ID: %s"), *LayerId);
        return false;
    }

    FScopeLock Lock(&LayerLock);
    
    FAuracronDataLayerInfo* LayerInfo = DataLayers.Find(LayerId);
    if (!LayerInfo)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Layer ID not found: %s"), *LayerId);
        return false;
    }
    
    EAuracronDataLayerState OldState = LayerInfo->State;
    
    if (OldState == NewState)
    {
        return true; // Already in desired state
    }
    
    // Get Data Layer Subsystem for UE5.6 integration
    UDataLayerSubsystem* DataLayerSys = GetDataLayerSubsystem();
    if (!DataLayerSubsystem.IsValid())
    {
        AURACRON_WP_LOG_ERROR(TEXT("Failed to get Data Layer Subsystem"));
        return false;
    }
    
    // Update layer state
    LayerInfo->State = NewState;
    LayerInfo->LastModifiedTime = FDateTime::Now();
    
    // Handle state transitions with UE5.6 Data Layer system
    bool bSuccess = true;
    switch (NewState)
    {
        case EAuracronDataLayerState::Loaded:
            LoadedLayers.Add(LayerId);
            bSuccess = true; // State change handled by LoadDataLayer function
            break;
        case EAuracronDataLayerState::Unloaded:
            LoadedLayers.Remove(LayerId);
            VisibleLayers.Remove(LayerId);
            bSuccess = true; // State change handled by UnloadDataLayer function
            break;
        case EAuracronDataLayerState::Loading:
            // Async loading state - handled by LoadDataLayer
            bSuccess = true;
            break;
        case EAuracronDataLayerState::Unloading:
            // Async unloading state - handled by UnloadDataLayer
            bSuccess = true;
            break;
        default:
            AURACRON_WP_LOG_WARNING(TEXT("Unknown data layer state: %d"), (int32)NewState);
            bSuccess = false;
            break;
    }
    
    if (!bSuccess)
    {
        // Revert state change if operation failed
        LayerInfo->State = OldState;
        AURACRON_WP_LOG_ERROR(TEXT("Failed to change data layer state for: %s"), *LayerId);
        return false;
    }
    
    // Update statistics
    UpdateStatistics();
    
    OnLayerStateChangedInternal(LayerId, NewState);
    
    AURACRON_WP_LOG_VERBOSE(TEXT("Data layer state changed: %s (%s -> %s)"), 
                           *LayerId, 
                           *UEnum::GetValueAsString(OldState), 
                           *UEnum::GetValueAsString(NewState));
    
    return true;
}

EAuracronDataLayerState UAuracronDataLayerManager::GetDataLayerState(const FString& LayerId) const
{
    FScopeLock Lock(&LayerLock);
    
    const FAuracronDataLayerInfo* LayerInfo = DataLayers.Find(LayerId);
    if (LayerInfo)
    {
        return LayerInfo->State;
    }
    
    return EAuracronDataLayerState::Unloaded;
}

bool UAuracronDataLayerManager::LoadDataLayer(const FString& LayerId)
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Data Layer Manager not initialized"));
        return false;
    }

    if (!ValidateLayerId(LayerId))
    {
        AURACRON_WP_LOG_ERROR(TEXT("Invalid layer ID: %s"), *LayerId);
        return false;
    }

    FScopeLock Lock(&LayerLock);
    
    FAuracronDataLayerInfo* LayerInfo = DataLayers.Find(LayerId);
    if (!LayerInfo)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Layer ID not found: %s"), *LayerId);
        return false;
    }
    
    if (LayerInfo->State == EAuracronDataLayerState::Loaded)
    {
        AURACRON_WP_LOG_VERBOSE(TEXT("Data layer already loaded: %s"), *LayerId);
        return true; // Already loaded
    }
    
    // Load dependencies first
    if (!LoadLayerDependencies(LayerId))
    {
        AURACRON_WP_LOG_WARNING(TEXT("Failed to load dependencies for layer: %s"), *LayerId);
    }
    
    // Set loading state
    SetDataLayerState(LayerId, EAuracronDataLayerState::Loading);
    
    // Interface with UE5.6's data layer system
    bool bLoadSuccess = false;
    
    // Use UDataLayerSubsystem to load the actual data layer
    UDataLayerSubsystem* DataLayerSys = GetDataLayerSubsystem();
    if (!DataLayerSys)
    {
        AURACRON_WP_LOG_ERROR(TEXT("DataLayerSubsystem not available for loading layer: %s"), *LayerId);
        SetDataLayerState(LayerId, EAuracronDataLayerState::Failed);
        return false;
    }
    
    // Try to find existing data layer asset by name
    if (UWorld* World = DataLayerSys->GetWorld())
    {
        if (World->GetWorldPartition())
        {
            AWorldDataLayers* WorldDataLayers = World->GetWorldDataLayers();
            if (WorldDataLayers)
            {
                // Find data layer instance by name
                const UDataLayerInstance* DataLayerInstance = WorldDataLayers->GetDataLayerInstance(FName(*LayerInfo->LayerName));
                if (DataLayerInstance)
                {
                    // Set data layer to loaded state using UE5.6 APIs
                    if (UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World))
                    {
                        DataLayerManager->SetDataLayerInstanceRuntimeState(DataLayerInstance, EDataLayerRuntimeState::Loaded, false);
                    }
                    
                    // Store reference to the data layer instance
                    LayerInfo->DataLayerInstance = const_cast<UDataLayerInstance*>(DataLayerInstance);
                    
                    bLoadSuccess = true;
                    
                    AURACRON_WP_LOG_VERBOSE(TEXT("Data layer loaded using UE5.6 system: %s"), *LayerId);
                }
                else
                {
                    // Create new data layer instance if it doesn't exist
                    UDataLayerAsset* NewDataLayerAsset = NewObject<UDataLayerAsset>(WorldDataLayers);
                    if (NewDataLayerAsset)
                    {
                        // Configure new DataLayerAsset for UE 5.6
                        // Note: SetType and SetDebugColor are WITH_EDITOR only in UE 5.6
                        // These properties are set during asset creation in editor
#if WITH_EDITOR
                        NewDataLayerAsset->SetType(EDataLayerType::Runtime);
                        NewDataLayerAsset->SetDebugColor(FColor::MakeRandomColor());
#endif
                        
                        // Create data layer instance
                        // Use the correct UE 5.6 API for creating data layer instances
                        // Create the data layer instance using UE 5.6 compatible API
                        // Note: Data layer creation is WITH_EDITOR only in UE 5.6
                        UDataLayerInstance* NewDataLayerInstance = nullptr;
#if WITH_EDITOR
                        // Use NewObject instead of CreateDataLayer which doesn't exist in UE 5.6
                        NewDataLayerInstance = NewObject<UDataLayerInstance>(WorldDataLayers, UDataLayerInstance::StaticClass(), *LayerName);
                        if (NewDataLayerInstance)
                        {
                            // Initialize the data layer instance
                            NewDataLayerInstance->SetDataLayerShortName(*LayerName);
                            NewDataLayerInstance->SetVisible(true);
                            NewDataLayerInstance->SetIsInitiallyVisible(true);
                        }
#else
                        // In runtime, we can't create new data layer instances
                        // This functionality is editor-only
                        UE_LOG(LogAuracronWorldPartitionBridge, Warning, TEXT("Data layer creation is editor-only functionality"));
#endif

                        if (NewDataLayerInstance)
                        {
                            // The data layer instance is automatically configured by the CreateDataLayer method
                            AURACRON_WP_LOG_INFO(TEXT("Successfully created data layer instance: %s"), *NewDataLayerAsset->GetName());
                        }
                        if (NewDataLayerInstance)
                        {
                            // Set to loaded state
                            if (UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World))
                            {
                                DataLayerManager->SetDataLayerInstanceRuntimeState(NewDataLayerInstance, EDataLayerRuntimeState::Loaded, false);
                            }
                            
                            // Store reference
                            LayerInfo->DataLayerInstance = NewDataLayerInstance;
                            LayerInfo->DataLayerAsset = NewDataLayerAsset;
                            
                            bLoadSuccess = true;
                            
                            AURACRON_WP_LOG_VERBOSE(TEXT("New data layer created and loaded: %s"), *LayerId);
                        }
                        else
                        {
                            AURACRON_WP_LOG_ERROR(TEXT("Failed to create data layer instance for: %s"), *LayerId);
                        }
                    }
                    else
                    {
                        AURACRON_WP_LOG_ERROR(TEXT("Failed to create data layer asset for: %s"), *LayerId);
                    }
                }
            }
            else
            {
                AURACRON_WP_LOG_ERROR(TEXT("WorldDataLayers not found for loading layer: %s"), *LayerId);
            }
        }
        else
        {
            AURACRON_WP_LOG_ERROR(TEXT("World Partition not found for loading layer: %s"), *LayerId);
        }
    }
    else
    {
        AURACRON_WP_LOG_ERROR(TEXT("World not found for loading layer: %s"), *LayerId);
    }
    
    if (bLoadSuccess)
    {
        SetDataLayerState(LayerId, EAuracronDataLayerState::Loaded);
        
        // Update statistics
        {
            FScopeLock StatsLock(&StatisticsLock);
            Statistics.LayerSwitches++;
        }
        
        AURACRON_WP_LOG_INFO(TEXT("Data layer loaded successfully: %s (%s)"), *LayerInfo->LayerName, *LayerId);
        return true;
    }
    else
    {
        SetDataLayerState(LayerId, EAuracronDataLayerState::Failed);
        
        // Update statistics
        {
            FScopeLock StatsLock(&StatisticsLock);
            Statistics.FailedOperations++;
        }
        
        AURACRON_WP_LOG_ERROR(TEXT("Failed to load data layer: %s"), *LayerId);
        return false;
    }
}

bool UAuracronDataLayerManager::UnloadDataLayer(const FString& LayerId)
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Data Layer Manager not initialized"));
        return false;
    }

    if (!ValidateLayerId(LayerId))
    {
        AURACRON_WP_LOG_ERROR(TEXT("Invalid layer ID: %s"), *LayerId);
        return false;
    }

    FScopeLock Lock(&LayerLock);

    FAuracronDataLayerInfo* LayerInfo = DataLayers.Find(LayerId);
    if (!LayerInfo)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Layer ID not found: %s"), *LayerId);
        return false;
    }

    if (LayerInfo->State == EAuracronDataLayerState::Unloaded)
    {
        AURACRON_WP_LOG_VERBOSE(TEXT("Data layer already unloaded: %s"), *LayerId);
        return true; // Already unloaded
    }

    // Unload dependents first
    if (!UnloadLayerDependents(LayerId))
    {
        AURACRON_WP_LOG_WARNING(TEXT("Some dependents could not be unloaded for layer: %s"), *LayerId);
    }

    // Set unloading state
    SetDataLayerState(LayerId, EAuracronDataLayerState::Unloading);

    // Real implementation using UE5.6 Data Layer APIs
    bool bUnloadSuccess = false;

    // Use UDataLayerSubsystem for UE5.6 integration
    UDataLayerSubsystem* DataLayerSys = GetDataLayerSubsystem();
    if (!DataLayerSys)
    {
        AURACRON_WP_LOG_ERROR(TEXT("DataLayerSubsystem not available for unloading layer: %s"), *LayerId);
        SetDataLayerState(LayerId, EAuracronDataLayerState::Failed);
        return false;
    }

    // Try multiple approaches to find and unload the data layer
    if (LayerInfo->DataLayerInstance)
    {
        // Use stored instance reference
        GetDataLayerManager()->SetDataLayerInstanceRuntimeState(LayerInfo->DataLayerInstance, EDataLayerRuntimeState::Unloaded, false);
        bUnloadSuccess = true;
        AURACRON_WP_LOG_VERBOSE(TEXT("Data layer unloaded using stored instance: %s"), *LayerId);
    }
    else if (LayerInfo->DataLayerAsset)
    {
        // Try to find instance by asset
        if (UWorld* World = DataLayerSys->GetWorld())
        {
            if (World->GetWorldPartition())
            {
                AWorldDataLayers* WorldDataLayers = World->GetWorldDataLayers();
                if (WorldDataLayers)
                {
                    const UDataLayerInstance* DataLayerInstance = WorldDataLayers->GetDataLayerInstance(FName(*LayerInfo->LayerName));
                    if (DataLayerInstance)
                    {
                        if (UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World))
                        {
                            DataLayerManager->SetDataLayerInstanceRuntimeState(DataLayerInstance, EDataLayerRuntimeState::Unloaded, false);
                        }
                        bUnloadSuccess = true;
                        AURACRON_WP_LOG_VERBOSE(TEXT("Data layer unloaded using world data layers: %s"), *LayerId);
                    }
                }
            }
        }
    }
    else
    {
        // Try to find the data layer instance by layer name
        if (const UDataLayerInstance* DataLayerInstance = GetDataLayerManager()->GetDataLayerInstanceFromName(FName(*LayerInfo->LayerName)))
        {
            GetDataLayerManager()->SetDataLayerInstanceRuntimeState(DataLayerInstance, EDataLayerRuntimeState::Unloaded, false);
            bUnloadSuccess = true;
            AURACRON_WP_LOG_VERBOSE(TEXT("Data layer unloaded using layer name lookup: %s"), *LayerId);
        }
        else
        {
            AURACRON_WP_LOG_WARNING(TEXT("Data layer instance not found for unloading: %s, marking as unloaded"), *LayerId);
            // If we can't find the instance, assume it's already unloaded
            bUnloadSuccess = true;
        }
    }

    if (bUnloadSuccess)
    {
        SetDataLayerState(LayerId, EAuracronDataLayerState::Unloaded);
        
        // Update statistics
        {
            FScopeLock StatsLock(&StatisticsLock);
            Statistics.LayerSwitches++;
        }
        
        AURACRON_WP_LOG_INFO(TEXT("Data layer unloaded successfully: %s (%s)"), *LayerInfo->LayerName, *LayerId);
        return true;
    }
    else
    {
        SetDataLayerState(LayerId, EAuracronDataLayerState::Failed);

        // Update statistics
        {
            FScopeLock StatsLock(&StatisticsLock);
            Statistics.FailedOperations++;
        }

        AURACRON_WP_LOG_ERROR(TEXT("Failed to unload data layer: %s"), *LayerId);
        return false;
    }
}

bool UAuracronDataLayerManager::ToggleDataLayer(const FString& LayerId)
{
    EAuracronDataLayerState CurrentState = GetDataLayerState(LayerId);

    if (CurrentState == EAuracronDataLayerState::Loaded)
    {
        return UnloadDataLayer(LayerId);
    }
    else if (CurrentState == EAuracronDataLayerState::Unloaded)
    {
        return LoadDataLayer(LayerId);
    }

    return false;
}

bool UAuracronDataLayerManager::SetDataLayerVisibility(const FString& LayerId, EAuracronDataLayerVisibility Visibility)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&LayerLock);

    FAuracronDataLayerInfo* LayerInfo = DataLayers.Find(LayerId);
    if (!LayerInfo)
    {
        return false;
    }

    EAuracronDataLayerVisibility OldVisibility = LayerInfo->Visibility;
    LayerInfo->Visibility = Visibility;
    LayerInfo->LastModifiedTime = FDateTime::Now();

    // Update visibility collections
    switch (Visibility)
    {
        case EAuracronDataLayerVisibility::Visible:
            VisibleLayers.Add(LayerId);
            break;
        case EAuracronDataLayerVisibility::Hidden:
            VisibleLayers.Remove(LayerId);
            break;
    }

    OnLayerVisibilityChangedInternal(LayerId, Visibility);

    AURACRON_WP_LOG_VERBOSE(TEXT("Data layer visibility changed: %s (%s -> %s)"),
                           *LayerId,
                           *UEnum::GetValueAsString(OldVisibility),
                           *UEnum::GetValueAsString(Visibility));

    return true;
}

EAuracronDataLayerVisibility UAuracronDataLayerManager::GetDataLayerVisibility(const FString& LayerId) const
{
    FScopeLock Lock(&LayerLock);

    const FAuracronDataLayerInfo* LayerInfo = DataLayers.Find(LayerId);
    if (LayerInfo)
    {
        return LayerInfo->Visibility;
    }

    return EAuracronDataLayerVisibility::Hidden;
}

bool UAuracronDataLayerManager::ShowDataLayer(const FString& LayerId)
{
    return SetDataLayerVisibility(LayerId, EAuracronDataLayerVisibility::Visible);
}

bool UAuracronDataLayerManager::HideDataLayer(const FString& LayerId)
{
    return SetDataLayerVisibility(LayerId, EAuracronDataLayerVisibility::Hidden);
}

bool UAuracronDataLayerManager::SwitchToDataLayerSet(const TArray<FString>& LayerIds)
{
    if (!bIsInitialized)
    {
        return false;
    }

    AURACRON_WP_LOG_INFO(TEXT("Switching to data layer set with %d layers"), LayerIds.Num());

    // First, unload all currently loaded layers that are not in the new set
    TArray<FString> CurrentlyLoaded = GetLoadedLayers();
    for (const FString& LoadedLayerId : CurrentlyLoaded)
    {
        if (!LayerIds.Contains(LoadedLayerId))
        {
            UnloadDataLayer(LoadedLayerId);
        }
    }

    // Then, load all layers in the new set
    bool bAllSuccessful = true;
    for (const FString& LayerId : LayerIds)
    {
        if (!LoadDataLayer(LayerId))
        {
            bAllSuccessful = false;
            AURACRON_WP_LOG_WARNING(TEXT("Failed to load layer in set: %s"), *LayerId);
        }
    }

    return bAllSuccessful;
}

bool UAuracronDataLayerManager::BlendDataLayers(const TArray<FString>& LayerIds, const TArray<float>& BlendWeights)
{
    if (!Configuration.bEnableLayerBlending)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Layer blending is disabled"));
        return false;
    }

    if (LayerIds.Num() != BlendWeights.Num())
    {
        AURACRON_WP_LOG_ERROR(TEXT("Layer IDs and blend weights arrays must have the same size"));
        return false;
    }

    AURACRON_WP_LOG_INFO(TEXT("Blending %d data layers"), LayerIds.Num());

    // Real implementation using UE5.6 Data Layer APIs for blending
    bool bAllSuccessful = true;

    // Use UDataLayerSubsystem for proper blending
    if (UDataLayerSubsystem* DataLayerSys = GetDataLayerSubsystem())
    {
        // Normalize weights to ensure they sum to 1.0
        TArray<float> NormalizedWeights = BlendWeights;
        float WeightSum = 0.0f;
        for (float Weight : BlendWeights)
        {
            WeightSum += FMath::Max(0.0f, Weight);
        }
        
        if (WeightSum > 0.0f)
        {
            for (float& Weight : NormalizedWeights)
            {
                Weight = FMath::Max(0.0f, Weight) / WeightSum;
            }
        }

        // Apply blending based on normalized weights
        for (int32 i = 0; i < LayerIds.Num(); i++)
        {
            const FString& LayerId = LayerIds[i];
            float NormalizedWeight = NormalizedWeights[i];

            // Find the data layer instance
            if (const UDataLayerInstance* DataLayerInstance = GetDataLayerManager()->GetDataLayerInstanceFromName(FName(*LayerId)))
            {
                // Determine runtime state based on weight
                EDataLayerRuntimeState TargetState = EDataLayerRuntimeState::Unloaded;
                if (NormalizedWeight > 0.1f) // Load layers with weight > 10%
                {
                    TargetState = EDataLayerRuntimeState::Loaded;
                }
                if (NormalizedWeight > 0.5f) // Activate layers with weight > 50%
                {
                    TargetState = EDataLayerRuntimeState::Activated;
                }

                // Set the runtime state
                GetDataLayerManager()->SetDataLayerInstanceRuntimeState(DataLayerInstance, TargetState, false);

                // Update our internal state tracking
                EAuracronDataLayerState InternalState = EAuracronDataLayerState::Unloaded;
                if (TargetState == EDataLayerRuntimeState::Loaded)
                {
                    InternalState = EAuracronDataLayerState::Loaded;
                }
                else if (TargetState == EDataLayerRuntimeState::Activated)
                {
                    InternalState = EAuracronDataLayerState::Loaded;
                }

                SetDataLayerState(LayerId, InternalState);

                // Set visibility based on weight
                EAuracronDataLayerVisibility Visibility = EAuracronDataLayerVisibility::Hidden;
                if (NormalizedWeight > 0.8f)
                {
                    Visibility = EAuracronDataLayerVisibility::Visible;
                }
                else if (NormalizedWeight > 0.3f)
                {
                    Visibility = EAuracronDataLayerVisibility::PartiallyVisible;
                }

                SetDataLayerVisibility(LayerId, Visibility);
            }
            else
            {
                AURACRON_WP_LOG_WARNING(TEXT("Data layer instance not found for blending: %s"), *LayerId);
                bAllSuccessful = false;
            }
        }
    }
    else
    {
        AURACRON_WP_LOG_ERROR(TEXT("DataLayerSubsystem not available for blending"));
        bAllSuccessful = false;
    }

    return bAllSuccessful;
}

bool UAuracronDataLayerManager::TransitionToDataLayer(const FString& FromLayerId, const FString& ToLayerId, float TransitionTime)
{
    if (!bIsInitialized)
    {
        return false;
    }

    AURACRON_WP_LOG_INFO(TEXT("Transitioning from layer %s to %s (%.2fs)"), *FromLayerId, *ToLayerId, TransitionTime);

    OnDataLayerTransitionStarted.Broadcast(FromLayerId, ToLayerId, TransitionTime);

    // Real implementation using UE5.6 Data Layer APIs for smooth transitions
    bool bSuccess = true;

    // Use UDataLayerSubsystem for proper transition handling
    if (UDataLayerSubsystem* DataLayerSys = GetDataLayerSubsystem())
    {
        // Find both data layer instances
        const UDataLayerInstance* FromLayerInstance = nullptr;
        const UDataLayerInstance* ToLayerInstance = nullptr;

        if (!FromLayerId.IsEmpty())
        {
            FromLayerInstance = GetDataLayerManager()->GetDataLayerInstanceFromName(FName(*FromLayerId));
            if (!FromLayerInstance)
            {
                AURACRON_WP_LOG_WARNING(TEXT("Source data layer instance not found: %s"), *FromLayerId);
            }
        }

        ToLayerInstance = GetDataLayerManager()->GetDataLayerInstanceFromName(FName(*ToLayerId));
        if (!ToLayerInstance)
        {
            AURACRON_WP_LOG_ERROR(TEXT("Target data layer instance not found: %s"), *ToLayerId);
            bSuccess = false;
        }

        if (bSuccess && ToLayerInstance)
        {
            // For smooth transitions, we'll implement a gradual state change
            if (TransitionTime > 0.0f)
            {
                // Start by loading the target layer
                GetDataLayerManager()->SetDataLayerInstanceRuntimeState(ToLayerInstance, EDataLayerRuntimeState::Loaded, false);
                SetDataLayerState(ToLayerId, EAuracronDataLayerState::Loading);

                // Create a timer for the transition
                if (UWorld* World = DataLayerSys->GetWorld())
                {
                    FTimerHandle TransitionTimer;
                    FTimerDelegate TransitionDelegate;
                    
                    TransitionDelegate.BindLambda([this, DataLayerSys, FromLayerInstance, ToLayerInstance, FromLayerId, ToLayerId]()
                    {
                        // Complete the transition
                        if (ToLayerInstance)
                        {
                            GetDataLayerManager()->SetDataLayerInstanceRuntimeState(ToLayerInstance, EDataLayerRuntimeState::Activated, false);
                            SetDataLayerState(ToLayerId, EAuracronDataLayerState::Loaded);
                            SetDataLayerVisibility(ToLayerId, EAuracronDataLayerVisibility::Visible);
                        }

                        if (FromLayerInstance && !FromLayerId.IsEmpty())
                        {
                            GetDataLayerManager()->SetDataLayerInstanceRuntimeState(FromLayerInstance, EDataLayerRuntimeState::Unloaded, false);
                            SetDataLayerState(FromLayerId, EAuracronDataLayerState::Unloaded);
                            SetDataLayerVisibility(FromLayerId, EAuracronDataLayerVisibility::Hidden);
                        }

                        OnDataLayerTransitionCompleted.Broadcast(FromLayerId, ToLayerId);
                    });

                    World->GetTimerManager().SetTimer(TransitionTimer, TransitionDelegate, TransitionTime, false);
                }
            }
            else
            {
                // Immediate transition
                GetDataLayerManager()->SetDataLayerInstanceRuntimeState(ToLayerInstance, EDataLayerRuntimeState::Activated, false);
                SetDataLayerState(ToLayerId, EAuracronDataLayerState::Loaded);
                SetDataLayerVisibility(ToLayerId, EAuracronDataLayerVisibility::Visible);

                if (FromLayerInstance && !FromLayerId.IsEmpty())
                {
                    GetDataLayerManager()->SetDataLayerInstanceRuntimeState(FromLayerInstance, EDataLayerRuntimeState::Unloaded, false);
                    SetDataLayerState(FromLayerId, EAuracronDataLayerState::Unloaded);
                    SetDataLayerVisibility(FromLayerId, EAuracronDataLayerVisibility::Hidden);
                }
            }
        }
    }
    else
    {
        AURACRON_WP_LOG_ERROR(TEXT("DataLayerSubsystem not available for transition"));
        bSuccess = false;
    }

    if (bSuccess)
    {
        OnDataLayerTransitionCompleted.Broadcast(FromLayerId, ToLayerId);
    }

    return bSuccess;
}

bool UAuracronDataLayerManager::AddLayerCondition(const FString& LayerId, const FAuracronDataLayerCondition& Condition)
{
    if (!bIsInitialized || !Configuration.bEnableConditionalLoading)
    {
        return false;
    }

    FScopeLock Lock(&LayerLock);

    if (!DataLayers.Contains(LayerId))
    {
        return false;
    }

    TArray<FAuracronDataLayerCondition>& Conditions = LayerConditions.FindOrAdd(LayerId);

    // Check if condition with same name already exists
    for (FAuracronDataLayerCondition& ExistingCondition : Conditions)
    {
        if (ExistingCondition.ConditionName == Condition.ConditionName)
        {
            ExistingCondition = Condition; // Update existing
            AURACRON_WP_LOG_VERBOSE(TEXT("Updated condition '%s' for layer %s"), *Condition.ConditionName, *LayerId);
            return true;
        }
    }

    // Add new condition
    Conditions.Add(Condition);
    AURACRON_WP_LOG_VERBOSE(TEXT("Added condition '%s' for layer %s"), *Condition.ConditionName, *LayerId);

    return true;
}

bool UAuracronDataLayerManager::RemoveLayerCondition(const FString& LayerId, const FString& ConditionName)
{
    FScopeLock Lock(&LayerLock);

    TArray<FAuracronDataLayerCondition>* Conditions = LayerConditions.Find(LayerId);
    if (!Conditions)
    {
        return false;
    }

    for (int32 i = Conditions->Num() - 1; i >= 0; i--)
    {
        if ((*Conditions)[i].ConditionName == ConditionName)
        {
            Conditions->RemoveAt(i);
            AURACRON_WP_LOG_VERBOSE(TEXT("Removed condition '%s' from layer %s"), *ConditionName, *LayerId);
            return true;
        }
    }

    return false;
}

TArray<FAuracronDataLayerCondition> UAuracronDataLayerManager::GetLayerConditions(const FString& LayerId) const
{
    FScopeLock Lock(&LayerLock);

    const TArray<FAuracronDataLayerCondition>* Conditions = LayerConditions.Find(LayerId);
    if (Conditions)
    {
        return *Conditions;
    }

    return TArray<FAuracronDataLayerCondition>();
}

void UAuracronDataLayerManager::EvaluateAllConditions()
{
    if (!Configuration.bEnableConditionalLoading)
    {
        return;
    }

    FScopeLock Lock(&LayerLock);

    for (const auto& ConditionPair : LayerConditions)
    {
        const FString& LayerId = ConditionPair.Key;
        const TArray<FAuracronDataLayerCondition>& Conditions = ConditionPair.Value;

        bool bShouldBeLoaded = false;

        // Evaluate all conditions for this layer
        for (const FAuracronDataLayerCondition& Condition : Conditions)
        {
            FAuracronDataLayerContext Context; // Create a default context
            if (Condition.EvaluateCondition(Context))
            {
                bShouldBeLoaded = true;
                break; // Any condition being true means the layer should be loaded
            }
        }

        // Update layer state based on condition evaluation
        EAuracronDataLayerState CurrentState = GetDataLayerState(LayerId);

        if (bShouldBeLoaded && CurrentState == EAuracronDataLayerState::Unloaded)
        {
            LoadDataLayer(LayerId);
        }
        else if (!bShouldBeLoaded && CurrentState == EAuracronDataLayerState::Loaded)
        {
            UnloadDataLayer(LayerId);
        }
    }
}

TArray<FString> UAuracronDataLayerManager::GetLoadedLayers() const
{
    FScopeLock Lock(&LayerLock);

    TArray<FString> LoadedLayersList;
    LoadedLayersList = LoadedLayers.Array();

    return LoadedLayersList;
}

TArray<FString> UAuracronDataLayerManager::GetVisibleLayers() const
{
    FScopeLock Lock(&LayerLock);

    TArray<FString> VisibleLayersList;
    VisibleLayersList = VisibleLayers.Array();

    return VisibleLayersList;
}

TArray<FString> UAuracronDataLayerManager::GetLayersByType(EAuracronDataLayerType LayerType) const
{
    FScopeLock Lock(&LayerLock);

    TArray<FString> FilteredLayers;

    for (const auto& LayerPair : DataLayers)
    {
        if (LayerPair.Value.LayerType == LayerType)
        {
            FilteredLayers.Add(LayerPair.Key);
        }
    }

    return FilteredLayers;
}

TArray<FString> UAuracronDataLayerManager::GetLayersByTag(const FString& Tag) const
{
    FScopeLock Lock(&LayerLock);

    TArray<FString> FilteredLayers;

    for (const auto& LayerPair : DataLayers)
    {
        if (LayerPair.Value.Tags.Contains(Tag))
        {
            FilteredLayers.Add(LayerPair.Key);
        }
    }

    return FilteredLayers;
}

void UAuracronDataLayerManager::SetConfiguration(const FAuracronDataLayerConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();

    AURACRON_WP_LOG_INFO(TEXT("Data layer configuration updated"));
}

FAuracronDataLayerConfiguration UAuracronDataLayerManager::GetConfiguration() const
{
    return Configuration;
}

FAuracronDataLayerStatistics UAuracronDataLayerManager::GetDataLayerStatistics() const
{
    FScopeLock Lock(&StatisticsLock);

    FAuracronDataLayerStatistics CurrentStats = Statistics;
    CurrentStats.UpdateCalculatedFields();

    return CurrentStats;
}

void UAuracronDataLayerManager::ResetStatistics()
{
    FScopeLock Lock(&StatisticsLock);
    Statistics = FAuracronDataLayerStatistics();

    AURACRON_WP_LOG_INFO(TEXT("Data layer statistics reset"));
}

float UAuracronDataLayerManager::GetTotalMemoryUsage() const
{
    FScopeLock Lock(&StatisticsLock);
    return Statistics.TotalMemoryUsageMB;
}

int32 UAuracronDataLayerManager::GetLoadedLayerCount() const
{
    FScopeLock Lock(&LayerLock);
    return LoadedLayers.Num();
}

int32 UAuracronDataLayerManager::GetTotalLayerCount() const
{
    FScopeLock Lock(&LayerLock);
    return DataLayers.Num();
}

void UAuracronDataLayerManager::EnableLayerDebug(bool bEnabled)
{
    Configuration.bEnableLayerDebug = bEnabled;

    AURACRON_WP_LOG_INFO(TEXT("Data layer debug %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronDataLayerManager::IsLayerDebugEnabled() const
{
    return Configuration.bEnableLayerDebug;
}

void UAuracronDataLayerManager::LogLayerState() const
{
    FScopeLock Lock(&LayerLock);

    int32 LoadedCount = LoadedLayers.Num();
    int32 VisibleCount = VisibleLayers.Num();
    int32 TotalCount = DataLayers.Num();

    AURACRON_WP_LOG_INFO(TEXT("Data Layer State: %d total, %d loaded, %d visible"), TotalCount, LoadedCount, VisibleCount);

    FAuracronDataLayerStatistics CurrentStats = GetDataLayerStatistics();
    AURACRON_WP_LOG_INFO(TEXT("Statistics: %.1fMB memory, %.2f efficiency, %d switches"),
                         CurrentStats.TotalMemoryUsageMB, CurrentStats.LayerEfficiency, CurrentStats.LayerSwitches);
}

void UAuracronDataLayerManager::DrawDebugLayerInfo(UWorld* World) const
{
    if (!Configuration.bEnableLayerDebug || !World)
    {
        return;
    }

    // Draw debug information for loaded layers
    TArray<FString> LoadedLayersList = GetLoadedLayers();

    FVector DebugLocation = FVector(0, 0, 1000);

    for (int32 i = 0; i < LoadedLayersList.Num(); i++)
    {
        const FString& LayerId = LoadedLayersList[i];
        FAuracronDataLayerInfo LayerInfo = GetDataLayerInfo(LayerId);

        FVector TextLocation = DebugLocation + FVector(0, 0, i * 100);

        FString DebugText = FString::Printf(TEXT("Layer: %s (%s)"), *LayerInfo.LayerName, *LayerId);

        DrawDebugString(World, TextLocation, DebugText, nullptr, FColor::Green, -1.0f, true);
    }
}

void UAuracronDataLayerManager::UpdateStatistics()
{
    FScopeLock Lock(&StatisticsLock);

    Statistics.TotalLayers = DataLayers.Num();
    Statistics.LoadedLayers = LoadedLayers.Num();
    Statistics.VisibleLayers = VisibleLayers.Num();

    // Calculate total memory usage
    Statistics.TotalMemoryUsageMB = 0.0f;
    for (const auto& LayerPair : DataLayers)
    {
        if (LoadedLayers.Contains(LayerPair.Key))
        {
            Statistics.TotalMemoryUsageMB += LayerPair.Value.MemoryUsageMB;
        }
    }

    Statistics.UpdateCalculatedFields();
}

FString UAuracronDataLayerManager::GenerateLayerId(const FString& LayerName) const
{
    // Generate a unique ID based on layer name and timestamp
    FString CleanName = LayerName.Replace(TEXT(" "), TEXT("_"));
    CleanName = CleanName.Replace(TEXT("-"), TEXT("_"));

    return FString::Printf(TEXT("DL_%s_%lld"), *CleanName, FDateTime::Now().GetTicks());
}

bool UAuracronDataLayerManager::ValidateLayerId(const FString& LayerId) const
{
    return !LayerId.IsEmpty() && LayerId.StartsWith(TEXT("DL_"));
}

void UAuracronDataLayerManager::OnLayerStateChangedInternal(const FString& LayerId, EAuracronDataLayerState NewState)
{
    OnDataLayerStateChanged.Broadcast(LayerId, NewState);

    AURACRON_WP_LOG_VERBOSE(TEXT("Layer state changed event: %s -> %s"),
                           *LayerId, *UEnum::GetValueAsString(NewState));
}

void UAuracronDataLayerManager::OnLayerVisibilityChangedInternal(const FString& LayerId, EAuracronDataLayerVisibility NewVisibility)
{
    OnDataLayerVisibilityChanged.Broadcast(LayerId, NewVisibility);

    AURACRON_WP_LOG_VERBOSE(TEXT("Layer visibility changed event: %s -> %s"),
                           *LayerId, *UEnum::GetValueAsString(NewVisibility));
}

void UAuracronDataLayerManager::ValidateConfiguration()
{
    // Validate concurrent operations
    Configuration.MaxConcurrentLayerOperations = FMath::Max(1, Configuration.MaxConcurrentLayerOperations);

    // Validate transition time
    Configuration.LayerTransitionTime = FMath::Max(0.1f, Configuration.LayerTransitionTime);

    // Validate memory settings
    Configuration.MaxLayerMemoryUsageMB = FMath::Max(100.0f, Configuration.MaxLayerMemoryUsageMB);
}

UDataLayerSubsystem* UAuracronDataLayerManager::GetDataLayerSubsystem() const
{
    // Try to get world from multiple sources
    UWorld* World = nullptr;
    
    // First try: Get from cached world reference
    if (CachedWorld.IsValid())
    {
        World = CachedWorld.Get();
    }
    
    // Second try: Get from GEngine
    if (!World && GEngine)
    {
        // Try to get the current world from the engine
        if (UWorld* EngineWorld = GEngine->GetCurrentPlayWorld())
        {
            World = EngineWorld;
        }
        else if (GEngine->GetWorldContexts().Num() > 0)
        {
            // Get world from first valid world context
            for (const FWorldContext& WorldContext : GEngine->GetWorldContexts())
            {
                if (WorldContext.World())
                {
                    World = WorldContext.World();
                    break;
                }
            }
        }
    }
    
    // Third try: Get from editor world if in editor
    #if WITH_EDITOR
    if (!World && GEditor)
    {
        World = GEditor->GetEditorWorldContext().World();
    }
    #endif
    
    if (World)
    {
        UDataLayerSubsystem* LocalDataLayerSubsystem = UWorld::GetSubsystem<UDataLayerSubsystem>(World);
        if (LocalDataLayerSubsystem)
        {
            // Cache the world reference for future use
            const_cast<UAuracronDataLayerManager*>(this)->CachedWorld = World;
            return LocalDataLayerSubsystem;
        }
        else
        {
            UE_LOG(LogAuracronDataLayers, Warning, TEXT("DataLayerSubsystem not found in world"));
        }
    }
    else
    {
        UE_LOG(LogAuracronDataLayers, Warning, TEXT("No valid world found for DataLayerSubsystem"));
    }
    
    return nullptr;
}

UDataLayerManager* UAuracronDataLayerManager::GetDataLayerManager() const
{
    // Try to get world from multiple sources
    UWorld* World = nullptr;

    // First try: Get from cached world reference
    if (CachedWorld.IsValid())
    {
        World = CachedWorld.Get();
    }

    // Second try: Get from GEngine
    if (!World && GEngine)
    {
        // Try to get the current world from the engine
        if (UWorld* EngineWorld = GEngine->GetCurrentPlayWorld())
        {
            World = EngineWorld;
        }
        else if (GEngine->GetWorldContexts().Num() > 0)
        {
            // Get world from first valid world context
            for (const FWorldContext& WorldContext : GEngine->GetWorldContexts())
            {
                if (WorldContext.World())
                {
                    World = WorldContext.World();
                    break;
                }
            }
        }
    }

    // Third try: Get from editor world if in editor
    #if WITH_EDITOR
    if (!World && GEditor)
    {
        World = GEditor->GetEditorWorldContext().World();
    }
    #endif

    if (World)
    {
        // Note: UDataLayerManager is not a world subsystem, it's within WorldPartition
        UDataLayerManager* LocalDataLayerManager = nullptr;
        if (UWorldPartition* WorldPartition = World->GetWorldPartition())
        {
            LocalDataLayerManager = WorldPartition->GetDataLayerManager();
        }
        if (LocalDataLayerManager)
        {
            // Cache the world reference for future use
            const_cast<UAuracronDataLayerManager*>(this)->CachedWorld = World;
            return LocalDataLayerManager;
        }
        else
        {
            UE_LOG(LogAuracronDataLayers, Warning, TEXT("DataLayerManager not found in world"));
        }
    }
    else
    {
        UE_LOG(LogAuracronDataLayers, Warning, TEXT("No valid world found for DataLayerManager"));
    }

    return nullptr;
}

bool UAuracronDataLayerManager::LoadLayerDependencies(const FString& LayerId)
{
    const FAuracronDataLayerInfo* LayerInfo = DataLayers.Find(LayerId);
    if (!LayerInfo)
    {
        return false;
    }

    bool bAllDependenciesLoaded = true;

    for (const FString& DependencyId : LayerInfo->Dependencies)
    {
        if (GetDataLayerState(DependencyId) != EAuracronDataLayerState::Loaded)
        {
            if (!LoadDataLayer(DependencyId))
            {
                bAllDependenciesLoaded = false;
                AURACRON_WP_LOG_WARNING(TEXT("Failed to load dependency %s for layer %s"), *DependencyId, *LayerId);
            }
        }
    }

    return bAllDependenciesLoaded;
}

bool UAuracronDataLayerManager::UnloadLayerDependents(const FString& LayerId)
{
    bool bAllDependentsUnloaded = true;

    // Find all layers that depend on this layer
    for (const auto& LayerPair : DataLayers)
    {
        const FAuracronDataLayerInfo& LayerInfo = LayerPair.Value;

        if (LayerInfo.Dependencies.Contains(LayerId) &&
            GetDataLayerState(LayerPair.Key) == EAuracronDataLayerState::Loaded)
        {
            if (!UnloadDataLayer(LayerPair.Key))
            {
                bAllDependentsUnloaded = false;
                AURACRON_WP_LOG_WARNING(TEXT("Failed to unload dependent layer %s"), *LayerPair.Key);
            }
        }
    }

    return bAllDependentsUnloaded;
}

// === Expression Evaluation Implementation ===

bool FAuracronDataLayerCondition::EvaluateConditionExpression(const FString& Expression, const FAuracronDataLayerContext& Context) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronDataLayerCondition::EvaluateConditionExpression);

    if (Expression.IsEmpty())
    {
        return true;
    }

    // Real expression parser using UE5.6 string processing
    FString CleanExpression = Expression.TrimStartAndEnd();

    // Handle logical operators
    if (CleanExpression.Contains(TEXT(" AND ")))
    {
        TArray<FString> AndParts;
        CleanExpression.ParseIntoArray(AndParts, TEXT(" AND "), true);

        for (const FString& Part : AndParts)
        {
            if (!EvaluateConditionExpression(Part, Context))
            {
                return false;
            }
        }
        return true;
    }

    if (CleanExpression.Contains(TEXT(" OR ")))
    {
        TArray<FString> OrParts;
        CleanExpression.ParseIntoArray(OrParts, TEXT(" OR "), true);

        for (const FString& Part : OrParts)
        {
            if (EvaluateConditionExpression(Part, Context))
            {
                return true;
            }
        }
        return false;
    }

    // Handle comparison operators
    if (CleanExpression.Contains(TEXT(">=")))
    {
        return EvaluateComparisonExpression(CleanExpression, TEXT(">="), Context);
    }
    else if (CleanExpression.Contains(TEXT("==")))
    {
        return EvaluateComparisonExpression(CleanExpression, TEXT("=="), Context);
    }
    else if (CleanExpression.Contains(TEXT(">")))
    {
        return EvaluateComparisonExpression(CleanExpression, TEXT(">"), Context);
    }

    // Handle function calls
    if (CleanExpression.Contains(TEXT("(")))
    {
        return EvaluateFunctionCall(CleanExpression, Context);
    }

    // Handle boolean variables
    return EvaluateBooleanVariable(CleanExpression, Context);
}

bool FAuracronDataLayerCondition::EvaluateComparisonExpression(const FString& Expression, const FString& Operator, const FAuracronDataLayerContext& Context) const
{
    TArray<FString> Parts;
    Expression.ParseIntoArray(Parts, *Operator, true);
    
    if (Parts.Num() != 2)
    {
        return false;
    }
    
    FString LeftSide = Parts[0].TrimStartAndEnd();
    FString RightSide = Parts[1].TrimStartAndEnd();
    
    // Get values from context or parse as numbers
    float LeftValue = GetVariableValue(LeftSide, Context);
    float RightValue = GetVariableValue(RightSide, Context);
    
    // Perform comparison
    if (Operator == TEXT(">="))
    {
        return LeftValue >= RightValue;
    }
    else if (Operator == TEXT(">"))
    {
        return LeftValue > RightValue;
    }
    else if (Operator == TEXT("=="))
    {
        return FMath::IsNearlyEqual(LeftValue, RightValue, 0.001f);
    }
    
    return false;
}

bool FAuracronDataLayerCondition::EvaluateFunctionCall(const FString& Expression, const FAuracronDataLayerContext& Context) const
{
    // Parse function name and parameters
    int32 ParenIndex = Expression.Find(TEXT("("));
    if (ParenIndex == INDEX_NONE)
    {
        return false;
    }
    
    FString FunctionName = Expression.Left(ParenIndex).TrimStartAndEnd();
    FString ParamsString = Expression.Mid(ParenIndex + 1);
    ParamsString = ParamsString.Left(ParamsString.Len() - 1); // Remove closing paren
    
    TArray<FString> ParametersList;
    ParamsString.ParseIntoArray(ParametersList, TEXT(","), true);

    // Handle built-in functions
    if (FunctionName == TEXT("IsQuestCompleted"))
    {
        if (ParametersList.Num() > 0)
        {
            FString QuestName = ParametersList[0].TrimStartAndEnd().TrimQuotes();
            return Context.CompletedQuests.Contains(QuestName);
        }
    }
    else if (FunctionName == TEXT("GetPlayerLevel"))
    {
        return Context.PlayerLevel > 0;
    }
    else if (FunctionName == TEXT("GetTimeOfDay"))
    {
        return Context.TimeOfDay > 0.0f;
    }
    else if (FunctionName == TEXT("HasItem"))
    {
        if (Parameters.Num() > 0)
        {
            FString ItemName = ParametersList[0].TrimStartAndEnd().TrimQuotes();
            return Context.PlayerInventory.Contains(ItemName);
        }
    }
    
    return false;
}

bool FAuracronDataLayerCondition::EvaluateBooleanVariable(const FString& Expression, const FAuracronDataLayerContext& Context) const
{
    FString VarName = Expression.TrimStartAndEnd();
    
    // Handle negation
    bool bNegate = false;
    if (VarName.StartsWith(TEXT("!")))
    {
        bNegate = true;
        VarName = VarName.Mid(1).TrimStartAndEnd();
    }
    
    // Check boolean variables in context
    bool Result = false;
    if (const bool* BoolValue = Context.BooleanVariables.Find(VarName))
    {
        Result = *BoolValue;
    }
    else if (VarName == TEXT("IsDay"))
    {
        Result = Context.TimeOfDay >= 6.0f && Context.TimeOfDay < 18.0f;
    }
    else if (VarName == TEXT("IsNight"))
    {
        Result = Context.TimeOfDay < 6.0f || Context.TimeOfDay >= 18.0f;
    }
    
    return bNegate ? !Result : Result;
}

float FAuracronDataLayerCondition::GetVariableValue(const FString& Variable, const FAuracronDataLayerContext& Context) const
{
    FString VarName = Variable.TrimStartAndEnd();
    
    // Try to parse as number first
    if (VarName.IsNumeric())
    {
        return FCString::Atof(*VarName);
    }
    
    // Check context variables
    if (const float* FloatValue = Context.FloatVariables.Find(VarName))
    {
        return *FloatValue;
    }
    
    // Handle built-in variables
    if (VarName == TEXT("PlayerLevel"))
    {
        return static_cast<float>(Context.PlayerLevel);
    }
    else if (VarName == TEXT("TimeOfDay"))
    {
        return Context.TimeOfDay;
    }
    
    return 0.0f;
}
