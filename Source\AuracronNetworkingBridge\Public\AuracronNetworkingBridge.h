// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema Multiplayer 5v5 Bridge
// Integração C++ para networking autoritativo usando APIs modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "Components/GameFrameworkComponent.h"
#include "OnlineSubsystem.h"
#include "OnlineSubsystemUtils.h"
#include "Interfaces/OnlineSessionInterface.h"
#include "GameFramework/OnlineSession.h"
#include "OnlineSessionSettings.h"
#include "Interfaces/OnlineIdentityInterface.h"
#include "Interfaces/OnlineUserInterface.h"
#include "Interfaces/OnlineFriendsInterface.h"
#include "Interfaces/OnlinePresenceInterface.h"
#include "Interfaces/OnlineStatsInterface.h"
#include "Interfaces/OnlineLeaderboardInterface.h"
#include "Interfaces/OnlineAchievementsInterface.h"
#include "Engine/NetConnection.h"
#include "Engine/NetDriver.h"
#include "Engine/ReplicationDriver.h"
#include "Net/Core/PushModel/PushModel.h"
// Usando APIs de replicação modernas do UE 5.6
#include "Iris/ReplicationSystem/ReplicationSystem.h"
#include "GameFramework/GameMode.h"
#include "GameFramework/GameState.h"
#include "GameFramework/PlayerState.h"
#include "GameFramework/PlayerController.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "GameplayTagContainer.h"
#include "TimerManager.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "AuracronNetworkingBridge.generated.h"

// Forward Declarations
class IOnlineSubsystem;
class IOnlineSessionInterface;
class IOnlineIdentityInterface;
class UReplicationGraph;
class AGameMode;
class AGameState;
class APlayerState;
class APlayerController;

/**
 * Enumeração para tipos de conexão de rede
 */
UENUM(BlueprintType)
enum class EAuracronNetworkConnectionType : uint8
{
    None            UMETA(DisplayName = "None"),
    Listen          UMETA(DisplayName = "Listen Server"),
    Dedicated       UMETA(DisplayName = "Dedicated Server"),
    Client          UMETA(DisplayName = "Client"),
    Standalone      UMETA(DisplayName = "Standalone")
};

/**
 * Enumeração para estado da sessão
 */
UENUM(BlueprintType)
enum class EAuracronSessionState : uint8
{
    None                UMETA(DisplayName = "None"),
    Creating            UMETA(DisplayName = "Creating"),
    Searching           UMETA(DisplayName = "Searching"),
    Joining             UMETA(DisplayName = "Joining"),
    InLobby             UMETA(DisplayName = "In Lobby"),
    InGame              UMETA(DisplayName = "In Game"),
    Ending              UMETA(DisplayName = "Ending"),
    Disconnected        UMETA(DisplayName = "Disconnected"),
    Error               UMETA(DisplayName = "Error")
};

/**
 * Enumeração para tipos de validação server-side
 */
UENUM(BlueprintType)
enum class EAuracronServerValidationType : uint8
{
    None                UMETA(DisplayName = "None"),
    Movement            UMETA(DisplayName = "Movement"),
    Ability             UMETA(DisplayName = "Ability"),
    Damage              UMETA(DisplayName = "Damage"),
    ItemUsage           UMETA(DisplayName = "Item Usage"),
    Experience          UMETA(DisplayName = "Experience"),
    Gold                UMETA(DisplayName = "Gold"),
    Position            UMETA(DisplayName = "Position"),
    Timing              UMETA(DisplayName = "Timing"),
    Input               UMETA(DisplayName = "Input")
};

/**
 * Estrutura para configuração básica de sessão
 */
USTRUCT(BlueprintType)
struct AURACRONNETWORKINGBRIDGE_API FAuracronSessionConfiguration
{
    GENERATED_BODY()

    /** Nome da sessão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    FString SessionName = TEXT("AuracronSession");

    /** Número máximo de jogadores */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration", meta = (ClampMin = "2", ClampMax = "10"))
    int32 MaxPlayers = 10;

    /** Sessão é pública */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    bool bIsPublic = true;

    /** Modo de jogo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    FString GameMode = TEXT("AuracronGameMode");

    /** Nome do mapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    FString MapName = TEXT("AuracronMap");

    /** Região preferida */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    FString PreferredRegion = TEXT("US-East");

    /** Ping máximo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    int32 MaxPing = 150;

    /** Usar voice chat */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    bool bUseVoiceChat = true;

    /** Usar anti-cheat */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    bool bUseAntiCheat = true;
};

/**
 * Estrutura para configuração de sessão multiplayer
 */
USTRUCT(BlueprintType)
struct AURACRONNETWORKINGBRIDGE_API FAuracronNetworkingSessionConfiguration
{
    GENERATED_BODY()

    /** Nome da sessão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    FString SessionName = TEXT("AuracronMatch");

    /** Número máximo de jogadores */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration", meta = (ClampMin = "2", ClampMax = "10"))
    int32 MaxPlayers = 10;

    /** Número mínimo de jogadores para iniciar */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration", meta = (ClampMin = "2", ClampMax = "10"))
    int32 MinPlayersToStart = 10;

    /** Sessão é pública */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    bool bIsPublic = true;

    /** Permite espectadores */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    bool bAllowSpectators = false;

    /** Usa anti-cheat */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    bool bUseAntiCheat = true;

    /** Região preferida */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    FString PreferredRegion = TEXT("us-east-1");

    /** Modo de jogo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    FString GameMode = TEXT("AuracronClassic");

    /** Mapa da partida */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    FString MapName = TEXT("AuracronRift");

    /** Duração máxima da partida (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration", meta = (ClampMin = "600", ClampMax = "3600"))
    int32 MaxMatchDuration = 1800; // 30 minutos

    /** Tempo limite para conexão (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration", meta = (ClampMin = "10", ClampMax = "120"))
    int32 ConnectionTimeout = 60;

    /** Ping máximo permitido */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration", meta = (ClampMin = "50", ClampMax = "500"))
    int32 MaxPing = 150;

    /** Usa voice chat */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    bool bUseVoiceChat = true;

    /** Permite reconexão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    bool bAllowReconnection = true;

    /** Tempo para reconexão (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration", meta = (ClampMin = "30", ClampMax = "300"))
    int32 ReconnectionTimeLimit = 180;
};

/**
 * Estrutura para informações de jogador
 */
USTRUCT(BlueprintType)
struct AURACRONNETWORKINGBRIDGE_API FAuracronPlayerInfo
{
    GENERATED_BODY()

    /** ID único do jogador */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Info")
    FString PlayerID;

    /** Nome do jogador */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Info")
    FString PlayerName;

    /** Nível da conta */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Info", meta = (ClampMin = "1", ClampMax = "500"))
    int32 AccountLevel = 1;

    /** Ranking competitivo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Info")
    FString CompetitiveRank = TEXT("Unranked");

    /** Ping atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Info", meta = (ClampMin = "0", ClampMax = "1000"))
    int32 CurrentPing = 0;

    /** Está conectado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Info")
    bool bIsConnected = false;

    /** Está pronto para iniciar */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Info")
    bool bIsReady = false;

    /** Time do jogador (0 = Team A, 1 = Team B) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Info", meta = (ClampMin = "0", ClampMax = "1"))
    int32 TeamID = 0;

    /** Posição no time (0-4) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Info", meta = (ClampMin = "0", ClampMax = "4"))
    int32 TeamPosition = 0;

    /** Campeão selecionado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Info")
    FString SelectedChampion;

    /** Sígilos selecionados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Info")
    TArray<FString> SelectedSigilos;

    /** Estatísticas da partida */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Info")
    TMap<FString, int32> MatchStats;

    /** Tempo de conexão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Info")
    FDateTime ConnectionTime;

    /** Última atividade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Info")
    FDateTime LastActivity;
};

/**
 * Estrutura para configuração de anti-cheat
 */
USTRUCT(BlueprintType)
struct AURACRONNETWORKINGBRIDGE_API FAuracronNetworkingAntiCheatConfiguration
{
    GENERATED_BODY()

    /** Validação de movimento habilitada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Anti-Cheat Configuration")
    bool bValidateMovement = true;

    /** Validação de habilidades habilitada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Anti-Cheat Configuration")
    bool bValidateAbilities = true;

    /** Validação de dano habilitada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Anti-Cheat Configuration")
    bool bValidateDamage = true;

    /** Validação de timing habilitada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Anti-Cheat Configuration")
    bool bValidateTiming = true;

    /** Tolerância de movimento (unidades por segundo) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Anti-Cheat Configuration", meta = (ClampMin = "100.0", ClampMax = "2000.0"))
    float MovementTolerance = 1000.0f;

    /** Tolerância de ping (ms) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Anti-Cheat Configuration", meta = (ClampMin = "10", ClampMax = "500"))
    int32 PingTolerance = 200;

    /** Máximo de ações por segundo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Anti-Cheat Configuration", meta = (ClampMin = "1", ClampMax = "50"))
    int32 MaxActionsPerSecond = 20;

    /** Tempo de cooldown para detecção de spam */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Anti-Cheat Configuration", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float SpamDetectionCooldown = 1.0f;

    /** Número máximo de violações antes de kick */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Anti-Cheat Configuration", meta = (ClampMin = "1", ClampMax = "20"))
    int32 MaxViolationsBeforeKick = 5;

    /** Log de atividades suspeitas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Anti-Cheat Configuration")
    bool bLogSuspiciousActivity = true;

    /** Reportar automaticamente cheaters */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Anti-Cheat Configuration")
    bool bAutoReportCheaters = true;
};

/**
 * Estrutura para configuração de replicação
 */
USTRUCT(BlueprintType)
struct AURACRONNETWORKINGBRIDGE_API FAuracronReplicationConfiguration
{
    GENERATED_BODY()

    /** Usar Replication Graph */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication Configuration")
    bool bUseReplicationGraph = true;

    /** Usar Iris Networking */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication Configuration")
    bool bUseIrisNetworking = true;

    /** Usar Network Prediction */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication Configuration")
    bool bUseNetworkPrediction = true;

    /** Frequência de replicação (Hz) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication Configuration", meta = (ClampMin = "10", ClampMax = "120"))
    int32 ReplicationFrequency = 60;

    /** Distância máxima de replicação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication Configuration", meta = (ClampMin = "1000.0", ClampMax = "50000.0"))
    float MaxReplicationDistance = 15000.0f;

    /** Usar compressão de pacotes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication Configuration")
    bool bUsePacketCompression = true;

    /** Usar delta compression */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication Configuration")
    bool bUseDeltaCompression = true;

    /** Prioridade de replicação para campeões */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication Configuration", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float ChampionReplicationPriority = 5.0f;

    /** Prioridade de replicação para habilidades */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication Configuration", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float AbilityReplicationPriority = 8.0f;

    /** Prioridade de replicação para efeitos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication Configuration", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float EffectReplicationPriority = 3.0f;

    /** Usar relevância baseada em distância */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication Configuration")
    bool bUseDistanceBasedRelevancy = true;

    /** Usar relevância baseada em line of sight */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication Configuration")
    bool bUseLineOfSightRelevancy = false;

    /** Usar culling baseado em frustum */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication Configuration")
    bool bUseFrustumCulling = true;
};

/**
 * Classe principal do Bridge para Sistema Multiplayer 5v5
 * Responsável pelo gerenciamento completo de networking autoritativo
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Networking", meta = (DisplayName = "AURACRON Networking Bridge", BlueprintSpawnableComponent))
class AURACRONNETWORKINGBRIDGE_API UAuracronNetworkingBridge : public UGameFrameworkComponent
{
    GENERATED_BODY()

public:
    UAuracronNetworkingBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Session Management ===

    /**
     * Criar sessão multiplayer
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Session", CallInEditor)
    bool CreateSession(const FAuracronSessionConfiguration& SessionConfig);

    /**
     * Buscar sessões disponíveis
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Session", CallInEditor)
    bool FindSessions(int32 MaxResults = 50);

    /**
     * Entrar em sessão
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Session", CallInEditor)
    bool JoinSession(const FString& SessionID);

    /**
     * Sair da sessão atual
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Session", CallInEditor)
    bool LeaveSession();

    /**
     * Destruir sessão atual
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Session", CallInEditor)
    bool DestroySession();

    /**
     * Obter estado atual da sessão
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Session", CallInEditor)
    EAuracronSessionState GetSessionState() const { return CurrentSessionState; }

    /**
     * Obter informações da sessão atual
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Session", CallInEditor)
    FAuracronNetworkingSessionConfiguration GetCurrentSessionInfo() const { return CurrentSessionConfig; }

    // === Player Management ===

    /**
     * Obter lista de jogadores conectados
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Players", CallInEditor)
    TArray<FAuracronPlayerInfo> GetConnectedPlayers() const;

    /**
     * Obter informações de um jogador específico
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Players", CallInEditor)
    FAuracronPlayerInfo GetPlayerInfo(const FString& PlayerID) const;

    /**
     * Atualizar informações de jogador
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Players", CallInEditor)
    bool UpdatePlayerInfo(const FString& PlayerID, const FAuracronPlayerInfo& PlayerInfo);

    /**
     * Kickar jogador
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Players", CallInEditor)
    bool KickPlayer(const FString& PlayerID, const FString& Reason);

    /**
     * Banir jogador
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Players", CallInEditor)
    bool BanPlayer(const FString& PlayerID, const FString& Reason, int32 DurationMinutes = 0);

    // === Server Validation ===

    /**
     * Validar ação do jogador no servidor
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Validation")
    bool ValidatePlayerAction(const FString& PlayerID, EAuracronServerValidationType ValidationType, const TMap<FString, FString>& ActionData);

    /**
     * Reportar atividade suspeita
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Validation")
    bool ReportSuspiciousActivity(const FString& PlayerID, const FString& ActivityDescription, const TMap<FString, FString>& Evidence);

    /**
     * Verificar se jogador está sendo monitorado
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Validation", CallInEditor)
    bool IsPlayerUnderSurveillance(const FString& PlayerID) const;

    // === Network Statistics ===

    /**
     * Obter estatísticas de rede
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Statistics", CallInEditor)
    TMap<FString, float> GetNetworkStatistics() const;

    /**
     * Obter ping médio da sessão
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Statistics", CallInEditor)
    float GetAverageSessionPing() const;

    /**
     * Obter taxa de perda de pacotes
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Statistics", CallInEditor)
    float GetPacketLossRate() const;

    /**
     * Obter largura de banda utilizada
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Networking|Statistics", CallInEditor)
    float GetBandwidthUsage() const;

protected:
    // === Internal Management Methods ===

    /** Inicializar sistema de networking */
    bool InitializeNetworkingSystem();

    /** Configurar Online Subsystem */
    bool SetupOnlineSubsystem();

    /** Configurar Replication Graph */
    bool SetupReplicationGraph();

    /** Configurar anti-cheat */
    bool SetupAntiCheat();

    /** Processar validações server-side */
    void ProcessServerValidations(float DeltaTime);

    /** Monitorar conexões de jogadores */
    void MonitorPlayerConnections(float DeltaTime);

    /** Atualizar estatísticas de rede */
    void UpdateNetworkStatistics(float DeltaTime);

    /** Validar configuração de sessão */
    bool ValidateSessionConfiguration(const FAuracronSessionConfiguration& SessionConfig) const;

    // === Callback Methods ===

    /** Callback para criação de sessão */
    void OnCreateSessionComplete(FName SessionName, bool bWasSuccessful);

    /** Callback para busca de sessões */
    void OnFindSessionsComplete(bool bWasSuccessful);

    /** Callback para entrada em sessão */
    void OnJoinSessionComplete(FName SessionName, EOnJoinSessionCompleteResult::Type Result);

    /** Callback para saída de sessão */
    void OnDestroySessionComplete(FName SessionName, bool bWasSuccessful);

    /** Callback para conexão de jogador */
    void OnPlayerConnected(const FString& PlayerID);

    /** Callback para desconexão de jogador */
    void OnPlayerDisconnected(const FString& PlayerID);

public:
    // === Configuration Properties ===

    /** Configuração da sessão atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    FAuracronNetworkingSessionConfiguration CurrentSessionConfig;

    /** Configuração de anti-cheat */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronNetworkingAntiCheatConfiguration AntiCheatConfig;

    /** Configuração de replicação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronReplicationConfiguration ReplicationConfig;

    /** Estado atual da sessão */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_SessionState)
    EAuracronSessionState CurrentSessionState = EAuracronSessionState::None;

    /** Tipo de conexão atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_ConnectionType)
    EAuracronNetworkConnectionType CurrentConnectionType = EAuracronNetworkConnectionType::None;

    /** Jogadores conectados */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_ConnectedPlayers)
    TArray<FAuracronPlayerInfo> ConnectedPlayers;

    /** Estatísticas de rede */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    TMap<FString, float> NetworkStatistics;

private:
    // === Internal State ===

    /** Online Subsystem Interface */
    IOnlineSubsystem* OnlineSubsystem = nullptr;

    /** Session Interface */
    TSharedPtr<IOnlineSessionInterface> SessionInterface;

    /** Identity Interface */
    TSharedPtr<IOnlineIdentityInterface> IdentityInterface;

    /** Sistema inicializado */
    bool bSystemInitialized = false;

    /** É servidor dedicado */
    bool bIsDedicatedServer = false;

    /** Sessões encontradas */
    TSharedPtr<FOnlineSessionSearch> SessionSearch;

    /** Timer para monitoramento */
    FTimerHandle MonitoringTimer;

    /** Timer para estatísticas */
    FTimerHandle StatisticsTimer;

    /** Mutex para thread safety */
    mutable FCriticalSection NetworkingMutex;

    /** Jogadores sob vigilância */
    TSet<FString> PlayersUnderSurveillance;

    /** Histórico de violações */
    TMap<FString, int32> PlayerViolationCounts;

    // === Replication Callbacks ===

    UFUNCTION()
    void OnRep_SessionState();

    UFUNCTION()
    void OnRep_ConnectionType();

    UFUNCTION()
    void OnRep_ConnectedPlayers();

public:
    // === Delegates ===

    /** Delegate chamado quando sessão é criada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSessionCreated, FString, SessionName, bool, bSuccess);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Networking|Events")
    FOnSessionCreated OnSessionCreated;

    /** Delegate chamado quando jogador se conecta */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPlayerConnectedDelegate, FAuracronPlayerInfo, PlayerInfo);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Networking|Events")
    FOnPlayerConnectedDelegate OnPlayerConnectedDelegate;

    /** Delegate chamado quando jogador se desconecta */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPlayerDisconnectedDelegate, FString, PlayerID, FString, Reason);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Networking|Events")
    FOnPlayerDisconnectedDelegate OnPlayerDisconnectedDelegate;

    /** Delegate chamado quando atividade suspeita é detectada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnSuspiciousActivityDetected, FString, PlayerID, FString, ActivityType, FString, Evidence);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Networking|Events")
    FOnSuspiciousActivityDetected OnSuspiciousActivityDetected;

    /** Delegate chamado quando estado da sessão muda */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSessionStateChanged, EAuracronSessionState, OldState, EAuracronSessionState, NewState);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Networking|Events")
    FOnSessionStateChanged OnSessionStateChanged;
};

