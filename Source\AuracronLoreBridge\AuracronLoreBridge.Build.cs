// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Lore DinÃ¢mico Bridge Build Configuration
using UnrealBuildTool;
public class AuracronLoreBridge : ModuleRules
{
    public AuracronLoreBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "ModularGameplay",
                "NetCore",
                "ReplicationGraph",
                "EnhancedInput",
                "InputCore",
                "UMG",
                "Slate",
                "SlateCore",
                "CommonUI",
                "CommonInput",
                "DeveloperSettings",
                "EngineSettings",
                "NavigationSystem"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "RenderCore",
                "RHI",
                "Json",
                "AudioMixer",
                "SignalProcessing",
                "MovieScene",
                "MovieSceneTracks",
                "LevelSequence",
                "CinematicCamera",
                "MediaAssets",
                "MediaUtils",
                "Localization",
                "ICU",
                "NiagaraCore",
                "NiagaraShader",
                "MetasoundFrontend",
                "OnlineSubsystemUtils",
                "EOSShared"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "ToolMenus",
                    "EditorStyle",
                    "EditorWidgets",
                    "UnrealEd",
                    "PropertyEditor",
                    "KismetCompiler",
                    "BlueprintGraph",
                    "Kismet",
                    "ToolMenus",
                    "SequencerWidgets",
                    "UMGEditor",
                    "CommonUIEditor"
                }
            );
        }
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {}
        );
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_COMMON_UI=1");
        PublicDefinitions.Add("WITH_LEVEL_SEQUENCE=1");
        PublicDefinitions.Add("WITH_CINEMATIC_CAMERA=1");
        PublicDefinitions.Add("WITH_LOCALIZATION=1");
        PublicDefinitions.Add("WITH_INTERNATIONALIZATION=1");
        PublicDefinitions.Add("WITH_FIREBASE=1");
        PublicDefinitions.Add("WITH_CLOUD_STORAGE=1");
        PublicDefinitions.Add("WITH_DYNAMIC_CONTENT=1");
        // Lore features
        PublicDefinitions.Add("AURACRON_DYNAMIC_LORE=1");
        PublicDefinitions.Add("AURACRON_INTERACTIVE_LORE=1");
        PublicDefinitions.Add("AURACRON_LORE_DISCOVERY=1");
        PublicDefinitions.Add("AURACRON_LORE_PROGRESSION=1");
        PublicDefinitions.Add("AURACRON_LORE_ANALYTICS=1");
        PublicDefinitions.Add("AURACRON_LORE_LOCALIZATION=1");
        PublicDefinitions.Add("AURACRON_LORE_CINEMATICS=1");
        PublicDefinitions.Add("AURACRON_LORE_AUDIO=1");
        PublicDefinitions.Add("AURACRON_LORE_CODEX=1");
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_LORE=1");
            PublicDefinitions.Add("AURACRON_COMPRESSED_LORE=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_LORE=0");
            PublicDefinitions.Add("AURACRON_COMPRESSED_LORE=0");
        }
        // Development vs Shipping
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_LORE_DEBUG=1");
            PublicDefinitions.Add("AURACRON_LORE_EDITOR=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_LORE_DEBUG=0");
            PublicDefinitions.Add("AURACRON_LORE_EDITOR=0");
        }
    }
}


