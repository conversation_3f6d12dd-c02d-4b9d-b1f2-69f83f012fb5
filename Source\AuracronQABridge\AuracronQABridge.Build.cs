﻿using UnrealBuildTool;
public class AuracronQABridge : ModuleRules
{
    public AuracronQABridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "AutomationController",
                "AutomationMessages",
                "AutomationTest",
                "AutomationWorker",
                "FunctionalTesting",
                "ScreenShotComparisonTools",
                "ImageWrapper",
                "RenderCore",
                "RHI",
                "Slate",
                "SlateCore",
                "UMG"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "Json",
                "Json",
                "InputCore",
                "RenderCore",
                "RHI",
                "ImageWrapper",
                "AutomationController",
                "AutomationMessages",
                "AutomationTest",
                "AutomationWorker",
                "FunctionalTesting",
                "ScreenShotComparisonTools",
                "SessionServices",
                "GameplayDebugger",
                "EngineSettings",
                "DeveloperSettings"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "ToolMenus",
                    "EditorStyle",
                    "EditorWidgets",
                    "UnrealEd",
                    "LevelEditor",
                    "PropertyEditor",
                    "DetailCustomizations",
                    "ComponentVisualizers"
                }
            );
        }
        // Enable RTTI for this module
        bUseRTTI = true;
        // Enable exceptions for this module
        bEnableExceptions = true;
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        // Include paths
        PublicIncludePaths.AddRange(new string[] 
        {
            "AuracronQABridge/Public"
        });
        PrivateIncludePaths.AddRange(new string[] 
        {
            "AuracronQABridge/Private"
        });
    }
}



