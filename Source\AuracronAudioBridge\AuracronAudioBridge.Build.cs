﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Ãudio MetaSounds Bridge Build Configuration
using UnrealBuildTool;
public class AuracronAudioBridge : ModuleRules
{
    public AuracronAudioBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "AudioMixer",
            "Synthesis",
                "AudioExtensions",
                "SignalProcessing",
                "SoundFieldRendering",
                "Synthesis","MetasoundFrontend",
                "MetasoundStandardNodes",
                "MetasoundGenerator",
                "MetasoundEngine",
                "WaveTable",
                "GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "ModularGameplay",
                "NetCore",
                "ReplicationGraph",
                "EnhancedInput",
                "InputCore",
                "UMG",
                "Slate",
                "SlateCore","DeveloperSettings",
                "EngineSettings"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "RenderCore",
                "RHI",
                "Json",
                "OnlineSubsystemUtils",
                "Sockets",
                "Networking",
                "ChaosCore",
                "PhysicsCore",
                "NiagaraCore",
                "NiagaraShader",
                "MediaAssets",
                "MediaUtils"
            }
        );

        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "ToolMenus",
                    "EditorStyle",
                    "EditorWidgets",
                    "UnrealEd",
                    "PropertyEditor",
                    "KismetCompiler",
                    "BlueprintGraph",
                    "Kismet",
                    "AudioEditor",
                    "MetasoundEditor",
                    "MetasoundGraphCore",
                    "WaveTableEditor",
                    "SynthesisEditor"
                }
            );
        }
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {}
        );
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_METASOUNDS=1");
        PublicDefinitions.Add("WITH_AUDIO_MIXER=1");
        PublicDefinitions.Add("WITH_SYNTHESIS=1");
        PublicDefinitions.Add("WITH_SIGNAL_PROCESSING=1");
        PublicDefinitions.Add("WITH_SOUND_FIELD_RENDERING=1");
        PublicDefinitions.Add("WITH_SPATIAL_AUDIO=1");
        PublicDefinitions.Add("WITH_VOICE_CHAT=1");
        PublicDefinitions.Add("WITH_AUDIO_STREAMING=1");
        PublicDefinitions.Add("WITH_PROCEDURAL_AUDIO=1");
        // Audio features
        PublicDefinitions.Add("AURACRON_3D_AUDIO=1");
        PublicDefinitions.Add("AURACRON_DYNAMIC_MUSIC=1");
        PublicDefinitions.Add("AURACRON_ADAPTIVE_AUDIO=1");
        PublicDefinitions.Add("AURACRON_REALM_AUDIO=1");
        PublicDefinitions.Add("AURACRON_CHAMPION_VOICES=1");
        PublicDefinitions.Add("AURACRON_ABILITY_SOUNDS=1");
        PublicDefinitions.Add("AURACRON_AMBIENT_AUDIO=1");
        PublicDefinitions.Add("AURACRON_VOICE_PROCESSING=1");
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_AUDIO=1");
            PublicDefinitions.Add("AURACRON_COMPRESSED_AUDIO=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_AUDIO=0");
            PublicDefinitions.Add("AURACRON_COMPRESSED_AUDIO=0");
        }
        // Development vs Shipping
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_AUDIO_DEBUG=1");
            PublicDefinitions.Add("AURACRON_AUDIO_PROFILING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_AUDIO_DEBUG=0");
            PublicDefinitions.Add("AURACRON_AUDIO_PROFILING=0");
        }
        // Performance optimizations
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("AURACRON_OPTIMIZE_AUDIO=1");
            PublicDefinitions.Add("AURACRON_AUDIO_STREAMING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_OPTIMIZE_AUDIO=0");
            PublicDefinitions.Add("AURACRON_AUDIO_STREAMING=0");
        }
    }
}

