// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Material Variation Implementation
// Bridge 4.11: Foliage - Material Variation

#include "AuracronFoliageMaterialVariation.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageInstanced.h"
#include "AuracronFoliageBiome.h"
#include "AuracronFoliageSeasonal.h"
// #include "AuracronPCGMaterialSystem.h" // TODO: Create this system
#include "AuracronFoliageBridge.h"

// UE5.6 Material includes
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Materials/MaterialInstanceConstant.h"

// Foliage includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "InstancedFoliageActor.h"

// Texture includes
#include "Engine/Texture2D.h"
#include "Engine/TextureRenderTarget2D.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformMemory.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Color.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

// =============================================================================
// FOLIAGE MATERIAL VARIATION MANAGER IMPLEMENTATION
// =============================================================================

UAuracronFoliageMaterialVariationManager* UAuracronFoliageMaterialVariationManager::Instance = nullptr;

UAuracronFoliageMaterialVariationManager* UAuracronFoliageMaterialVariationManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronFoliageMaterialVariationManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronFoliageMaterialVariationManager::Initialize(const FAuracronFoliageMaterialVariationConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Material Variation Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize collections
    MaterialInstances.Empty();
    ColorVariations.Empty();
    TextureBlending.Empty();
    MaterialCache.Empty();
    TextureCache.Empty();

    // Initialize performance data
    PerformanceData = FAuracronMaterialVariationPerformanceData();
    LastPerformanceUpdate = 0.0f;
    LastMaterialUpdate = 0.0f;

    // Get world reference
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        ManagedWorld = World;
    }

    bIsInitialized = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Material Variation Manager initialized with strategy: %s, color variation: %s"), 
                              *UEnum::GetValueAsString(Configuration.VariationStrategy),
                              Configuration.bEnableColorVariation ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliageMaterialVariationManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Clear all collections
    MaterialInstances.Empty();
    ColorVariations.Empty();
    TextureBlending.Empty();
    MaterialCache.Empty();
    TextureCache.Empty();

    // Reset references
    ManagedWorld.Reset();
    SeasonalManager.Reset();

    bIsInitialized = false;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Material Variation Manager shutdown completed"));
}

bool UAuracronFoliageMaterialVariationManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronFoliageMaterialVariationManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        return;
    }

    // Update material instances
    LastMaterialUpdate += DeltaTime;
    if (LastMaterialUpdate >= Configuration.MaterialUpdateInterval)
    {
        UpdateMaterialInstancesInternal(DeltaTime);
        LastMaterialUpdate = 0.0f;
    }

    // Update color variations
    if (Configuration.bEnableColorVariation)
    {
        UpdateColorVariationsInternal(DeltaTime);
    }

    // Update texture blending
    if (Configuration.bEnableTextureBlending)
    {
        UpdateTextureBlendingInternal(DeltaTime);
    }

    // Update performance monitoring
    LastPerformanceUpdate += DeltaTime;
    if (LastPerformanceUpdate >= 1.0f) // Update every second
    {
        UpdatePerformanceMetrics();
        LastPerformanceUpdate = 0.0f;
    }

    // Cleanup unused materials and textures periodically
    static float LastCleanupTime = 0.0f;
    LastCleanupTime += DeltaTime;
    if (LastCleanupTime >= 30.0f) // Cleanup every 30 seconds
    {
        CleanupUnusedMaterials();
        CleanupUnusedTextures();
        LastCleanupTime = 0.0f;
    }
}

void UAuracronFoliageMaterialVariationManager::SetConfiguration(const FAuracronFoliageMaterialVariationConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Material Variation configuration updated"));
}

FAuracronFoliageMaterialVariationConfiguration UAuracronFoliageMaterialVariationManager::GetConfiguration() const
{
    return Configuration;
}

FString UAuracronFoliageMaterialVariationManager::CreateMaterialInstance(const FString& FoliageInstanceId, UMaterialInterface* BaseMaterial)
{
    if (!bIsInitialized || !BaseMaterial)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Material Variation Manager not initialized or invalid base material"));
        return FString();
    }

    FScopeLock Lock(&MaterialVariationLock);

    FString InstanceId = GenerateMaterialInstanceId();

    FAuracronMaterialInstanceData NewInstanceData;
    NewInstanceData.InstanceId = InstanceId;
    NewInstanceData.FoliageInstanceId = FoliageInstanceId;
    NewInstanceData.BaseMaterial = BaseMaterial;
    NewInstanceData.bIsGenerated = false;
    NewInstanceData.bNeedsUpdate = true;
    NewInstanceData.LastUpdateTime = FDateTime::Now();

    // Create dynamic material instance
    UMaterialInstanceDynamic* DynamicMaterial = CreateDynamicMaterialInstanceInternal(BaseMaterial, NewInstanceData);
    if (DynamicMaterial)
    {
        NewInstanceData.DynamicMaterial = DynamicMaterial;
        NewInstanceData.bIsGenerated = true;
        
        // Cache the material
        MaterialCache.Add(InstanceId, DynamicMaterial);
        
        OnMaterialInstanceCreated.Broadcast(InstanceId, DynamicMaterial);
    }

    MaterialInstances.Add(InstanceId, NewInstanceData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Material instance created: %s for foliage: %s"), 
                              *InstanceId, *FoliageInstanceId);

    return InstanceId;
}

FString UAuracronFoliageMaterialVariationManager::CreateColorVariation(const FString& FoliageTypeId, const FAuracronColorVariationData& ColorData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Material Variation Manager not initialized"));
        return FString();
    }

    FScopeLock Lock(&MaterialVariationLock);

    FString VariationId = GenerateColorVariationId();

    FAuracronColorVariationData NewColorData = ColorData;
    NewColorData.VariationId = VariationId;
    NewColorData.FoliageTypeId = FoliageTypeId;
    NewColorData.CreationTime = FDateTime::Now();

    ColorVariations.Add(VariationId, NewColorData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Color variation created: %s for foliage type: %s"), 
                              *VariationId, *FoliageTypeId);

    return VariationId;
}

FLinearColor UAuracronFoliageMaterialVariationManager::ApplyColorVariation(const FLinearColor& BaseColor, const FAuracronColorVariationData& VariationData) const
{
    FLinearColor ResultColor = BaseColor;

    switch (VariationData.VariationMode)
    {
        case EAuracronColorVariationMode::HSV:
            ResultColor = CalculateHSVVariation(BaseColor, VariationData);
            break;

        case EAuracronColorVariationMode::RGB:
            ResultColor = CalculateRGBVariation(BaseColor, VariationData);
            break;

        case EAuracronColorVariationMode::Palette:
            if (VariationData.ColorPalette.Num() > 0)
            {
                float SelectionValue = FMath::FRandRange(0.0f, 1.0f);
                ResultColor = SelectColorFromPalette(VariationData, SelectionValue);
            }
            break;

        case EAuracronColorVariationMode::Gradient:
            if (VariationData.GradientColors.Num() > 0)
            {
                float InterpolationValue = FMath::FRandRange(0.0f, 1.0f);
                ResultColor = InterpolateGradient(VariationData, InterpolationValue);
            }
            break;

        case EAuracronColorVariationMode::Seasonal:
            // Integration with seasonal system would be handled here
            ResultColor = BaseColor;
            break;

        default:
            ResultColor = BaseColor;
            break;
    }

    // Apply variation intensity
    ResultColor = FMath::Lerp(BaseColor, ResultColor, VariationData.VariationIntensity);

    return ResultColor;
}

void UAuracronFoliageMaterialVariationManager::ValidateConfiguration()
{
    // Validate color variation settings
    Configuration.ColorVariationIntensity = FMath::Clamp(Configuration.ColorVariationIntensity, 0.0f, 2.0f);
    Configuration.HueVariationRange = FMath::Clamp(Configuration.HueVariationRange, 0.0f, 1.0f);
    Configuration.SaturationVariationRange = FMath::Clamp(Configuration.SaturationVariationRange, 0.0f, 2.0f);
    Configuration.ValueVariationRange = FMath::Clamp(Configuration.ValueVariationRange, 0.0f, 2.0f);

    // Validate texture blending settings
    Configuration.BlendingOpacity = FMath::Clamp(Configuration.BlendingOpacity, 0.0f, 1.0f);

    // Validate procedural settings
    Configuration.ProceduralVariationStrength = FMath::Clamp(Configuration.ProceduralVariationStrength, 0.1f, 5.0f);

    // Validate performance settings
    Configuration.MaxMaterialInstancesPerFrame = FMath::Max(1, Configuration.MaxMaterialInstancesPerFrame);
    Configuration.MaterialUpdateInterval = FMath::Max(0.01f, Configuration.MaterialUpdateInterval);
    Configuration.MaxCachedMaterials = FMath::Max(10, Configuration.MaxCachedMaterials);
}

FString UAuracronFoliageMaterialVariationManager::GenerateMaterialInstanceId() const
{
    return FString::Printf(TEXT("MaterialInstance_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageMaterialVariationManager::GenerateColorVariationId() const
{
    return FString::Printf(TEXT("ColorVariation_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageMaterialVariationManager::GenerateTextureBlendingId() const
{
    return FString::Printf(TEXT("TextureBlending_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

void UAuracronFoliageMaterialVariationManager::UpdateMaterialInstancesInternal(float DeltaTime)
{
    FScopeLock Lock(&MaterialVariationLock);

    int32 UpdatedInstances = 0;
    const int32 MaxUpdatesThisFrame = Configuration.MaxMaterialInstancesPerFrame;

    for (auto& InstancePair : MaterialInstances)
    {
        if (UpdatedInstances >= MaxUpdatesThisFrame)
        {
            break;
        }

        FAuracronMaterialInstanceData& InstanceData = InstancePair.Value;
        if (InstanceData.bNeedsUpdate && InstanceData.DynamicMaterial.IsValid())
        {
            UMaterialInstanceDynamic* DynamicMaterial = InstanceData.DynamicMaterial.Get();

            // Update scalar parameters
            for (const auto& ScalarParam : InstanceData.ScalarParameters)
            {
                DynamicMaterial->SetScalarParameterValue(*ScalarParam.Key, ScalarParam.Value);
            }

            // Update vector parameters
            for (const auto& VectorParam : InstanceData.VectorParameters)
            {
                DynamicMaterial->SetVectorParameterValue(*VectorParam.Key, VectorParam.Value);
            }

            // Update texture parameters
            for (const auto& TextureParam : InstanceData.TextureParameters)
            {
                if (TextureParam.Value.IsValid())
                {
                    UTexture* Texture = TextureParam.Value.LoadSynchronous();
                    if (Texture)
                    {
                        DynamicMaterial->SetTextureParameterValue(*TextureParam.Key, Texture);
                    }
                }
            }

            InstanceData.bNeedsUpdate = false;
            InstanceData.LastUpdateTime = FDateTime::Now();
            UpdatedInstances++;
        }
    }
}

void UAuracronFoliageMaterialVariationManager::UpdateColorVariationsInternal(float DeltaTime)
{
    FScopeLock Lock(&MaterialVariationLock);

    for (auto& ColorPair : ColorVariations)
    {
        FAuracronColorVariationData& ColorData = ColorPair.Value;

        if (ColorData.bIsActive)
        {
            // Apply color variation to associated material instances
            for (auto& InstancePair : MaterialInstances)
            {
                FAuracronMaterialInstanceData& InstanceData = InstancePair.Value;

                if (InstanceData.ColorVariation.FoliageTypeId == ColorData.FoliageTypeId)
                {
                    FLinearColor VariedColor = ApplyColorVariation(ColorData.BaseColor, ColorData);

                    // Update the material instance with the varied color
                    if (InstanceData.DynamicMaterial.IsValid())
                    {
                        UMaterialInstanceDynamic* DynamicMaterial = InstanceData.DynamicMaterial.Get();
                        DynamicMaterial->SetVectorParameterValue(TEXT("BaseColor"), VariedColor);

                        OnColorVariationApplied.Broadcast(ColorData.VariationId, VariedColor);
                    }
                }
            }
        }
    }
}

void UAuracronFoliageMaterialVariationManager::UpdateTextureBlendingInternal(float DeltaTime)
{
    FScopeLock Lock(&MaterialVariationLock);

    for (auto& BlendingPair : TextureBlending)
    {
        FAuracronTextureBlendingData& BlendingData = BlendingPair.Value;

        if (BlendingData.bIsActive)
        {
            // Apply texture blending to associated material instances
            for (auto& InstancePair : MaterialInstances)
            {
                FAuracronMaterialInstanceData& InstanceData = InstancePair.Value;

                if (InstanceData.TextureBlending.FoliageTypeId == BlendingData.FoliageTypeId)
                {
                    // Update blending parameters in material instance
                    if (InstanceData.DynamicMaterial.IsValid())
                    {
                        UMaterialInstanceDynamic* DynamicMaterial = InstanceData.DynamicMaterial.Get();

                        // Set blending opacity
                        DynamicMaterial->SetScalarParameterValue(TEXT("BlendOpacity"), BlendingData.BlendOpacity);

                        // Set texture scale and offset
                        DynamicMaterial->SetVectorParameterValue(TEXT("TextureScale"),
                            FLinearColor(BlendingData.TextureScale.X, BlendingData.TextureScale.Y, 0.0f, 0.0f));
                        DynamicMaterial->SetVectorParameterValue(TEXT("TextureOffset"),
                            FLinearColor(BlendingData.TextureOffset.X, BlendingData.TextureOffset.Y, 0.0f, 0.0f));

                        // Set texture rotation
                        DynamicMaterial->SetScalarParameterValue(TEXT("TextureRotation"), BlendingData.TextureRotation);

                        // Load and set blend texture
                        if (BlendingData.BlendTexture.IsValid())
                        {
                            UTexture2D* BlendTexture = BlendingData.BlendTexture.LoadSynchronous();
                            if (BlendTexture)
                            {
                                DynamicMaterial->SetTextureParameterValue(TEXT("BlendTexture"), BlendTexture);
                                OnTextureBlendingCompleted.Broadcast(BlendingData.BlendingId, BlendTexture);
                            }
                        }
                    }
                }
            }
        }
    }
}

void UAuracronFoliageMaterialVariationManager::UpdatePerformanceDataInternal()
{
    FScopeLock Lock(&MaterialVariationLock);

    // Reset counters
    PerformanceData.TotalMaterialInstances = MaterialInstances.Num();
    PerformanceData.ActiveMaterialInstances = 0;
    PerformanceData.CachedMaterialInstances = MaterialCache.Num();
    PerformanceData.ColorVariations = ColorVariations.Num();
    PerformanceData.TextureBlends = TextureBlending.Num();

    // Count active material instances
    for (const auto& InstancePair : MaterialInstances)
    {
        if (InstancePair.Value.bIsGenerated && InstancePair.Value.DynamicMaterial.IsValid())
        {
            PerformanceData.ActiveMaterialInstances++;
        }
    }

    // Calculate real memory usage using UE5.6 memory tracking
    PerformanceData.MemoryUsageMB = CalculateRealMaterialVariationMemoryUsage();
}

// Implementation of missing functions

float UAuracronFoliageMaterialVariationManager::CalculateRealMaterialVariationMemoryUsage()
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateMetrics);

    float TotalMemoryMB = 0.0f;

    // Calculate memory used by material instances
    TotalMemoryMB += MaterialInstances.GetAllocatedSize() / (1024.0f * 1024.0f);
    for (const auto& InstancePair : MaterialInstances)
    {
        if (InstancePair.Value.IsValid())
        {
            UMaterialInstanceDynamic* MaterialInstance = InstancePair.Value.Get();
            if (MaterialInstance)
            {
                // Estimate memory usage of material instance
                TotalMemoryMB += sizeof(UMaterialInstanceDynamic) / (1024.0f * 1024.0f);

                // Add texture memory if available
                TArray<UTexture*> ReferencedTextures;
                MaterialInstance->GetUsedTextures(ReferencedTextures, EMaterialQualityLevel::Num, true, ERHIFeatureLevel::Num, true);
                for (UTexture* Texture : ReferencedTextures)
                {
                    if (Texture)
                    {
                        TotalMemoryMB += Texture->CalcTextureMemorySizeEnum(TMC_AllMips) / (1024.0f * 1024.0f);
                    }
                }
            }
        }
    }

    // Calculate memory used by color variations
    TotalMemoryMB += ColorVariations.GetAllocatedSize() / (1024.0f * 1024.0f);
    TotalMemoryMB += ColorVariations.Num() * sizeof(FAuracronColorVariationData) / (1024.0f * 1024.0f);

    // Calculate memory used by texture blending
    TotalMemoryMB += TextureBlendingData.GetAllocatedSize() / (1024.0f * 1024.0f);
    TotalMemoryMB += TextureBlendingData.Num() * sizeof(FAuracronTextureBlendingData) / (1024.0f * 1024.0f);

    // Calculate memory used by seasonal variations
    TotalMemoryMB += SeasonalVariations.GetAllocatedSize() / (1024.0f * 1024.0f);
    TotalMemoryMB += SeasonalVariations.Num() * sizeof(FAuracronSeasonalVariationData) / (1024.0f * 1024.0f);

    return TotalMemoryMB;
}
