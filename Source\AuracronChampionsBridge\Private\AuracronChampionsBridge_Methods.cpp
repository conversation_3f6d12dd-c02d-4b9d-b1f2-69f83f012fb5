// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Campeões Bridge Implementation (Métodos Adicionais)

#include "AuracronChampionsBridge.h"
#include "Engine/Engine.h"
#include "GameplayAbilities/Public/AbilitySystemComponent.h"
#include "GameplayAbilities/Public/GameplayAbilitySpec.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "Animation/AnimInstance.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputMappingContext.h"
#include "InputAction.h"

// === Ability Management (Continuação) ===

bool UAuracronChampionsBridge::CancelActiveAbility()
{
    if (!bSystemInitialized || !AbilitySystemComponent)
    {
        return false;
    }

    // Cancelar todas as habilidades ativas
    AbilitySystemComponent->CancelAllAbilities();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Habilidades ativas canceladas"));
    return true;
}

bool UAuracronChampionsBridge::LevelUpAbility(const FString& AbilitySlot)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado"));
        return false;
    }

    // Find configuration by iterating through array
    FAuracronChampionConfiguration* Config = nullptr;
    for (auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == SelectedChampionID)
        {
            Config = &Entry.Configuration;
            break;
        }
    }
    if (!Config)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração do campeão não encontrada"));
        return false;
    }

    // Verificar se há pontos de habilidade disponíveis
    if (Config->Abilities.AvailableAbilityPoints <= 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Nenhum ponto de habilidade disponível"));
        return false;
    }

    // Verificar nível atual da habilidade
    int32 CurrentLevel = Config->Abilities.AbilityLevels.Contains(AbilitySlot) ? 
                         Config->Abilities.AbilityLevels[AbilitySlot] : 0;

    int32 MaxLevel = (AbilitySlot == TEXT("R")) ? 3 : 5; // Ultimate tem 3 níveis, outras têm 5
    
    if (CurrentLevel >= MaxLevel)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Habilidade já está no nível máximo: %s"), *AbilitySlot);
        return false;
    }

    // Upar habilidade
    Config->Abilities.AbilityLevels.Add(AbilitySlot, CurrentLevel + 1);
    Config->Abilities.AvailableAbilityPoints--;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Habilidade %s upada para nível %d"), *AbilitySlot, CurrentLevel + 1);

    return true;
}

float UAuracronChampionsBridge::GetAbilityCooldown(const FString& AbilitySlot) const
{
    // Find configuration by iterating through array
    const FAuracronChampionConfiguration* Config = nullptr;
    for (const auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == SelectedChampionID)
        {
            Config = &Entry.Configuration;
            break;
        }
    }
    if (!Config)
    {
        return 0.0f;
    }

    if (Config->Abilities.AbilityCooldowns.Contains(AbilitySlot))
    {
        return Config->Abilities.AbilityCooldowns[AbilitySlot];
    }

    return 0.0f;
}

bool UAuracronChampionsBridge::IsAbilityAvailable(const FString& AbilitySlot) const
{
    if (!AbilitySystemComponent)
    {
        return false;
    }

    // Verificar se a habilidade não está em cooldown
    float Cooldown = GetAbilityCooldown(AbilitySlot);
    if (Cooldown > 0.0f)
    {
        return false;
    }

    // Verificar se o campeão está em estado que permite usar habilidades
    if (CurrentChampionState == EAuracronChampionState::Stunned ||
        CurrentChampionState == EAuracronChampionState::Dead ||
        CurrentChampionState == EAuracronChampionState::Respawning)
    {
        return false;
    }

    return true;
}

int32 UAuracronChampionsBridge::GetAbilityLevel(const FString& AbilitySlot) const
{
    // Find configuration by iterating through array
    const FAuracronChampionConfiguration* Config = nullptr;
    for (const auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == SelectedChampionID)
        {
            Config = &Entry.Configuration;
            break;
        }
    }
    if (!Config)
    {
        return 0;
    }

    if (Config->Abilities.AbilityLevels.Contains(AbilitySlot))
    {
        return Config->Abilities.AbilityLevels[AbilitySlot];
    }

    return 0;
}

// === Attribute Management ===

bool UAuracronChampionsBridge::ApplyDamage(float DamageAmount, bool bIsMagicDamage)
{
    if (!IsChampionAlive() || DamageAmount <= 0.0f)
    {
        return false;
    }

    // Calcular redução de dano
    float Resistance = bIsMagicDamage ? CurrentAttributes.MagicResistance : CurrentAttributes.Armor;
    float DamageReduction = Resistance / (Resistance + 100.0f);
    float FinalDamage = DamageAmount * (1.0f - DamageReduction);

    // Aplicar dano
    CurrentAttributes.CurrentHealth = FMath::Max(0.0f, CurrentAttributes.CurrentHealth - FinalDamage);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Dano aplicado: %.2f (%.2f após resistência)"), DamageAmount, FinalDamage);

    // Verificar se morreu
    if (CurrentAttributes.CurrentHealth <= 0.0f)
    {
        CurrentChampionState = EAuracronChampionState::Dead;
        OnChampionDied.Broadcast(SelectedChampionID);
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Campeão morreu: %s"), *SelectedChampionID);
    }

    // Broadcast atualização de atributos
    OnAttributesUpdated.Broadcast(SelectedChampionID, CurrentAttributes);

    return true;
}

bool UAuracronChampionsBridge::HealChampion(float HealAmount)
{
    if (!IsChampionAlive() || HealAmount <= 0.0f)
    {
        return false;
    }

    CurrentAttributes.CurrentHealth = FMath::Min(CurrentAttributes.MaxHealth, CurrentAttributes.CurrentHealth + HealAmount);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Cura aplicada: %.2f"), HealAmount);

    // Broadcast atualização de atributos
    OnAttributesUpdated.Broadcast(SelectedChampionID, CurrentAttributes);

    return true;
}

bool UAuracronChampionsBridge::RestoreMana(float ManaAmount)
{
    if (!IsChampionAlive() || ManaAmount <= 0.0f)
    {
        return false;
    }

    CurrentAttributes.CurrentMana = FMath::Min(CurrentAttributes.MaxMana, CurrentAttributes.CurrentMana + ManaAmount);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Mana restaurada: %.2f"), ManaAmount);

    // Broadcast atualização de atributos
    OnAttributesUpdated.Broadcast(SelectedChampionID, CurrentAttributes);

    return true;
}

float UAuracronChampionsBridge::GetHealthPercentage() const
{
    if (CurrentAttributes.MaxHealth <= 0.0f)
    {
        return 0.0f;
    }

    return CurrentAttributes.CurrentHealth / CurrentAttributes.MaxHealth;
}

float UAuracronChampionsBridge::GetManaPercentage() const
{
    if (CurrentAttributes.MaxMana <= 0.0f)
    {
        return 0.0f;
    }

    return CurrentAttributes.CurrentMana / CurrentAttributes.MaxMana;
}

bool UAuracronChampionsBridge::ModifyAttribute(const FString& AttributeName, float ModifierValue, float Duration)
{
    if (!AbilitySystemComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AbilitySystemComponent não encontrado para modificar atributo: %s"), *AttributeName);
        return false;
    }

    // Create a GameplayEffect to modify the attribute
    UGameplayEffect* AttributeModifierEffect = NewObject<UGameplayEffect>(this);
    if (!AttributeModifierEffect)
    {
        UE_LOG(LogTemp, Error, TEXT("Falha ao criar GameplayEffect para modificar atributo: %s"), *AttributeName);
        return false;
    }

    // Configure the GameplayEffect
    AttributeModifierEffect->DurationPolicy = Duration > 0.0f ? EGameplayEffectDurationType::HasDuration : EGameplayEffectDurationType::Instant;
    AttributeModifierEffect->DurationMagnitude = FScalableFloat(Duration);
    
    // Create modifier for the specific attribute
    FGameplayModifierInfo ModifierInfo;
    ModifierInfo.ModifierMagnitude = FScalableFloat(ModifierValue);
    ModifierInfo.ModifierOp = ModifierValue >= 0.0f ? EGameplayModOp::Additive : EGameplayModOp::Additive;
    
    // Map attribute name to gameplay attribute
    FGameplayAttribute TargetAttribute;
    if (AttributeName == "Health")
    {
        // Use a custom attribute set or create one if needed
        // For now, we'll handle it through direct modification with proper networking
        float NewHealth = FMath::Clamp(
            CurrentAttributes.CurrentHealth + ModifierValue,
            0.0f,
            CurrentAttributes.MaxHealth
        );
        
        if (GetOwner()->HasAuthority())
        {
            CurrentAttributes.CurrentHealth = NewHealth;
            MARK_PROPERTY_DIRTY_FROM_NAME(UAuracronChampionsBridge, CurrentAttributes, this);
        }
    }
    else if (AttributeName == "Mana")
    {
        float NewMana = FMath::Clamp(
            CurrentAttributes.CurrentMana + ModifierValue,
            0.0f,
            CurrentAttributes.MaxMana
        );
        
        if (GetOwner()->HasAuthority())
        {
            CurrentAttributes.CurrentMana = NewMana;
            MARK_PROPERTY_DIRTY_FROM_NAME(UAuracronChampionsBridge, CurrentAttributes, this);
        }
    }
    else if (AttributeName == "AttackDamage")
    {
        float NewAttackDamage = FMath::Max(0.0f, CurrentAttributes.AttackDamage + ModifierValue);
        
        if (GetOwner()->HasAuthority())
        {
            CurrentAttributes.AttackDamage = NewAttackDamage;
            MARK_PROPERTY_DIRTY_FROM_NAME(UAuracronChampionsBridge, CurrentAttributes, this);
        }
    }
    else if (AttributeName == "MovementSpeed")
    {
        float NewMovementSpeed = FMath::Max(100.0f, CurrentAttributes.MovementSpeed + ModifierValue);
        
        if (GetOwner()->HasAuthority())
        {
            CurrentAttributes.MovementSpeed = NewMovementSpeed;
            MARK_PROPERTY_DIRTY_FROM_NAME(UAuracronChampionsBridge, CurrentAttributes, this);
            
            // Update movement component if available
            if (MovementComponent)
            {
                MovementComponent->MaxWalkSpeed = NewMovementSpeed;
            }
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("Atributo não reconhecido: %s"), *AttributeName);
        return false;
    }

    // Apply the GameplayEffect for duration-based modifications
    if (Duration > 0.0f)
    {
        FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
        EffectContext.AddSourceObject(this);
        
        FGameplayEffectSpecHandle EffectSpecHandle = AbilitySystemComponent->MakeOutgoingSpec(
            AttributeModifierEffect->GetClass(),
            1.0f, // Level
            EffectContext
        );
        
        if (EffectSpecHandle.IsValid())
        {
            FActiveGameplayEffectHandle ActiveEffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(
                *EffectSpecHandle.Data.Get()
            );
            
            if (ActiveEffectHandle.IsValid())
            {
                ActiveEffects.Add(ActiveEffectHandle);
                
                // Set up timer to revert the change when effect expires
                FTimerHandle RevertTimer;
                GetWorld()->GetTimerManager().SetTimer(
                    RevertTimer,
                    [this, AttributeName, ModifierValue]()
                    {
                        // Revert the attribute change
                        ModifyAttribute(AttributeName, -ModifierValue, 0.0f);
                    },
                    Duration,
                    false
                );
            }
        }
    }

    // Broadcast attribute update
    if (GetOwner()->HasAuthority())
    {
        OnAttributesUpdated.Broadcast(SelectedChampionID, CurrentAttributes);
    }
    
    UE_LOG(LogTemp, Log, TEXT("Atributo %s modificado em %f por %f segundos usando GameplayEffect"), *AttributeName, ModifierValue, Duration);
    return true;
}
