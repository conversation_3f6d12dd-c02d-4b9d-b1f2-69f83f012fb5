#pragma once

#include "CoreMinimal.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Array.h"
#include "Containers/Queue.h"
#include "Misc/DateTime.h"
#include "Logging/LogMacros.h"

#include "AuracronErrorHandling.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronErrorHandling, Log, All);

// Enums for error handling
UENUM(BlueprintType)
enum class EErrorSeverity : uint8
{
    Info            UMETA(DisplayName = "Info"),
    Warning         UMETA(DisplayName = "Warning"),
    Error           UMETA(DisplayName = "Error"),
    Critical        UMETA(DisplayName = "Critical"),
    Fatal           UMETA(DisplayName = "Fatal")
};

UENUM(BlueprintType)
enum class EErrorCategory : uint8
{
    General         UMETA(DisplayName = "General"),
    DNA             UMETA(DisplayName = "DNA"),
    Mesh            UMETA(DisplayName = "Mesh"),
    Animation       UMETA(DisplayName = "Animation"),
    Texture         UMETA(DisplayName = "Texture"),
    Hair            UMETA(DisplayName = "Hair"),
    Clothing        UMETA(DisplayName = "Clothing"),
    Physics         UMETA(DisplayName = "Physics"),
    Performance     UMETA(DisplayName = "Performance"),
    Memory          UMETA(DisplayName = "Memory"),
    IO              UMETA(DisplayName = "Input/Output"),
    Network         UMETA(DisplayName = "Network"),
    Validation      UMETA(DisplayName = "Validation")
};

UENUM(BlueprintType)
enum class EDNAValidationType : uint8
{
    Basic           UMETA(DisplayName = "Basic"),
    Comprehensive   UMETA(DisplayName = "Comprehensive"),
    Integrity       UMETA(DisplayName = "Integrity"),
    Performance     UMETA(DisplayName = "Performance"),
    Compatibility   UMETA(DisplayName = "Compatibility")
};

UENUM(BlueprintType)
enum class ERecoveryAction : uint8
{
    None            UMETA(DisplayName = "None"),
    Retry           UMETA(DisplayName = "Retry"),
    Reset           UMETA(DisplayName = "Reset"),
    Reload          UMETA(DisplayName = "Reload"),
    Fallback        UMETA(DisplayName = "Fallback"),
    Abort           UMETA(DisplayName = "Abort")
};

// Structures for error handling
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FErrorInfo
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Info")
    int32 ErrorCode = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Info")
    EErrorSeverity Severity = EErrorSeverity::Error;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Info")
    EErrorCategory Category = EErrorCategory::General;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Info")
    FString Message;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Info")
    FString SourceLocation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Info")
    FDateTime Timestamp;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Info")
    FString StackTrace;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Info")
    TMap<FString, FString> AdditionalData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Info")
    FString Context;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Info")
    bool bShowNotification = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Info")
    bool bGenerateCrashDump = false;

    FErrorInfo()
    {
        Timestamp = FDateTime::Now();
    }
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FDNAValidationResult
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA Validation")
    bool bIsValid = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA Validation")
    TArray<FErrorInfo> Errors;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA Validation")
    TArray<FErrorInfo> Warnings;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA Validation")
    float ValidationScore = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA Validation")
    FString ValidationSummary;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA Validation")
    int32 CorruptedDataBlocks = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA Validation")
    int32 MissingDataBlocks = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA Validation")
    bool bCanBeRepaired = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA Validation")
    TArray<FString> RepairSuggestions;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FErrorStatistics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Statistics")
    int32 TotalErrors = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Statistics")
    int32 TotalWarnings = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Statistics")
    int32 TotalCriticalErrors = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Statistics")
    FDateTime LastErrorTime;

    FErrorStatistics()
    {
        LastErrorTime = FDateTime::MinValue();
    }
};

UENUM(BlueprintType)
enum class EErrorRecoveryAction : uint8
{
    None            UMETA(DisplayName = "None"),
    Retry           UMETA(DisplayName = "Retry"),
    Fallback        UMETA(DisplayName = "Fallback"),
    Reset           UMETA(DisplayName = "Reset"),
    Ignore          UMETA(DisplayName = "Ignore"),
    Abort           UMETA(DisplayName = "Abort")
};

/**
 * Error handling and validation system for MetaHuman Bridge
 * Provides comprehensive error management and DNA validation with UE5.6 APIs
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronErrorHandling
{
public:
    FAuracronErrorHandling();
    ~FAuracronErrorHandling();

    // Core error handling
    void RecordError(const FErrorInfo& ErrorInfo);
    FErrorInfo CreateErrorInfo(int32 ErrorCode, EErrorSeverity Severity, EErrorCategory Category,
                              const FString& Message, const FString& SourceLocation = TEXT(""));
    FErrorInfo GetLastError() const;
    TArray<FErrorInfo> GetErrorHistory() const;
    void ClearErrorHistory();

    // Error recovery
    bool IsErrorRecoverable(const FErrorInfo& ErrorInfo);
    ERecoveryAction GetDefaultRecoveryAction(const FErrorInfo& ErrorInfo);
    bool AttemptErrorRecovery(const FErrorInfo& ErrorInfo);

    // DNA validation
    FDNAValidationResult ValidateDNAFile(const FString& FilePath, EDNAValidationType ValidationType);
    bool DetectDNACorruption();
    bool RepairCorruptedDNA(bool bCreateBackup = true);
    FString CreateDNABackup(const FString& BackupName = TEXT(""));
    bool RestoreDNAFromBackup(const FString& BackupPath);

    // System validation
    bool ValidateSystemConfiguration();
    TArray<FString> GetSystemValidationErrors() const;
    TArray<FString> GetSystemValidationWarnings() const;

    // Error handling configuration
    void SetErrorHandlingMode(bool bEnableAutoRecovery, bool bEnableDetailedLogging);
    bool IsAutoRecoveryEnabled() const;
    bool IsDetailedLoggingEnabled() const;

    // Statistics and reporting
    TMap<FString, FString> GetErrorHandlingStats();
    void GenerateErrorReport(const FString& ReportPath);
    void ExportErrorLog(const FString& LogPath);
    FErrorStatistics GetErrorStatistics() const;

    // System management
    void InitializeErrorHandlingSystem();
    void CleanupErrorHandlingSystem();

    // Error handling methods
    void ReportError(EErrorSeverity Severity, const FString& Category, const FString& Message,
                    const FString& Context = TEXT(""), bool bShowNotification = false, bool bGenerateCrashDump = false);
    bool HandleError(const FString& ErrorCode, const FString& ErrorMessage, EErrorRecoveryAction RecoveryAction);
    void SetErrorHandlingEnabled(bool bEnabled);
    void SetAutoRecoveryEnabled(bool bEnabled);
    void SetMaxRetryAttempts(int32 MaxAttempts);
    bool ValidateSystemState();

    // Thread safety
    mutable FCriticalSection ErrorHandlingMutex;

private:
    // Error tracking
    FErrorInfo CurrentError;
    TArray<FErrorInfo> ErrorHistory;
    int32 MaxErrorHistorySize;

    // Configuration
    FThreadSafeBool bAutoRecoveryEnabled;
    FThreadSafeBool bDetailedLoggingEnabled;
    FThreadSafeBool bErrorHandlingEnabled;
    int32 MaxRetryAttempts;
    bool bInBatchOperation;

    // Statistics
    mutable TMap<EErrorCategory, int32> ErrorCounts;
    mutable TMap<EErrorSeverity, int32> SeverityCounts;
    mutable int32 TotalErrors;
    mutable int32 RecoveredErrors;

    // Recovery system
    TMap<FString, EErrorRecoveryAction> RecoveryStrategies;
    TMap<FString, int32> RetryAttempts;

    // Internal methods
    void LogErrorInfo(const FErrorInfo& ErrorInfo);
    void UpdateErrorStatistics(const FErrorInfo& ErrorInfo);
    bool PerformDNAIntegrityCheck(const FString& FilePath);
    bool PerformDNAStructureValidation(const FString& FilePath);
    bool PerformDNADataValidation(const FString& FilePath);
    FString GenerateStackTrace() const;

    // Error handling internal methods
    void InitializeErrorHandling();
    void ShutdownErrorHandling();
    void InitializeErrorRecovery();
    void ShutdownErrorRecovery();
    void SetupErrorCallbacks();
    FString GetCurrentStackTrace();
    void AttemptAutoRecovery(const FErrorInfo& ErrorEntry);
    bool AttemptRetry(const FString& ErrorCode, const FString& ErrorMessage);
    bool AttemptFallback(const FString& ErrorCode, const FString& ErrorMessage);
    bool AttemptReset(const FString& ErrorCode, const FString& ErrorMessage);

    // Prevent copying
    FAuracronErrorHandling(const FAuracronErrorHandling&) = delete;
    FAuracronErrorHandling& operator=(const FAuracronErrorHandling&) = delete;
};
