// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Progressão Bridge Implementation

#include "AuracronProgressionBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "OnlineSubsystem.h"
#include "OnlineSubsystemUtils.h"
#include "Interfaces/OnlineAchievementsInterface.h"
#include "Interfaces/OnlineLeaderboardInterface.h"
#include "Interfaces/OnlineStatsInterface.h"
#include "Interfaces/OnlineUserCloudInterface.h"
#include "Json.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "HttpModule.h"
#include "Interfaces/IHttpRequest.h"
#include "Interfaces/IHttpResponse.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Engine/ActorChannel.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "Kismet/GameplayStatics.h"
#include "DrawDebugHelpers.h"

UAuracronProgressionBridge::UAuracronProgressionBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 5.0f; // 0.2 FPS para progressão (não precisa ser frequente)
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Configurações padrão de progressão de conta
    CurrentAccountProgression.AccountLevel = 1;
    CurrentAccountProgression.CurrentExperience = 0;
    CurrentAccountProgression.TotalExperience = 0;
    CurrentAccountProgression.ExperienceToNextLevel = 1000;
    CurrentAccountProgression.CurrentGold = 0;
    CurrentAccountProgression.PremiumCurrency = 0;
    CurrentAccountProgression.BlueEssence = 0;
    CurrentAccountProgression.OrangeEssence = 0;
    CurrentAccountProgression.MatchesPlayed = 0;
    CurrentAccountProgression.MatchesWon = 0;
    CurrentAccountProgression.WinRate = 0.0f;
    CurrentAccountProgression.TotalPlayTimeMinutes = 0;
    CurrentAccountProgression.AccountCreationDate = FDateTime::Now();
    CurrentAccountProgression.LastOnlineDate = FDateTime::Now();
    CurrentAccountProgression.CurrentWinStreak = 0;
    CurrentAccountProgression.BestWinStreak = 0;
    CurrentAccountProgression.HonorLevel = 2;
    CurrentAccountProgression.HonorPoints = 0;
    CurrentAccountProgression.bHasActiveBattlePass = false;
    CurrentAccountProgression.BattlePassLevel = 0;
    CurrentAccountProgression.BattlePassXP = 0;
}

void UAuracronProgressionBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Progressão"));

    // Inicializar sistema
    bSystemInitialized = InitializeProgressionSystem();
    
    if (bSystemInitialized)
    {
        // Configurar timers para sincronização automática
        GetWorld()->GetTimerManager().SetTimer(
            AutoSyncTimer,
            [this]()
            {
                SyncWithFirebase();
            },
            300.0f, // A cada 5 minutos
            true
        );

        GetWorld()->GetTimerManager().SetTimer(
            MilestoneCheckTimer,
            [this]()
            {
                CheckAutomaticMilestones();
            },
            10.0f, // A cada 10 segundos
            true
        );
        
        // Carregar progressão da nuvem
        LoadProgressionFromCloud();
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Progressão inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Progressão"));
    }
}

void UAuracronProgressionBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(AutoSyncTimer);
        GetWorld()->GetTimerManager().ClearTimer(MilestoneCheckTimer);
    }
    
    // Salvar progressão antes de sair
    if (bSystemInitialized)
    {
        SaveProgressionToCloud();
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronProgressionBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronProgressionBridge, CurrentAccountProgression);
    DOREPLIFETIME(UAuracronProgressionBridge, ChampionMasteries);
    DOREPLIFETIME(UAuracronProgressionBridge, RealmMasteries);
    DOREPLIFETIME(UAuracronProgressionBridge, PendingRewards);
}

void UAuracronProgressionBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar progressão automática
    ProcessAutomaticProgression(DeltaTime);
    
    // Verificar marcos automaticamente
    CheckAutomaticMilestones();
}

// === Account Progression ===

bool UAuracronProgressionBridge::GainAccountExperience(int32 ExperienceAmount)
{
    if (!bSystemInitialized || ExperienceAmount <= 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado ou experiência inválida"));
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    CurrentAccountProgression.CurrentExperience += ExperienceAmount;
    CurrentAccountProgression.TotalExperience += ExperienceAmount;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Experiência ganha: %d (Total: %lld)"), ExperienceAmount, CurrentAccountProgression.TotalExperience);

    // Verificar se pode subir de nível
    while (CurrentAccountProgression.CurrentExperience >= CurrentAccountProgression.ExperienceToNextLevel && 
           CurrentAccountProgression.AccountLevel < 500)
    {
        LevelUpAccount();
    }

    return true;
}

bool UAuracronProgressionBridge::LevelUpAccount()
{
    if (CurrentAccountProgression.AccountLevel >= 500)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Conta já está no nível máximo"));
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    int32 OldLevel = CurrentAccountProgression.AccountLevel;
    
    // Subir nível
    CurrentAccountProgression.AccountLevel++;
    CurrentAccountProgression.CurrentExperience -= CurrentAccountProgression.ExperienceToNextLevel;
    
    // Calcular nova experiência necessária (fórmula exponencial)
    CurrentAccountProgression.ExperienceToNextLevel = CalculateExperienceForLevel(CurrentAccountProgression.AccountLevel + 1);

    // Conceder recompensas de nível
    FAuracronReward LevelReward;
    LevelReward.RewardID = FString::Printf(TEXT("AccountLevel_%d"), CurrentAccountProgression.AccountLevel);
    LevelReward.RewardName = FText::FromString(FString::Printf(TEXT("Level %d Reward"), CurrentAccountProgression.AccountLevel));
    LevelReward.RewardType = EAuracronRewardType::Gold;
    LevelReward.Quantity = CurrentAccountProgression.AccountLevel * 100; // 100 gold por nível
    LevelReward.RewardRarity = EAuracronRewardRarity::Common;

    GrantReward(LevelReward);

    // Conceder Blue Essence
    CurrentAccountProgression.BlueEssence += CurrentAccountProgression.AccountLevel * 50;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Conta subiu para nível %d"), CurrentAccountProgression.AccountLevel);

    // Broadcast evento
    OnAccountLevelUp.Broadcast(OldLevel, CurrentAccountProgression.AccountLevel);

    return true;
}

bool UAuracronProgressionBridge::AddGold(int32 GoldAmount)
{
    if (!bSystemInitialized || GoldAmount <= 0)
    {
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    CurrentAccountProgression.CurrentGold += GoldAmount;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Gold adicionado: %d (Total: %d)"), GoldAmount, CurrentAccountProgression.CurrentGold);

    return true;
}

bool UAuracronProgressionBridge::RemoveGold(int32 GoldAmount)
{
    if (!bSystemInitialized || GoldAmount <= 0)
    {
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    if (CurrentAccountProgression.CurrentGold < GoldAmount)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Gold insuficiente: %d < %d"), CurrentAccountProgression.CurrentGold, GoldAmount);
        return false;
    }

    CurrentAccountProgression.CurrentGold -= GoldAmount;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Gold removido: %d (Restante: %d)"), GoldAmount, CurrentAccountProgression.CurrentGold);

    return true;
}

bool UAuracronProgressionBridge::AddPremiumCurrency(int32 Amount)
{
    if (!bSystemInitialized || Amount <= 0)
    {
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    CurrentAccountProgression.PremiumCurrency += Amount;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Moeda premium adicionada: %d (Total: %d)"), Amount, CurrentAccountProgression.PremiumCurrency);

    return true;
}

int64 UAuracronProgressionBridge::CalculateExperienceForLevel(int32 Level) const
{
    if (Level <= 1)
    {
        return 1000;
    }

    // Fórmula exponencial para experiência: Base * (Level^1.5) * Multiplier
    float BaseXP = 1000.0f;
    float LevelMultiplier = FMath::Pow(Level, 1.5f);
    float ScalingFactor = 1.2f;

    return FMath::RoundToInt(BaseXP * LevelMultiplier * ScalingFactor);
}

// === Champion Mastery ===

bool UAuracronProgressionBridge::GainChampionMasteryPoints(const FString& ChampionID, int32 Points)
{
    if (!bSystemInitialized || ChampionID.IsEmpty() || Points <= 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Parâmetros inválidos para maestria de campeão"));
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    // Obter ou criar maestria do campeão
    FAuracronChampionMastery* Mastery = ChampionMasteries.Find(ChampionID);
    if (!Mastery)
    {
        FAuracronChampionMastery NewMastery;
        NewMastery.ChampionID = ChampionID;
        NewMastery.MasteryLevel = 0;
        NewMastery.MasteryPoints = 0;
        NewMastery.PointsToNextLevel = 1800; // Pontos necessários para nível 1
        NewMastery.MatchesPlayed = 0;
        NewMastery.MatchesWon = 0;
        NewMastery.ChampionWinRate = 0.0f;
        NewMastery.AverageKDA = 0.0f;
        NewMastery.BestKDA = 0.0f;
        NewMastery.TotalPlayTime = 0;
        NewMastery.LastPlayed = FDateTime::Now();
        NewMastery.bHasMasteryToken = false;
        NewMastery.MasteryTokens = 0;

        ChampionMasteries.Add(ChampionID, NewMastery);
        Mastery = &ChampionMasteries[ChampionID];
    }

    Mastery->MasteryPoints += Points;
    Mastery->LastPlayed = FDateTime::Now();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Pontos de maestria ganhos para %s: %d (Total: %d)"), *ChampionID, Points, Mastery->MasteryPoints);

    // Verificar se pode subir de nível
    while (Mastery->MasteryPoints >= Mastery->PointsToNextLevel && Mastery->MasteryLevel < 10)
    {
        LevelUpChampionMastery(ChampionID);
    }

    return true;
}

FAuracronChampionMastery UAuracronProgressionBridge::GetChampionMastery(const FString& ChampionID) const
{
    const FAuracronChampionMastery* Mastery = ChampionMasteries.Find(ChampionID);
    if (Mastery)
    {
        return *Mastery;
    }

    // Retornar maestria vazia se não encontrada
    FAuracronChampionMastery EmptyMastery;
    EmptyMastery.ChampionID = ChampionID;
    return EmptyMastery;
}

TArray<FAuracronChampionMastery> UAuracronProgressionBridge::GetAllChampionMasteries() const
{
    TArray<FAuracronChampionMastery> AllMasteries;

    for (const auto& MasteryPair : ChampionMasteries)
    {
        AllMasteries.Add(MasteryPair.Value);
    }

    // Ordenar por nível de maestria (decrescente)
    AllMasteries.Sort([](const FAuracronChampionMastery& A, const FAuracronChampionMastery& B)
    {
        if (A.MasteryLevel != B.MasteryLevel)
        {
            return A.MasteryLevel > B.MasteryLevel;
        }
        return A.MasteryPoints > B.MasteryPoints;
    });

    return AllMasteries;
}

bool UAuracronProgressionBridge::LevelUpChampionMastery(const FString& ChampionID)
{
    FAuracronChampionMastery* Mastery = ChampionMasteries.Find(ChampionID);
    if (!Mastery || Mastery->MasteryLevel >= 10)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Maestria não encontrada ou já no nível máximo: %s"), *ChampionID);
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    int32 OldLevel = Mastery->MasteryLevel;

    // Subir nível
    Mastery->MasteryLevel++;
    Mastery->MasteryPoints -= Mastery->PointsToNextLevel;

    // Calcular pontos necessários para próximo nível
    int32 BasePoints = 1800;
    float LevelMultiplier = FMath::Pow(Mastery->MasteryLevel + 1, 1.3f);
    Mastery->PointsToNextLevel = FMath::RoundToInt(BasePoints * LevelMultiplier);

    // Conceder recompensas de maestria
    FAuracronReward MasteryReward;
    MasteryReward.RewardID = FString::Printf(TEXT("ChampionMastery_%s_%d"), *ChampionID, Mastery->MasteryLevel);
    MasteryReward.RewardName = FText::FromString(FString::Printf(TEXT("Mastery %d Reward"), Mastery->MasteryLevel));
    MasteryReward.RewardType = EAuracronRewardType::BlueEssence;
    MasteryReward.Quantity = Mastery->MasteryLevel * 500; // 500 BE por nível
    MasteryReward.RewardRarity = EAuracronRewardRarity::Uncommon;

    GrantReward(MasteryReward);

    // Conceder token de maestria em níveis específicos
    if (Mastery->MasteryLevel == 5 || Mastery->MasteryLevel == 7 || Mastery->MasteryLevel == 10)
    {
        Mastery->MasteryTokens++;
        Mastery->bHasMasteryToken = true;

        FAuracronReward TokenReward;
        TokenReward.RewardID = FString::Printf(TEXT("MasteryToken_%s_%d"), *ChampionID, Mastery->MasteryLevel);
        TokenReward.RewardName = FText::FromString(TEXT("Mastery Token"));
        TokenReward.RewardType = EAuracronRewardType::Essence;
        TokenReward.Quantity = 1;
        TokenReward.RewardRarity = EAuracronRewardRarity::Epic;

        GrantReward(TokenReward);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Maestria de %s subiu para nível %d"), *ChampionID, Mastery->MasteryLevel);

    // Broadcast evento
    OnChampionMasteryLevelUp.Broadcast(ChampionID, OldLevel, Mastery->MasteryLevel);

    return true;
}

// === Realm Mastery ===

bool UAuracronProgressionBridge::GainRealmMasteryPoints(int32 RealmType, int32 Points)
{
    if (!bSystemInitialized || RealmType < 0 || RealmType > 2 || Points <= 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Parâmetros inválidos para maestria de realm"));
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    // Obter ou criar maestria do realm
    FAuracronRealmMastery* Mastery = RealmMasteries.Find(RealmType);
    if (!Mastery)
    {
        FAuracronRealmMastery NewMastery;
        NewMastery.RealmType = RealmType;
        NewMastery.MasteryLevel = 0;
        NewMastery.MasteryPoints = 0;
        NewMastery.TimeSpentInRealm = 0;
        NewMastery.ObjectivesCompleted = 0;
        NewMastery.KillsInRealm = 0;
        NewMastery.DeathsInRealm = 0;
        NewMastery.AssistsInRealm = 0;
        NewMastery.ExplorationPercentage = 0.0f;

        RealmMasteries.Add(RealmType, NewMastery);
        Mastery = &RealmMasteries[RealmType];
    }

    Mastery->MasteryPoints += Points;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Pontos de maestria de realm %d ganhos: %d (Total: %d)"), RealmType, Points, Mastery->MasteryPoints);

    // Verificar se pode subir de nível (lógica similar à maestria de campeão)
    int32 PointsNeeded = (Mastery->MasteryLevel + 1) * 2000; // 2000 pontos por nível
    if (Mastery->MasteryPoints >= PointsNeeded && Mastery->MasteryLevel < 10)
    {
        Mastery->MasteryLevel++;
        Mastery->MasteryPoints -= PointsNeeded;

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Maestria de realm %d subiu para nível %d"), RealmType, Mastery->MasteryLevel);
    }

    return true;
}
